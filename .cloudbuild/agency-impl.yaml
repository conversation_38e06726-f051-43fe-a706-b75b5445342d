steps:
  - id: 'restore cache'
    name: 'eu.gcr.io/$PROJECT_ID/restore_cache'
    args:
      - '--bucket=gs://cloudbuild_sbt_cache_1'
      - '--key=build-cache-$( checksum build.sbt )'
    waitFor: ['-']

  - id: 'run'
    name: 'eu.gcr.io/$PROJECT_ID/cloudbuild-sbt'
    args: ['run', '-p=agency-impl']
    secretEnv: ['GH_USER', 'GH_TOKEN']
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=europe-west4-a'
      - 'CLOUDSDK_CONTAINER_CLUSTER=cluster-1'
    waitFor: ['restore cache']

  - id: 'save cache'
    name: 'eu.gcr.io/$PROJECT_ID/save_cache'
    args:
      - --bucket=gs://cloudbuild_sbt_cache_1
      - --key=build-cache-$( checksum build.sbt )
      - --path=/workspace/.ivy2/cache
      - --no-clobber
    waitFor: ['run']

  - id: 'cleanup'
    name: 'eu.gcr.io/$PROJECT_ID/cloudbuild-cleanup'
    args: [ 'agency-impl' ]
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=europe-west4-a'
      - 'CLOUDSDK_CONTAINER_CLUSTER=cluster-1'
    waitFor: [ 'run' ]
availableSecrets:
  secretManager:
  - versionName: projects/1069518649540/secrets/gh-user/versions/1
    env: GH_USER
  - versionName: projects/1069518649540/secrets/gh-access-token/versions/1
    env: GH_TOKEN
timeout: '1600s'
