package com.phoenix.portfolio.impl.agencyportfolio

import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag, AggregateEventTagger}
import play.api.libs.json.{Format, Json}

sealed trait AgencyPortfoliosEvent extends AggregateEvent[AgencyPortfoliosEvent] {
  override def aggregateTag: AggregateEventTagger[AgencyPortfoliosEvent] = AgencyPortfoliosEvent.Tag
}

object AgencyPortfoliosEvent {
  val Tag: AggregateEventTag[AgencyPortfoliosEvent] = AggregateEventTag[AgencyPortfoliosEvent]
}

case class AgencyAdded_v1(leaseIdentifierPrefix: String, leaseIdentifierStartsAt: Int) extends AgencyPortfoliosEvent
case object AgencyAdded_v1 {
  implicit val format: Format[AgencyAdded_v1] = Json.format
}

/**
 * event is no longer used however it is stored in the database so must be included to not break serialization
 */
case class AgencyPortfolioAssigned_v1(user: String) extends AgencyPortfoliosEvent
case object AgencyPortfolioAssigned_v1 {
  implicit val format: Format[AgencyPortfolioAssigned_v1] = Json.format
}

case class AgencyPortfolioDeleted_v1(user: String, portfolioId: String) extends AgencyPortfoliosEvent
case object AgencyPortfolioDeleted_v1 {
  implicit val format: Format[AgencyPortfolioDeleted_v1] = Json.format
}

case class LeaseIdentifierGenerated_v1(portfolioId: String, identifier: String) extends AgencyPortfoliosEvent
case object LeaseIdentifierGenerated_v1 {
  implicit val format: Format[LeaseIdentifierGenerated_v1] = Json.format
}

case class UserShared_v1(user: String, agency: String, sharedWithUser: String) extends AgencyPortfoliosEvent
case object UserShared_v1 {
  implicit val format: Format[UserShared_v1] = Json.format
}

case class UserShareRevoked_v1(user: String, agency: String, revokedUser: String) extends AgencyPortfoliosEvent
case object UserShareRevoked_v1 {
  implicit val format: Format[UserShareRevoked_v1] = Json.format
}
