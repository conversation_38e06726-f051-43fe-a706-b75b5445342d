package com.phoenix.portfolio.impl

import akka.actor.ActorSystem
import akka.persistence.query.Offset
import akka.stream.Materializer
import akka.stream.scaladsl.{Flow, Sink, Source}
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.{BadRequest, ExceptionMessage, Forbidden, NotFound, RequestHeader, ResponseHeader, TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.projection.Projections
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import com.phoenix.agency.api.AgencyService
import com.phoenix.agency.api.response.Agency
import com.phoenix.authentication.jwt._
import com.phoenix.date.DateUtcUtil
import com.phoenix.google.api.Address.{BuildingName, Component_v1, UnitNumber}
import com.phoenix.google.api.Details.Geometry
import com.phoenix.google.api.{Address, Autocomplete, Details, PlaceService}
import com.phoenix.lightstone.api.LightstoneService
import com.phoenix.lightstone.api.Request.PropertyPostRequest
import com.phoenix.logging.LoggedServerServiceCall.{logged, loggedServiceCall}
import com.phoenix.persistence.EventRecord
import com.phoenix.portfolio.api
import com.phoenix.portfolio.api.ApplicationFee.ApplicationStatus
import com.phoenix.portfolio.impl.applicationfee.{AcceptApplication, ApplicationFeeEntity, ApplicationFeeEventsGraph, CreateApplication, DeleteApplication, GetApplicationFee, RejectApplication, UpdateApplication}
import com.phoenix.portfolio.api._
import com.phoenix.portfolio.api.property.request.{PropertyCreation, PropertyList, PropertyUpdate}
import com.phoenix.portfolio.api.property.response._
import com.phoenix.portfolio.api.property.{LeaseAssociatedWithProperty, PropertyCreated_v1, PropertyDestroyed_v1, PropertySummary_v1, Property_v1, PropertyEvent => propEvent}
import com.phoenix.portfolio.api.request._
import com.phoenix.portfolio.api.response._
import com.phoenix.portfolio.impl.agencyportfolio._
import com.phoenix.portfolio.impl.connectors._
import com.phoenix.portfolio.impl.csv.BulkImportCsvTemplate
import com.phoenix.portfolio.impl.portfolio.{PortfolioEntity, PortfolioEvent, _}
import com.phoenix.portfolio.impl.property._
import com.phoenix.projections.lib.ProjectionEndpointsImpl
import com.phoenix.validation.ValidationUtil._
import com.phoenix.validation.{ValidationError, ValidationException}
import com.phoenix.wallet.api.{ReleaseDeposit, WalletService}
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.DateTime

import java.util.UUID
import com.phoenix.util.{UUID => pUUID}
import play.api.Application

import scala.concurrent.impl.Promise
import scala.concurrent.{ExecutionContext, Future, Promise}
import scala.language.postfixOps
import scala.util.{Failure, Success, Try}

// scalastyle:off
class PortfolioServiceImpl(
                            persistentEntityRegistry: PersistentEntityRegistry,
                            system: ActorSystem,
                            portfolioSummaryConnector: PortfolioSummaryConnector,
                            portfolioConnector: PortfolioConnector,
                            applicationFeeConnector: ApplicationFeeConnector,
                            pgApplicationConnector: PgApplicationConnector,
                            pgPortfolioStatusConnector: PgPortfolioStatusConnector,
                            successfulCommandConnector: SuccessfulCommandConnector,
                            partyConnector: PartyConnector,
                            propertyConnector: PropertyConnector,
                            propertySearchImpl: PropertySearchImpl,
                            pgPropertyConnector: PgPropertyConnector,
                            actorMaterializer: Materializer,
                            googlePlaceService: PlaceService,
                            lightstoneService: LightstoneService,
                            agencyService: AgencyService,
                            val auth: AuthenticationService,
                            walletService: WalletService,
                            bulkImportCsvTemplate: BulkImportCsvTemplate,
                            val projectionsImpl: Projections,
                            agencyConnector: AgencyConnector
                          )
                          (implicit val executionContext: ExecutionContext)
  extends PortfolioService with LazyLogging with ProjectionEndpointsImpl {

  implicit val ac: ActorSystem = system
  implicit val am: Materializer = actorMaterializer

  private def refFor(id: String) = persistentEntityRegistry.refFor[PortfolioEntity](id)

  private def authorize(userSession: UserSession, portfolioId: Option[String] = None): Future[UserSession] = {
    userSession.agencyMembership match {
      case Some(_) if portfolioId.isEmpty => Future(userSession)
      case None => throw Forbidden("Agency not selected")
      case Some(membership) =>
        val agencyIdResult = refFor(portfolioId.get).ask(GetPortfolio).map(_.map(_.agencyId)).flatMap {
          case None => portfolioConnector.findPortfolioAgency(UUID.fromString(portfolioId.get)).map(_.map(_.agencyId))
          case result => Future(result)
        }
        agencyIdResult.map {
          case Some(agencyId) if agencyId == UUID.fromString(membership.agencyId) => userSession
          case None => throw NotFound("Lease not found")
          case _ => throw Forbidden("Agency does not have access to lease")
        }
    }
  }

  override def openPortfolio: ServerServiceCall[OpenPortfolioFields, PortfolioId] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { request =>
          authorize(authorization).flatMap({ authority =>
            if (request.propertyId.isDefined && request.property.isDefined) throw BadRequest("You can only include a propertyId or property object not both")
            validateLeaseType(request)
            val agencyId = UUID.fromString(authority.agencyMembership.map(_.agencyId).get)
            val userId = UUID.fromString(authority.userId)
            val leaseId = UUID.randomUUID()
            val landlord = request.landlordPartyId.map { partyId =>
              val landlordId = UUID.randomUUID()
              LeaseOwner(landlordId.toString, partyId.toString)
            }
            for {
              propertyId <- request.property match {
                case Some(creation) => processPropertyCreation(creation, agencyId, userId, Some(leaseId)).map(_.id)
                case None => persistentEntityRegistry.refFor[PropertyEntity](request.propertyId.get.toString).ask(AssociatePropertyLease(Some(userId), agencyId, leaseId))
                  .map(_ => request.propertyId.get)
              }
              segments = authority.agencyMembership.flatMap(_.segment).flatMap(_.currentSegments.headOption)
                .map(s => Seq(s))
                .getOrElse(Seq.empty[UUID])
              leaseIdentifier <- persistentEntityRegistry.refFor[AgencyPortfoliosEntity](agencyId.toString).ask(GenerateLeaseId(leaseId))
              command = OpenPortfolio(agencyId, userId.toString, propertyId.toString, segments, landlord, request.leaseType, leaseIdentifier, request.metaData)
              portfolio <- refFor(leaseId.toString).ask(command)
                  .flatMap(_ => refFor(leaseId.toString).ask(GetPortfolio))
              portfolioAgency = PortfolioAgency(leaseId, propertyId, agencyId)
              _ <- portfolioConnector.updateFromEntity(portfolioAgency, portfolio.get.portfolio)
              _ <- portfolioConnector.insertPortfolioAgency(portfolioAgency)
            } yield PortfolioId(leaseId.toString)
          })
        }
      }
    }

  override def openPortfolioV2: ServerServiceCall[OpenPortfolioFields, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { request =>
          authorize(authorization).flatMap({ authority =>
            validate(request)
            if (request.propertyId.isDefined && request.property.isDefined) throw BadRequest("You can only include a propertyId or property object not both")
            validateLeaseType(request)
            val agencyId = UUID.fromString(authority.agencyMembership.map(_.agencyId).get)
            val userId = UUID.fromString(authority.userId)
            val leaseId = UUID.randomUUID()
            val landlord = request.landlordPartyId.map { partyId =>
              val landlordId = UUID.randomUUID()
              LeaseOwner(landlordId.toString, partyId.toString)
            }
            for {
              propertyId <- request.property match {
                case Some(creation) => processPropertyCreation(creation, agencyId, userId, Some(leaseId)).map(_.id)
                case None => persistentEntityRegistry.refFor[PropertyEntity](request.propertyId.get.toString).ask(AssociatePropertyLease(Some(userId), agencyId, leaseId))
                  .map(_ => request.propertyId.get)
              }
              segments = authority.agencyMembership.flatMap(_.segment).flatMap(_.currentSegments.headOption)
                .map(s => Seq(s))
                .getOrElse(Seq.empty[UUID])
              leaseIdentifier <- persistentEntityRegistry.refFor[AgencyPortfoliosEntity](agencyId.toString).ask(GenerateLeaseId(leaseId))
              command = OpenPortfolio(agencyId, userId.toString, propertyId.toString, segments, landlord,
                request.leaseType, leaseIdentifier, request.metaData)
              portfolio <- refFor(leaseId.toString).ask(command)
                .flatMap(_ => refFor(leaseId.toString).ask(GetPortfolio))
              portfolioAgency = PortfolioAgency(leaseId, propertyId, agencyId)
              _ <- portfolioConnector.updateFromEntity(portfolioAgency, portfolio.get.portfolio)
              _ <- portfolioConnector.insertPortfolioAgency(portfolioAgency)
            } yield portfolio.get.portfolio
          })
        }
      }
    }

  private def validateLeaseType(request: OpenPortfolioFields): Unit =
    if (!(request.leaseType == ManagedContract_v1.NAME) && !(request.leaseType == UnmanagedContract_v1.NAME)) {
      throw validationFailedForFields(Map("leaseType" -> s"LeaseType is not valid. Lease Type must be ${ManagedContract_v1.NAME} or ${UnmanagedContract_v1.NAME}."))
    }

  private def partyBankDetailsExist(party: PartyRow): Boolean = {
    party.accountName.nonEmpty &&
    party.accountNumber.nonEmpty &&
    party.accountType.nonEmpty &&
    party.bank.nonEmpty
  }

  private def updateContract(id: String, authority: UserSession, contract: ContractContainer) = {
    refFor(id).ask(AmendContract(authority.userId, contract))
      .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
  }

  private def updateContractV2(id: String, authority: UserSession, contract: ContractContainer) = {
    for {
      globallyVatEnabled <- agencyConnector.isGlobalVatEnabled(UUID.fromString(authority.agencyMembership.get.agencyId))
      portfolio <- refFor(id).ask(AmendContract_v2(authority.userId, contract, globallyVatEnabled))
        .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
    } yield portfolio
  }

  override def amendContract(id: String): ServerServiceCall[ContractContainer, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { contract =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(GetPortfolio).flatMap {
                case Some(PortfolioReply(portfolio, agencyId)) =>
                  contract match {
                    case ContractContainer(c: UnmanagedContract_v1) if c.terms.depositHeldByLandlord =>
                      val partyId = portfolio.parties.owners.primary.map(_.partyId).getOrElse(throw NotFound("PartyId not found."))
                      partyConnector.find(agencyId, partyId).flatMap({
                        case Some(x) if partyBankDetailsExist(x) => updateContract(id, authority, contract)
                        case _ => throw ValidationException.wrapException("contract amendment failed", Set(ValidationError(key = "general", message = "Owner bank details not set")))
                      })
                    case _ => updateContract(id, authority, contract)
                  }
                case _ => refFor(id).ask(GetPortfolio).map(_.get.portfolio)
              }
          })
        }
      }
    }

  override def amendContractV2(id: String): ServerServiceCall[ContractContainer, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { contract =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              for {
                portfolio <- refFor(id).ask(GetPortfolio).flatMap {
                  case Some(PortfolioReply(portfolio, agencyId)) =>
                    contract match {
                      case ContractContainer(c: UnmanagedContract_v1) if c.terms.depositHeldByLandlord =>
                        val partyId = portfolio.parties.owners.primary.map(_.partyId).getOrElse(throw NotFound("PartyId not found."))
                        partyConnector.find(agencyId, partyId).flatMap{
                          case Some(x) if partyBankDetailsExist(x) => updateContractV2(id, authority, contract)
                          case _ => throw ValidationException.wrapException("contract amendment failed", Set(ValidationError(key = "general", message = "Owner bank details not set")))
                        }
                      case _ => updateContractV2(id, authority, contract)
                    }
                  case None => throw NotFound("Lease not found")
                }
              } yield (portfolio)

          })
        }
      }
    }

  override def amendProperty(id: String): ServerServiceCall[PortfolioPropertyFields, Done] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { request =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              if (request.propertyId.isDefined && request.property.isDefined) throw BadRequest("You can only include a propertyId or property object not both")
              val agencyId = UUID.fromString(authority.agencyMembership.map(_.agencyId).get)
              val userId = UUID.fromString(authority.userId)
              val leaseId = UUID.fromString(id)
              for {
                propertyId <- request.property match {
                  case Some(creation) => processPropertyCreation(creation, agencyId, userId, Some(leaseId)).map(_.id.toString)
                  case None => persistentEntityRegistry.refFor[PropertyEntity](request.propertyId.get).ask(AssociatePropertyLease(Some(userId), agencyId, leaseId))
                    .map(_ => request.propertyId.get)
                }
                reply <- refFor(id).ask(AmendProperty(authority.userId, propertyId, request.partyId)).flatMap {
                  case PropertyAmendedReply(newPropertyId, Some(oldPropertyId)) if oldPropertyId != newPropertyId =>
                    persistentEntityRegistry.refFor[PropertyEntity](oldPropertyId.toString).ask(DisassociatePropertyLease(Some(userId), agencyId, leaseId))
                  case _ => Future(Done)
                }
              } yield reply
          })
        }
      }
    }

  override def amendPropertyV2(id: String): ServerServiceCall[PortfolioPropertyFields, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { request =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              if (request.propertyId.isDefined && request.property.isDefined) throw BadRequest("You can only include a propertyId or property object not both")
              val agencyId = UUID.fromString(authority.agencyMembership.map(_.agencyId).get)
              val userId = UUID.fromString(authority.userId)
              val leaseId = UUID.fromString(id)
              for {
                //We create a new prop
                propertyId <- request.property match {
                  case Some(creation) => processPropertyCreation(creation, agencyId, userId, Some(leaseId)).map(_.id.toString)
                  case None => persistentEntityRegistry.refFor[PropertyEntity](request.propertyId.get).ask(AssociatePropertyLease(Some(userId), agencyId, leaseId))
                    .map(_ => request.propertyId.get)
                }
                reply <- refFor(id).ask(AmendProperty(authority.userId, propertyId, request.partyId)).flatMap {
                  case PropertyAmendedReply(newPropertyId, Some(oldPropertyId)) if oldPropertyId != newPropertyId =>
                    persistentEntityRegistry.refFor[PortfolioEntity](id).ask(GetPortfolio).map(_.get.portfolio)
                  case _ => persistentEntityRegistry.refFor[PortfolioEntity](id).ask(GetPortfolio).map(_.get.portfolio)
                }
              } yield reply
          })
        }
      }
    }

  override def amendSettings(id: String): ServerServiceCall[SettingsFields, Done] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { settings =>
          authorize(authorization, Some(id)).flatMap({
            authority => for {
              _ <- refFor(id).ask(AmendSettings(authority.userId, Settings_v1(settings.notes, settings.customLeaseIdentifier, settings.autoSendPaymentReminderSms, settings.autoSendOwnerMonthlyEmail)))
              _ <- settings.segments match {
                case Some(segments) => refFor(id).ask(AmendSegments(segments, Some(UUID.fromString(authority.userId))))
                case None => Future(Done)
              }
            } yield Done
          })
        }
      }
    }

  override def amendSettingsV2(id: String): ServerServiceCall[SettingsFields, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { settings =>
          authorize(authorization, Some(id)).flatMap({
            authority => for {
              _ <- refFor(id).ask(AmendSettings(authority.userId, Settings_v1(settings.notes, settings.customLeaseIdentifier, settings.autoSendPaymentReminderSms, settings.autoSendOwnerMonthlyEmail)))
              _ <- settings.segments match {
                case Some(segments) => refFor(id).ask(AmendSegments(segments, Some(UUID.fromString(authority.userId))))
                case None => Future(Done)
              }
              portfolio <- refFor(id).ask(GetPortfolio).map(_.get.portfolio)
            } yield portfolio
          })
        }
      }
    }

  override def amendMetaData(id: String): ServerServiceCall[PortfolioMetaData, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { body =>
          authorize(authorization, Some(id)).flatMap({
            authority => for {
              _ <- refFor(id).ask(AmendMetaData(authority.userId, Some(body.data)))
              portfolio <- refFor(id).ask(GetPortfolio).map(_.get.portfolio)
            } yield portfolio
          })
        }
      }
    }

  override def amendSegments(id: String): ServerServiceCall[SegmentFields, Done] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { request =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(AmendSegments(request.segments, Some(UUID.fromString(authority.userId))))
          })
        }
      }
    }

  override def amendSegmentsV2(id: String): ServerServiceCall[SegmentFields, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { request =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(AmendSegments(request.segments, Some(UUID.fromString(authority.userId))))
                .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
          })
        }
      }
    }

  override def amendCommission(id: String): ServerServiceCall[CommissionSplit_v1, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { request =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(AmendCommission(authority.userId, CommissionSplit_v1(request.managementFee, request.procurementFee)))
                .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
          })
        }
      }
    }

  override def amendCommissionV2(id: String): ServerServiceCall[CommissionSplit_v1, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { request =>
          validate(request)
          authorize(authorization, Some(id)).flatMap({
            authority =>
              for {
                globalVat <- agencyService.getAgency(UUID.fromString(authority.agencyMembership.get.agencyId))
                  .handleRequestHeader(auth.authenticatedHandler(authorization))
                  .invoke().map(x => x.globalVatEnabled)
                  .recover {
                    case e:Exception => e.printStackTrace()
                      throw NotFound("Agency not found from AgencyService")
                  }
                portfolio <- refFor(id)
                  .ask(AmendCommissionV2(authority.userId, CommissionSplit_v1(request.managementFee, request.procurementFee), globalVat))
                  .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
              } yield portfolio

          })
        }
      }
    }

  import com.phoenix.portfolio.api.PortfolioStatusLabel.{Expired, Deleted, Terminated}

  private def getPortfolios(agencyUUID: UUID) = {
    portfolioConnector.find(agencyUUID)
      .runWith(Sink.seq)
      .map {
        results => results.map(PortfolioTable.toPortfolio)
      }
  }

  override def revertVATForAgency(agencyId: String): ServerServiceCall[NotUsed, Done] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { req =>
        authorize(authorization).flatMap { authority =>
          implicit val agencyIduuid = UUID.fromString(agencyId)
          implicit val a = authority
          implicit val today = DateTime.now()
          val cutoffDate = DateTime.parse("2025-04-10T23:59:59Z")//Very important date the day that the mistakes were made to commission
          for {
            agency <- agencyService.getAgency(agencyIduuid).handleRequestHeader(auth.authenticatedHandler(authorization)).invoke()
            oldValues <- successfulCommandConnector.getHistoricalEventsByAgency(agency.id, cutoffDate, "AmendCommissionV2")
            skipList <- successfulCommandConnector.getSuccessfulRevertedCommandsByName(agency.id, "AmendCommissionV2")
            portfolios <- getPortfolios(agencyIduuid)
            _ = logger.info(s"Old values for agency $agencyId: size=${oldValues.size}")
            _ = logger.info(s"Skip list for agency $agencyId: size=${skipList.size}")
            _ = logger.info(s"Portfolios for agency $agencyId: size=${portfolios.size}")
            revertCommands <- Future.sequence(portfolios.filterNot(p => skipList.map(_.leaseId.toString).contains(p.id)).map { portfolio =>
              implicit val ag = agency
              implicit val p = portfolio
              val status = PortfolioStatus.getStatusFromPortfolio(portfolio)
              if (!agency.globalVatEnabled) {
                Future(Option.empty[RevertCommand])
              } else if (status == Deleted || status == Expired || status == Terminated) {
                Future(Option.empty[RevertCommand])
              } else {
                logger.info("Reverting Vat for agency " + agencyId + " portfolio " + portfolio.id)
                updateCommissionUsingOldValue(oldValues, portfolio) match {
                  case Some(value) =>
                    refFor(portfolio.id).ask(AmendCommissionV2(authority.userId, value, agency.globalVatEnabled))
                      .map(_ => Some(buildRevertCommand()))
                      .recover {
                        case e: Exception =>
                          logger.info(s"UpdateVat for agency ${agencyId} failed for portfolio ${portfolio.id} with error: ${e.getMessage}")
                          Some(buildErrRevertCommand("Exception on event occurred: " + e.getMessage))
                      }
                  case _ =>
                    logger.info(s"Skipping portfolio ${portfolio.id} no data in old values")
                    Future(Option.empty[RevertCommand])
                }
              }
            })
            db <- successfulCommandConnector.batchInsertRevertedRows(revertCommands.flatten).recover{
              case e: Exception =>
                logger.error(s"Error inserting reverted commands for agency $agencyId: ${e.getMessage}")
            }
          } yield Done
        }
      }
    }
  }

  private def saveErrToDB(errMsg: String)(implicit authority: UserSession, today: DateTime, agency: Agency, portfolio: Portfolio_v1) = {
    saveToDB(Some(errMsg), false)
  }

  private def buildErrRevertCommand(errMsg: String)(implicit authority: UserSession, today: DateTime, agency: Agency, portfolio: Portfolio_v1) = {
    RevertCommand(agency.id, UUID.fromString(portfolio.id), "AmendCommissionV2", None, false, Some(errMsg), Some(authority.userId).map(UUID.fromString), Some(today.toString))
  }

  private def buildRevertCommand()(implicit authority: UserSession, today: DateTime, agency: Agency, portfolio: Portfolio_v1) = {
    RevertCommand(agency.id, UUID.fromString(portfolio.id), "AmendCommissionV2", None, true, None, Some(authority.userId).map(UUID.fromString), Some(today.toString))
  }

  private def saveToDB(errMsg: Option[String], success: Boolean)(implicit authority: UserSession, today: DateTime, agency: Agency, portfolio: Portfolio_v1) = {
    val rc = RevertCommand(agency.id, UUID.fromString(portfolio.id), "AmendCommissionV2", None, success, errMsg, Some(authority.userId).map(UUID.fromString), Some(today.toString))
    successfulCommandConnector.insertRevertedRow(rc)
  }

  private def updateCommissionUsingOldValue(oldValues: Seq[LatestCommandsResponse], portfolio: Portfolio_v1): Option[CommissionSplit_v1] = {
    oldValues.find(_.leaseId.toString == portfolio.id).flatMap {
      case LatestCommandsResponse(_, _, None, Some(commissionPerc)) =>
        Some(VatUpdateUtils.updateCommissionWithNewPercentage(portfolio.commission.get, commissionPerc.doubleValue, true))
      case LatestCommandsResponse(_, _, Some(comm), None) =>
        Some(VatUpdateUtils.updateCommissionWithNewAmount(portfolio.commission.get, comm.doubleValue, true))
      case _ =>
        logger.info(s"UpdateVat failed for portfolio ${portfolio.id}")
        Option.empty[CommissionSplit_v1]
    }
  }

  override def updateVATForAgency(agencyId: String): ServerServiceCall[VatUpdateFields, PortfolioList] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { vatUpdateFields =>
          authorize(authorization).flatMap { authority =>
            val oldVatRate = vatUpdateFields.oldVat
            val newVatRate = vatUpdateFields.newVat
            implicit val today = DateTime.now()
            for {
              agency <- agencyService.getAgency(UUID.fromString(authority.agencyMembership.get.agencyId))
                .handleRequestHeader(auth.authenticatedHandler(authorization)).invoke()
              //todo: consider skipping test agencies and agency where globalVatEnabled is false
              portfolios <- getPortfolios(UUID.fromString(agencyId))
              results <- Future.sequence(portfolios.map { portfolio =>
                val status = PortfolioStatus.getStatusFromPortfolio(portfolio)
                if (status == Deleted || status == Expired || status == Terminated) {
                  // Return skipped portfolio ID with no updates
                  Future.successful((None, Some(portfolio.id)))
                } else {
                  // Update commission with new VAT rate
                  val updatedCommission = portfolio.commission.map { commission =>
                    VatUpdateUtils.updateCommissionWithNewVat(commission, oldVatRate, newVatRate)
                  }

                  // Update invoice templates with new VAT rate
                  val updatedTemplates = portfolio.invoiceTemplates.map { template =>
                    VatUpdateUtils.updateInvoiceTemplateWithNewVat(template, oldVatRate, newVatRate)
                  }

                  // Sequence the updates: commission first, then templates
                  val commissionUpdates = updatedCommission.map { newCommission =>
                    refFor(portfolio.id).ask(AmendCommissionV2(authority.userId, newCommission, agency.globalVatEnabled))
                      .recover{
                        case _: Exception => logger.info(s"UpdateVat[AmendCommissionV2] for agency ${agencyId} failed for portfolio ${portfolio.id}")
                      }
                  }.toSeq

                  val templateUpdates = updatedTemplates.map { template =>
                    refFor(portfolio.id).ask(UpdateInvoiceTemplateV2(authority.userId, template))
                      .recover{
                      case _: Exception => logger.info(s"UpdateVat[UpdateInvoiceTemplateV2] failed for agency ${agencyId} failed for portfolio ${portfolio.id}")
                    }
                  }

                  // Ensure commission updates complete before template updates
                  Future.sequence(commissionUpdates).flatMap { _ =>
                    Future.sequence(templateUpdates)
                  }.map { _ =>
                    // Return updated portfolio ID with no skip
                    (Some(portfolio.id), None)
                  }
                }
              })
              _ = logger.info(s"UpdateVat for agency ${agencyId} completed with ${results.size} portfolios updated: ${results}")
              // Aggregate results into updated and skipped lists
              updatedPortfolios = results.collect { case (Some(id), _) => id }
              skippedPortfolios = results.collect { case (_, Some(id)) => id }
            } yield PortfolioList(updatedPortfolios, skippedPortfolios)
          }
        }
      }
    }

  override def updateVatAllAgencies()  = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { vatUpdateFields =>
        authorize(authorization).flatMap { authority =>
          for {
            allAgencies <- agencyService.agencyListing().handleRequestHeader(auth.authenticatedHandler(authorization)).invoke()
              .recover{
                case e: Exception =>
                  logger.error(s"Error fetching agencies for updateVatAllAgencies: ${e.getMessage}")
                  Seq.empty
              }
            filterOutTestAgencies = allAgencies.filter(_.isTestAgency.getOrElse(false) == false)
            filterOutDeletedAgencies = filterOutTestAgencies.filter(_.deactivatedDate.isEmpty)
            _ = logger.info(s"updateVatAllAgencies running on ${filterOutDeletedAgencies.size} agencies with filed out test agencies and filter out deleted agencies")
            portfolios <- Future.sequence(filterOutDeletedAgencies.map { agency =>
              logger.info(s"updateVatAllAgencies for agency ${agency.agencyId}")
              updateVATForAgency(agency.agencyId.toString)
                .handleRequestHeader(auth.authenticatedHandler(authorization))
                .invoke(vatUpdateFields)
                .recover{
                case e: Exception =>
                  logger.error(s"Error updating VAT for agency ${agency.agencyId}: ${e.getMessage}")
                  PortfolioList(Seq.empty, Seq.empty)
                }
            })
          } yield portfolios
        }
      }
    }
  }

  override def getPortfolio(id: String): ServerServiceCall[NotUsed, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { _ =>
          authorize(authorization, Some(id)).flatMap({
            authority => refFor(id).ask(GetPortfolio).map {
              case None => throw NotFound("Lease not found")
              case Some(PortfolioReply(portfolio, _)) => portfolio
            }
          })
        }
      }
    }

  override def getPortfolioInternal(id: String): ServiceCall[NotUsed, OptionalPortfolioResponse] =
    logged {
      ServerServiceCall { _ =>
        //TODO - Add internal authorization and cleanup
        refFor(id).ask(GetPortfolio).map {
          case None =>
            logger.info("Lease not found")
            OptionalPortfolioResponse(None)
          case Some(PortfolioReply(portfolio, _)) => OptionalPortfolioResponse(Some(portfolio))
        }
      }
    }

  override def addLeaseTerms(id: String): ServerServiceCall[LeaseTermsFields_v1, LeaseTermsFields_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { leaseTerms =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(AddLeaseTerms(authority.userId, LeaseTermsFields_v1(leaseTerms.startDate, leaseTerms.endDate, leaseTerms.rolloverMonthToMonth)))
                .map(_ => LeaseTermsFields_v1(leaseTerms.startDate, leaseTerms.endDate, leaseTerms.rolloverMonthToMonth))
          })
        }
      }
    }

  override def addLeaseTermsV2(id: String): ServerServiceCall[LeaseTermsFields_v1, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { leaseTerms =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(AddLeaseTerms(authority.userId, LeaseTermsFields_v1(leaseTerms.startDate, leaseTerms.endDate, leaseTerms.rolloverMonthToMonth)))
                .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
          })
        }
      }
    }

  override def invoiceSchedules(id: String): ServiceCall[NotUsed, InvoiceSchedules] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { _ =>
          authorize(authorization, Some(id)).flatMap({ authority =>
            refFor(id).ask(GetPortfolio).map {
              case Some(PortfolioReply(portfolio, agencyId)) =>
                portfolio.leaseTerms match {
                  case Some(lt) => {
                    val now = DateUtcUtil.now()
                    // Deduct half a month just to make sure we pick up the first months invoice
                    val start = new DateTime(lt.startDate).minusDays(15)
                    val startDate = if (now.isAfter(start)) now else start
                    InvoiceSchedules.calculate(startDate, new DateTime(lt.endDate), portfolio.invoiceTemplates)
                  }
                  case None     => InvoiceSchedules(Seq.empty)
                }
              case None => throw NotFound("Lease not found")
            }
          })
        }
      }
    }
  private def validateIntervalEqualsOnceOff(invoice: InvoiceTemplateFields): Unit = {
    if (!invoice.interval.equals("OnceOff")) throw ValidationException.wrapException("createInvoiceTemplate failed", Set(ValidationError(key="general", message="UnmanagedLease must have an interval set to OnceOff.")))
  }

  private def validateUnmanagedLease(invoice:InvoiceTemplateFields, portfolioId: String) = {
    for {
      _ <- refFor(portfolioId).ask(GetPortfolio).map {
        case None =>
          throw NotFound(s"Portfolio with id ${portfolioId} could not be found")
        case Some(p) => p.portfolio.tags("leaseType") match {
          case UnmanagedContract_v1.NAME => validateIntervalEqualsOnceOff(invoice)
          case _ => Done
        }
      }
    } yield(Done)
  }

  override def createInvoiceTemplate(id: String): ServiceCall[InvoiceTemplateFields, InvoiceTemplateId] =
    auth(Authorization.agency) {
      authorization =>
      logged {
        ServerServiceCall { invoice =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              validate(invoice)

              val invoiceId = UUID.randomUUID().toString

             for {
               _ <- refFor(id).ask(GetPortfolio).map {
                 case Some(x) =>
                   if (isDuplicate(invoice, x)) {
                     val err = TransportErrorCode(409, 0, "Conflict with current state")
                     val detail = new ExceptionMessage("TemplateExists",
                       "A template with the same category and InvoicePartyId already exists.")
                     throw TransportException.fromCodeAndMessage(err, detail)
                   }
                 case _ => None
               }

               _ <- validateUnmanagedLease(invoice, id)
               response <- refFor(id).ask(AddInvoiceTemplate(
                 authority.userId,
                 InvoiceTemplate_v1(
                   invoiceId,
                   invoice.category,
                   invoice.invoicePartyId,
                   invoice.invoicePartyAccountType,
                   invoice.interval,
                   invoice.netAmount,
                   invoice.vatable,
                   invoice.autoDeliver,
                   invoice.paymentRules,
                   invoice.description
                 )
               )).map(_ => InvoiceTemplateId(invoiceId))
             } yield response
          })
        }
      }
    }

  override def createInvoiceTemplateV2(id: String): ServiceCall[InvoiceTemplateFields, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { invoice =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              validate(invoice)
              val invoiceId = UUID.randomUUID().toString

              for {
                _ <- refFor(id).ask(GetPortfolio).map {
                  case Some(x) =>
                    if (isDuplicate(invoice, x)) {
                      val err = TransportErrorCode(409, 0, "Conflict with current state")
                      val detail = new ExceptionMessage("TemplateExists",
                        "A template with the same category and InvoicePartyId already exists.")
                      throw TransportException.fromCodeAndMessage(err, detail)
                    }
                  case _ => None
                }

                _ <- validateUnmanagedLease(invoice, id)
                response <- refFor(id).ask(AddInvoiceTemplate(
                  authority.userId,
                  InvoiceTemplate_v1(
                    invoiceId,
                    invoice.category,
                    invoice.invoicePartyId,
                    invoice.invoicePartyAccountType,
                    invoice.interval,
                    invoice.netAmount,
                    invoice.vatable,
                    invoice.autoDeliver,
                    invoice.paymentRules,
                    invoice.description
                  )
                )).flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
              } yield response
          })
        }
      }
    }

  private def isDuplicate(invoiceTemplate: InvoiceTemplateFields, portfolio: PortfolioReply): Boolean = {
    val invoiceWithSameCategory = portfolio.portfolio.invoiceTemplates.find(_.category == invoiceTemplate.category)
    val hasSamePartyID = invoiceWithSameCategory.exists(_.invoicePartyId == invoiceTemplate.invoicePartyId)
    val hasSameInterval = invoiceWithSameCategory.exists(_.interval == invoiceTemplate.interval)
    hasSamePartyID && hasSameInterval
  }

  override def updateInvoiceTemplate(id: String, invoiceId: String): ServerServiceCall[InvoiceTemplateFields, Done] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { invoice =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              validate(invoice)

              for {
                _ <- validateUnmanagedLease(invoice, id)

                response <- refFor(id).ask(UpdateInvoiceTemplate(
                authority.userId,
                InvoiceTemplate_v1(
                invoiceId,
                invoice.category,
                invoice.invoicePartyId,
                invoice.invoicePartyAccountType,
                invoice.interval,
                invoice.netAmount,
                invoice.vatable,
                invoice.autoDeliver,
                invoice.paymentRules,
                invoice.description
                )
                ))
              } yield response

          })
        }
      }
    }

  override def updateInvoiceTemplateV2(id: String, invoiceId: String): ServerServiceCall[InvoiceTemplateFields, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { invoice =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              validate(invoice)

              for {
                _ <- validateUnmanagedLease(invoice, id)

                response <- refFor(id).ask(UpdateInvoiceTemplateV2(
                  authority.userId,
                  InvoiceTemplate_v1(
                    invoiceId,
                    invoice.category,
                    invoice.invoicePartyId,
                    invoice.invoicePartyAccountType,
                    invoice.interval,
                    invoice.netAmount,
                    invoice.vatable,
                    invoice.autoDeliver,
                    invoice.paymentRules,
                    invoice.description
                  )
                )).flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
              } yield response

          })
        }
      }
    }

  override def removeInvoiceTemplate(id: String, invoiceId: String):ServerServiceCall[NotUsed, Done] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { _ =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(RemoveInvoiceTemplate(authority.userId, invoiceId))
          })
        }
      }
    }

  override def removeInvoiceTemplateV2(id: String, invoiceId: String):ServerServiceCall[NotUsed, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { _ =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(RemoveInvoiceTemplate(authority.userId, invoiceId))
                .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
          })
        }
      }
    }

  override def addAgent(id: String): ServerServiceCall[AgentFields, Done] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { agentFields =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(AddAgent(authority.userId, agentFields.agent))
          })
        }
      }
    }

  override def removeAgent(id: String, person_id: String): ServerServiceCall[NotUsed, Done] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { _ =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(RemoveAgent(authority.userId, person_id))
          })
        }
      }
    }

  override def requestApproval(id: String): ServerServiceCall[ApprovalFields, Done] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { approvalFields =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              val user = approvalFields.user
              val userEmail = approvalFields.userEmail
              val notifyEmail = approvalFields.notificationEmail
              refFor(id).ask(RequestApproval(authority.agencyMembership.get.agencyId, authority.userId, user, userEmail, notifyEmail))
          })
        }
      }
    }

  override def approve(id: String): ServerServiceCall[ApprovalFields, Done] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { approvalFields =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              val user = approvalFields.user
              val userEmail = approvalFields.userEmail
              val notifyEmail = approvalFields.notificationEmail

              refFor(id).ask(ApprovePortfolio(authority.agencyMembership.get.agencyId, authority.userId, user, userEmail, notifyEmail))
          })
        }
      }
    }

  override def approveV2(id: String): ServerServiceCall[ApprovalFields, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { approvalFields =>
          authorize(authorization, Some(id)).flatMap({
            authority =>

              for {
                tenant <- refFor(id).ask(GetPortfolio).map(_.get.portfolio.parties.tenants.primary)
                applicants <- applicationFeeConnector.getApplicationFees(id)
                pendingApplicants = applicants.filter(_.status.isEmpty).map(_.partyId)

                _ = if (tenant.nonEmpty && pendingApplicants.contains(tenant.get.partyId)) {
                  throw validationFailedForFields(Map("parties.tenants.primary.partyId" -> "Accept or delete the primary tenant application you can approve this lease."))
                }

                user = approvalFields.user
                userEmail = approvalFields.userEmail
                notifyEmail = approvalFields.notificationEmail

                _ <- refFor(id).ask(ApprovePortfolio(authority.agencyMembership.get.agencyId, authority.userId, user, userEmail, notifyEmail))
                result <- refFor(id).ask(GetPortfolio).map(_.get.portfolio)
              } yield result
          })
        }
      }
    }

  override def decline(id: String): ServerServiceCall[DeclineFields, Done] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { fields =>
          authorize(authorization, Some(id)).flatMap({
            authority =>
              refFor(id).ask(DeclinePortfolio(authority.agencyMembership.get.agencyId, authority.userId, fields.reason))
          })
        }
      }
    }

  override def getPortfolios: ServiceCall[NotUsed, Seq[PropertyPortfoliosRow]] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization).flatMap({ authority =>
          val agencyId = UUID.fromString(authority.agencyMembership.get.agencyId)
          val segments = authority.agencyMembership.get.segment.map(_.currentSegments)
          implicit val currDate: DateTime = DateUtcUtil.now()
          propertySearchImpl.allWithLease(agencyId, segments)
            .runWith(Sink.seq)
            .map {
              results => results.map { result =>
                import result.propertyRow._
                PropertyPortfoliosRow(propertyId, mainText, secondaryText, result.leases.map(PortfolioTable.toPortfolio))
              }
            }
        })
      }
    }
  }

  override def getPortfoliosByAgency(agencyId: String): ServiceCall[NotUsed, Seq[Portfolio_v1]] = auth(Authorization.supportStaff) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization).flatMap({ _ =>
          val agencyUUID = UUID.fromString(agencyId)
          portfolioConnector.find(agencyUUID)
            .runWith(Sink.seq)
            .map {
              results => results.map(PortfolioTable.toPortfolio)
            }
        }
        )
      }
    }
  }

  override def getPortfoliosByAgencyInternal(agencyId: String): ServiceCall[NotUsed, Seq[Portfolio_v1]] =
    logged {
      ServerServiceCall { _ =>
        //TODO - Add internal authorization and cleanup
        val agencyUUID = UUID.fromString(agencyId)
        portfolioConnector.find(agencyUUID)
          .runWith(Sink.seq)
          .map {
            results => results.map(PortfolioTable.toPortfolio)
          }
      }
    }

  override def leaseStatus: ServiceCall[Seq[pUUID], Seq[PortfoliosStatusRow]] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { req =>
        authorize(authorization).flatMap({ authority =>
          val agencyId = UUID.fromString(authority.agencyMembership.get.agencyId)
          portfolioSummaryConnector.find(agencyId)
            .runWith(Sink.seq)
            .map {
              results =>
                results.filter(x => req.seq.contains(pUUID.fromUUID(x.portfolioId))).map { summary =>
                  implicit val currentDate: DateTime = DateUtcUtil.now()
                  val status = Some(PortfolioStatus.getStatusFromPortfolio(summary))
                  PortfoliosStatusRow(summary.portfolioId, status.map(x => x.toString))
                }
            }
        })
      }
    }
  }

  /**
   * @todo Investigate why the propertySearchImpl wasn not used for this endpoint
   * @Deprecated use portfolioSummariesV2
   */
  override def portfolioSummaries: ServiceCall[NotUsed, Seq[PortfolioSummaryRow]] = auth(Authorization.agency) {
    authorized =>
      ServerServiceCall {
        _ => {
          val agencyId = UUID.fromString(authorized.agencyMembership.get.agencyId)
          implicit val currentDate: DateTime = DateUtcUtil.now()
          portfolioSummaryConnector
            .find(agencyId)
            .map(summary => {
              summary.copy(tags = summary.tags ++ Map("contract" -> summary.getContractStatus))
              summary.copy(status = Some(PortfolioStatus.getStatusFromPortfolio(summary)))
            })
            .runWith(Sink.seq)
        }
      }
  }
  //TODO refactor as the responses are getting bigger and bigger
  override def portfolioSummariesV2: ServiceCall[NotUsed, Seq[PortfolioWithApplications]] = auth(Authorization.agency) {
    authorized =>
      ServerServiceCall {
        _ => {
          val agencyId = UUID.fromString(authorized.agencyMembership.get.agencyId)
          implicit val currentDate: DateTime = DateUtcUtil.now()
          portfolioSummaryConnector.find(agencyId)
            .mapAsync(1) { summary =>
              val contractStatus = summary.getContractStatus
              val updatedSummary = summary.copy(
                tags = summary.tags ++ Map("contract" -> contractStatus),
                status = Some(PortfolioStatus.getStatusFromPortfolio(summary))
              )

              // Asynchronous call to Application connector
              applicationFeeConnector.getApplicationFees(updatedSummary.portfolioId).map { applications =>
                (updatedSummary, applications)
              }
            }.mapAsync(1){
              case (summary, applications: Seq[ApplicationFeeRow]) =>
                //Call Party connector for each application and get the party name
                //TODO use new table to hold all data in once place and avoid n+1 call
                Future.sequence(applications.map { a =>
                  partyConnector.find(agencyId, a.partyId).map {
                    case Some(party) => convertToResponse(a, Some(party.displayName))
                    case None => convertToResponse(a, None)
                  }
                }).map { updatedApplications =>
                  PortfolioWithApplications(summary, updatedApplications)
                }
            }
            .runWith(Sink.seq) // This collects the results into a Future[Seq[PortfolioWithApplications]]
        }
      }
  }

  private def convertToResponse(appFeeRow: ApplicationFeeRow, partyName: Option[String]): ApplicationFeeRowEnriched = {
    ApplicationFeeRowEnriched(
      portfolioId = appFeeRow.portfolioId,
      applicationId = appFeeRow.applicationId,
      applicationFee = appFeeRow.applicationFee,
      applicationFeeVat = appFeeRow.applicationFeeVat,
      partyId = appFeeRow.partyId,
      partyName = partyName,
      agencyId = appFeeRow.agencyId,
      status = appFeeRow.status
    )
  }

  override def bulkInvoicesCsvTemplate: ServiceCall[NotUsed, Base64CsvResponse] = auth(Authorization.agency) {
    authorized => loggedServiceCall {
      _ => {
        val agencyId = UUID.fromString(authorized.agencyMembership.get.agencyId)
        val segments = authorized.agencyMembership.get.segment.map(_.currentSegments)
        for {
          items <- bulkImportCsvTemplate.fetchItems(agencyId, segments)
          csv = bulkImportCsvTemplate.buildCsv(items)
        } yield Base64CsvResponse(csv)
      }
    }
  }

  override def getStats(): ServerServiceCall[NotUsed, PortfoliosStats] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { _ =>
          implicit val currDate: DateTime = DateUtcUtil.now()
          def getOccupiedPropertiesCount(portfoliosByProperty: Seq[PropertySearchImpl.PropertyWithLeasesResult], leaseType: String) =
            portfoliosByProperty.map(_.leases.filter(p =>
              p.tags.get("leaseType").contains(leaseType) && (
                PortfolioStatus.getStatusFromPortfolio(p) == PortfolioStatusLabel.Active ||
                PortfolioStatus.getStatusFromPortfolio(p) == PortfolioStatusLabel.Expiring
              )
            )).count(_.nonEmpty)

          for {
            portfoliosByProperty <- propertySearchImpl.allWithLease(UUID.fromString(authorization.agencyMembership.get.agencyId), Option.empty)
              .runWith(Sink.seq)
            allPortfolios = portfoliosByProperty.flatMap(_.leases)
            managedAndOccupiedPropertiesCount = getOccupiedPropertiesCount(portfoliosByProperty, ManagedContract_v1.NAME)
            unmanagedAndOccupiedPropertiesCount = getOccupiedPropertiesCount(portfoliosByProperty, UnmanagedContract_v1.NAME)
            activePortfolios = allPortfolios.filter(p => PortfolioStatus.getStatusFromPortfolio(p) == PortfolioStatusLabel.Active)
            managedDraftPortfolios = allPortfolios.filter(p =>
              p.tags.get("leaseType").contains(ManagedContract_v1.NAME) && PortfolioStatus.getStatusFromPortfolio(p) == PortfolioStatusLabel.Draft
            )
            unmanagedDraftPortfolios = allPortfolios.filter(p =>
              p.tags.get("leaseType").contains(UnmanagedContract_v1.NAME) && PortfolioStatus.getStatusFromPortfolio(p) == PortfolioStatusLabel.Draft
            )
            startingInNext30Days = activePortfolios.filter(p =>
              p.leaseTerms.exists(ls => currDate.isBefore(new DateTime(ls.startDate)) && currDate.plusDays(30).isAfter(new DateTime(ls.startDate)))
            )
            endingInNext30Days = allPortfolios.filter(l => PortfolioStatus.isActive(l.leaseTerms, l.terminatedReason, l.tags))
              .filter(_.leaseTerms.map(lt => new DateTime(lt.endDate))
                .exists(endDate => endDate.isBefore(currDate.plusDays(30)))
              )
            renewals = allPortfolios.filter(l => PortfolioStatus.getStatusFromPortfolio(l) == PortfolioStatusLabel.Renewal)
          } yield PortfoliosStats(
            /** @todo implement percentage change 30 days */
            ManagedAndOccupied(managedAndOccupiedPropertiesCount, 0, managedDraftPortfolios.length),
            UnmanagedAndOccupied(unmanagedAndOccupiedPropertiesCount, 0, unmanagedDraftPortfolios.length),
            StartingInNext30days(startingInNext30Days.length, 0, renewals.length),
            EndingInNext30days(endingInNext30Days.length, 0)
          )
        }
      }
    }

  override def portfolioEvents: Topic[api.PortfolioEvent] = TopicProducer.singleStreamWithOffset { offset =>
    persistentEntityRegistry.eventStream(PortfolioEvent.Tag, offset)
      .via(Flow.fromGraph(PortfolioEventsGraph(logger, persistentEntityRegistry, portfolioConnector)))
  }

  /*
  * Property API calls
  */

  private def validationFailedForFields(keys: Map[String, String]) = {
    ValidationException.wrapException("Object failed validation", keys.map { case (k, v) => ValidationError(k, v) }.toSet)
  }

  private def validatePropertyDetails(agencyId: UUID, placeId: Option[String], lightstoneId: Option[String],
                                         buildingName: Option[String], unitNumber: Option[String], selfPropertyId: Option[UUID]): Future[Done] = {
    val tuplations = (buildingName.filterNot(_.trim.isEmpty), unitNumber.filterNot(_.trim.isEmpty))
    val property = (placeId, lightstoneId) match {
      case (Some(_), Some(_)) => throw BadRequest("You can only include a placeId or lightstoneId not both")
      case (None, None) => throw BadRequest("You must include a placeId or lightstoneId")
      case (Some(id), None) => propertyConnector.findAll(agencyId).filter(_.placeId.contains(id))
      case (None, Some(_)) => propertyConnector.findAll(agencyId).filter(_.lightstoneId == lightstoneId)
    }
    property
      .filterNot(_.deletedAt.isDefined)
      .filterNot(p => selfPropertyId.contains(p.propertyId))
      .map(s => (s.buildingName, s.unitNumber))
      .filter(_ == tuplations)
      .runWith(Sink.headOption)
      .map {
        case Some(matchedTuple) =>
          // This is a very weird validation message, i couldn't find a generic way to explain so im doing a spiel for each case
          val fields = (matchedTuple._1.isDefined, matchedTuple._2.isDefined) match {
          // (building name defined, unit number defined)
            case (true, true) => Map(
              "general" -> "Building name and unit number already exist for this address")
            case (false, true) => Map(
              "unitNumber"   -> "Unit number already exists for the selected address")
            case (true, false) => Map(
              "buildingName" -> "Duplicate building name for this address")
            case (false, false) => Map(
              "buildingName" -> "Duplicate address exists, provide a building name or unit number")
          }
          if (fields.nonEmpty) throw validationFailedForFields(fields) else Done
        case None => Done
      }
  }

  override def updateProperty(id: String): ServerServiceCall[PropertyUpdate, Property_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { req =>
          val agencyId = UUID.fromString(authorization.agencyMembership.map(_.agencyId).get)
          val userId = UUID.fromString(authorization.userId)
          val ref = persistentEntityRegistry.refFor[PropertyEntity](id)
          for {
            placeId <- ref.ask(GetProperty).map(_.get.placeId)
            lightstoneId <- ref.ask(GetProperty).map(_.get.lightstoneId)
            property <- validatePropertyDetails(agencyId, placeId, lightstoneId, req.buildingName, req.unitNumber, Some(UUID.fromString(id)))
              .flatMap { _ => ref.ask(UpdateProperty(userId, agencyId, req.buildingName, req.unitNumber, req.schemeName, req.schemeNumber)) }
            _ <- propertyConnector.setAddress(agencyId, property.id, property.placeId, property.lightstoneId, property.addressComponents)
          } yield property
        }
      }
    }

  private case class PlaceDetails(
                           placeId: Option[String],
                           lightstoneId: Option[String],
                           addressComponents: List[Address.Component_v1],
                           location: Option[Geometry]
                         )

  private def processPropertyCreation(propertyCreation: PropertyCreation, agencyId: UUID, userId: UUID, startingPortfolioId: Option[UUID]) = {
    for {
      detailsResult <- validatePropertyDetails(agencyId, propertyCreation.placeId, propertyCreation.lightstoneId, propertyCreation.buildingName, propertyCreation.unitNumber, Option.empty[UUID])
        .flatMap(_ => (propertyCreation.placeId, propertyCreation.lightstoneId) match {
          case (Some(placeId), _) => googlePlaceService.details(placeId).invoke()
            .map(details => PlaceDetails(Some(placeId), None, details.addressComponents, Some(details.location)))
          case (_, Some(lightstoneId)) => lightstoneService.address(lightstoneId).invoke()
            .map(address => PlaceDetails(None, Some(lightstoneId), LightstoneUtils.mapAddressToAddressComponent(address), None))
          case _ => throw BadRequest("You must include a placeId or lightstoneId")
        })

      addressComponents: List[Address.Component_v1] = detailsResult.addressComponents

      userInputtedComponents: List[Address.Component_v1] = List(
        (propertyCreation.buildingName, Address.BuildingName()),
        (propertyCreation.unitNumber, Address.UnitNumber()),
        (propertyCreation.schemeName, Address.SchemeName()),
        (propertyCreation.schemeNumber, Address.SchemeNumber())
      ).collect {
        case (Some(longName), _type) => Address.Component_v1(longName, longName, Seq(_type))
      }
      // Replace existing components in addressComponents with userInputtedComponents
      mergedAddressComponents = userInputtedComponents.foldLeft(addressComponents) { (acc, component) =>
        acc.filterNot(_.types == component.types) :+ component
      }
      propertyId = UUID.randomUUID()
      ref = persistentEntityRegistry.refFor[PropertyEntity](propertyId.toString)
      createProperty = CreateProperty(userId, agencyId, startingPortfolioId, propertyCreation.placeId, propertyCreation.lightstoneId,
        mergedAddressComponents, detailsResult.location)
      result <- ref.ask(createProperty)
        .flatMap { _ => ref.ask(GetProperty) }
        // @todo potentially redundant setAddress as the user could still send multiple requests using the same place id and we wont be able to detect it in the entity
        .flatMap { reply => propertyConnector.setAddress(agencyId, reply.get.id, reply.get.placeId, reply.get.lightstoneId, reply.get.addressComponents).map(_ => reply.get) }
    } yield result
  }

  override def createProperty: ServerServiceCall[PropertyCreation, Property_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { request =>
          val agencyId = UUID.fromString(authorization.agencyMembership.map(_.agencyId).get)
          val userId = UUID.fromString(authorization.userId)
          processPropertyCreation(request, agencyId, userId, Option.empty[UUID])
        }
      }
    }

  override def getProperty(propertyId: String): ServerServiceCall[NotUsed, Property_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { (request) =>
          request match {
            case _ =>

              /**
                * @todo check agency  val agencyId = tokenContent.currentAgencyId.map(_.toString)
                */
              /**
               * @todo return 404 when collect None
               */
              persistentEntityRegistry.refFor[PropertyEntity](propertyId).ask(GetProperty).collect[Property_v1]({
                case Some(x: Property_v1) => x.copy(id = UUID.fromString(propertyId))
              })
          }
        }
      }
    }

  override def deleteProperty(propertyId: String): ServerServiceCall[NotUsed, PropertyDeleted] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { (_) =>
          val userId = UUID.fromString(authorization.userId)
          val agencyId = UUID.fromString(authorization.agencyMembership.map(_.agencyId).get)
          val promise = Promise[PropertyDeleted]

          persistentEntityRegistry.refFor[PropertyEntity](propertyId)
            .ask(DeleteProperty(userId, agencyId))
            .onComplete({
              case Success(_) => promise.success(PropertyDeleted(propertyId))
              case Failure(x) => promise.failure(x)
            })

          promise.future
        }
      }
    }

  /**
    * Add a landlord to a property, not sure if we should indicate whether this is a primary landlord or not.
    * For now let's just append it to a list.
  */
  override def addPropertyLandlord(id: String): ServerServiceCall[LandlordFields, LandlordId] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { landlord =>
          authorize(authorization).flatMap({
            authority =>
              val landlordId = UUID.randomUUID().toString()
              persistentEntityRegistry.refFor[PropertyEntity](id).ask(
                AddPropertyLandlord(UUID.fromString(landlord.partyId)))
                .map(_ => LandlordId(landlordId))
          })
        }
      }
    }

  private def searchPropertiesImpl(agencyId: UUID, query: String) = {
    propertySearchImpl.query(agencyId, query)
      .map { result =>
        import result._
        PropertySummary_v1(
          id = Some(propertyId.toString),
          placeId = placeId,
          lightstoneId = lightstoneId,
          description = description,
          mainText = mainText,
          secondaryText = secondaryText,
          tertiaryText = None)
      }
      .runWith(Sink.seq)
      .map { results =>
        PropertySearchResult(results.toList, 0, 0)
      }
  }

  override def combinedSearchProperties(query: String): ServerServiceCall[NotUsed, CombinedPropertySearchResult] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { (_) =>
          val agencyId = authorization.agencyMembership.map(_.agencyId)
          for {
            savedSearchResult <- searchPropertiesImpl(UUID.fromString(agencyId.get), query)
            autocompleteResult <- googlePlaceService.autocomplete(query, "address").invoke()
          } yield {
            val predictions = autocompleteResult.predictions.getOrElse(List.empty)

            CombinedPropertySearchResult(
              savedSearchResult.properties,
              predictions.map {
                case Autocomplete.Prediction(description, mainText, secondaryText, placeId) =>
                  GooglePlacesPrediction(description, mainText, secondaryText, placeId)
              }
            )
          }
        }
      }
    }

  override def searchProperties(query: String): ServerServiceCall[NotUsed, PropertySearchResult] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { _ =>
          val agencyId = authorization.agencyMembership.map(_.agencyId)
          for {
            savedResults <- searchPropertiesImpl(UUID.fromString(agencyId.get), query)
            lightstoneProperties <- lightstoneService.propertySearch(query).invoke()
              .map(_.results)
              .map(LightstoneUtils.mapToPropertySummary)
              .recover{
                case _ => logger.warn(s"Failed to retrieve lightstone properties for query: $query"); List.empty
              }

            results <- savedResults match {
                case PropertySearchResult(Nil, _, _) => Future.successful(PropertySearchResult(lightstoneProperties, 0, 0))
                case PropertySearchResult(properties, _, _) => Future.successful(
                  PropertySearchResult(properties ++ lightstoneProperties.filter(
                    p => !properties.exists(_.lightstoneId == p.lightstoneId)
                  ), 0, 0)
                )
            }
          } yield {
            PropertySearchResult(results.properties,0,0)
          }
        }
      }
    }

  override def advancedSearchProperties(): ServerServiceCall[PropertyPostRequest, PropertySearchResult] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { (payload) =>
          val agencyId = authorization.agencyMembership.map(_.agencyId)
          for {
            lightstoneResult <- lightstoneService.advancedPropertySearch().invoke(payload)
          } yield {
            PropertySearchResult(LightstoneUtils.mapToPropertySummary(lightstoneResult.results), 0, 0)
          }
        }
      }
    }

  override def getGooglePlace(placeId: String): ServerServiceCall[NotUsed, GooglePlacesResult] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { (_) =>
          googlePlaceService.details(placeId).invoke().map {
            case Details.Result(placeId, addressComponents, location, northeast, southwest) =>
            GooglePlacesResult(placeId, addressComponents, location, northeast, southwest)
          }
        }
      }
    }

  override def getLightstoneProperty(lightstoneId: String): ServerServiceCall[NotUsed, LightstoneResult] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { _ =>
          lightstoneService.address(lightstoneId).invoke().map(address =>
            LightstoneResult(lightstoneId, LightstoneUtils.mapAddressToAddressComponent(address))
          )
        }
      }
    }

  override def getPropertyPortfolios(id: String): ServiceCall[NotUsed, Seq[Portfolio_v1]] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { (_) =>
          val propertyId = UUID.fromString(id)

          portfolioConnector.find(UUID.fromString(authorization.agencyMembership.get.agencyId))
            .filter(_.propertyId == propertyId)
            .filter(_.deletedAt.isEmpty)
            .mapAsync(parallelism = 1) { row => // Enrich portfolio response with termination fields
              val portfolio = PortfolioTable.toPortfolio(row)
              refFor(portfolio.id).ask(GetPortfolio).map { response =>
                portfolio.copy(terminationFields = response.get.portfolio.terminationFields)
              }
            }
            .runWith(Sink.seq)
        }
      }
    }

  override def getPropertiesFromListInternal: ServiceCall[PropertyList, Seq[Property_v1]] = ServiceCall { body =>
    Source(body.propertyIds)
      .mapAsync(1) { p =>
        persistentEntityRegistry.refFor[PropertyEntity](p).ask(GetProperty).map {
          case Some(x: Property_v1) => Some(x.copy(id = UUID.fromString(p)))
          case None => None
        }
      }
      .collect { case Some(x) => x } // Filter out None values
      .runWith(Sink.seq)
  }

  override def getPropertiesV2(agencyId: String): ServerServiceCall[NotUsed, Seq[PropertyResultRowWithIDs]] = {
    auth(Authorization.agency) { _ =>
      logged {
        ServerServiceCall { _ =>
          for {
            props <- pgPropertyConnector.findAllPropertiesByAgency(UUID.fromString(agencyId))
            res = props.map(x => PropertyResultRowWithIDs(x.agencyId, x.propertyId, x.lightstoneId, x.placeId))
          } yield res
        }
      }
    }
  }

  override def getProperties: ServiceCall[NotUsed, Seq[PropertyResultRow]] =
    auth(Authorization.agency) { authorization =>
      val segments = authorization.agencyMembership.get.segment.map(_.currentSegments)
      logged {
        ServerServiceCall { (_) =>
          implicit val currDate: DateTime = DateUtcUtil.now()
          def preferredLease(seq: Seq[PortfolioTable]) = {
            seq.filter(_.tags.get("status").contains("approved"))
              .filter(p => p.leaseTerms.exists(_.rolloverMonthToMonth) || p.leaseTerms.exists(lt => new DateTime(lt.endDate).isBefore(currDate)))
              .find(p => p.leaseTerms.exists(lt => new DateTime(lt.startDate).isBefore(currDate)))
              .map(p => p.copy(tags = p.tags ++ Map("contract" -> "active")))
              .orElse(seq.headOption.map(p => p.copy(tags = p.tags ++ Map("contract" -> "inactive"))))
          }

          propertySearchImpl.allWithLease(UUID.fromString(authorization.agencyMembership.get.agencyId), segments)
            .runWith(Sink.seq)
            .map {
              results => results.flatMap { result =>
                import result.propertyRow._
                preferredLease(result.leases) match {
                  case Some(portfolio) => Seq(PropertyResultRow(agencyId, propertyId, portfolio.portfolioId,
                    mainText, secondaryText, portfolio.tags, portfolio.leaseTerms.map(p => new DateTime(p.endDate)),
                    portfolio.tenants.headOption.map(p => UUID.fromString(p.partyId)),
                    portfolio.landlord.headOption.map(p => UUID.fromString(p.partyId))))
                  case None => Seq.empty
                }
              }
            }
        }
      }
    }

  override def updateTenantParties(id: String): ServiceCall[Tenants, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { tenants =>
          authorize(authorization, Some(id)).flatMap { _ =>
            refFor(id).ask(AmendTenants(tenants, Some(authorization.userId), System.currentTimeMillis()))
              .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
              .recover { case PartiesUpdateOutOfDate() =>
                throw validationFailedForFields(Map("lastFetched" -> "This lease has been recently updated. Please reload before making changes"))
              }
          }
        }
      }
    }

  override def portfoliosWithStatusByAgency(agencyId: String): ServiceCall[NotUsed, Seq[LeaseStatusRow]] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { _ =>
          for {
            portfolios <- pgPortfolioStatusConnector.findByAgency(UUID.fromString(agencyId))
          } yield portfolios
        }
      }
    }

  override def updateOwnerParties(id: String): ServiceCall[Owners, Portfolio_v1] =
    auth(Authorization.agency) { authorization =>
      logged {
        ServerServiceCall { owners =>
          authorize(authorization, Some(id)).flatMap { _ =>
            refFor(id).ask(AmendOwners(owners, Some(authorization.userId), System.currentTimeMillis()))
              .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
              .recover { case PartiesUpdateOutOfDate() =>
                throw validationFailedForFields(Map("lastFetched" -> "This lease has been recently updated. Please reload before making changes"))
              }
          }
        }
      }
    }

  private def buildFullString(implicit components: List[Component_v1]): String = {
    new PropertyBuilder().withAddressComponents(components).buildFullAddress()
  }

  override def propertyEvents(): Topic[propEvent] = TopicProducer.singleStreamWithOffset[propEvent] { offset =>
    import scala.collection.immutable
    persistentEntityRegistry.eventStream(PropertyEvent.Tag, offset)
      .mapConcat[(propEvent, Offset)]({
      case EventStreamElement(propertyId, PropertyAddressAssigned_v1(user, agency, placeId, lightstoneId, address, location), _offset) =>
        immutable.Seq((PropertyCreated_v1(UUID.fromString(propertyId), placeId, lightstoneId, agency, buildFullString(address)), _offset))
      case EventStreamElement(propertyId, PropertyAddressUpdated(agency, placeId, lightstoneId, address, location, leaseIds, eventRecord), _offset) =>
        val buildingName = address.filter(_.types.contains(BuildingName())).map(_.longName).lastOption
        val unitNumber = address.filter(_.types.contains(UnitNumber())).map(_.longName).lastOption
        val event = (PropertyCreated_v1(UUID.fromString(propertyId), placeId, lightstoneId, agency, buildFullString(address)), _offset)
        immutable.Seq(event) ++ immutable.Seq(leaseIds.map { leaseId =>
          (LeaseAssociatedWithProperty(UUID.fromString(propertyId), agency, leaseId, placeId, lightstoneId,
            buildFullString(address), buildingName, unitNumber, eventRecord), _offset)
        }: _*)
      case EventStreamElement(propertyId, LeaseAssociated(leaseId, agencyId, placeId, lightstoneId, address, eventRecord), _offset) =>
        val buildingName = address.filter(_.types.contains(BuildingName())).map(_.longName).lastOption
        val unitNumber = address.filter(_.types.contains(UnitNumber())).map(_.longName).lastOption
        immutable.Seq((LeaseAssociatedWithProperty(UUID.fromString(propertyId), agencyId, leaseId, placeId, lightstoneId,
          buildFullString(address), buildingName, unitNumber, eventRecord), _offset))
      case EventStreamElement(propertyId, PropertyDeleted_v1(user, agency), _offset) =>
        immutable.Seq((PropertyDestroyed_v1(UUID.fromString(propertyId), agency), _offset))
      case _ => immutable.Seq.empty
    })
  }

  override def renew(id: String): ServiceCall[Renewal, Done] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { renewal =>
        authorize(authorization).flatMap({ authority =>
          val currentDateTime: DateTime = DateUtcUtil.now()
          persistentEntityRegistry.refFor[PortfolioEntity](id).ask(RenewLease(Some(renewal), Some(UUID.fromString(authority.userId)), currentDateTime))
        })
      }
    }
  }

  override def renewV2(id: String): ServiceCall[Renewal, Portfolio_v1] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { renewal =>
        authorize(authorization).flatMap({ authority =>
          val currentDateTime: DateTime = DateUtcUtil.now()
          persistentEntityRegistry.refFor[PortfolioEntity](id).ask(RenewLease(Some(renewal), Some(UUID.fromString(authority.userId)), currentDateTime))
            .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
        })
      }
    }
  }

  override def deleteRenew(id: String): ServiceCall[NotUsed, Done] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization).flatMap({ authority =>
          persistentEntityRegistry.refFor[PortfolioEntity](id).ask(DeleteRenewal(Some(UUID.fromString(authority.userId))))
        })
      }
    }
  }

  override def deleteRenewV2(id: String): ServiceCall[NotUsed, Portfolio_v1] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization).flatMap({ authority =>
          persistentEntityRegistry.refFor[PortfolioEntity](id).ask(DeleteRenewal(Some(UUID.fromString(authority.userId))))
            .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
        })
      }
    }
  }

  override def terminateLease(id: String): ServiceCall[TerminationFields, Done] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { (headers, fields) =>
        authorize(authorization).flatMap({ authority =>
          // if true then make a call to release the funds
          def releaseDeposits(portfolioId: UUID, partyId: String): Future[Done] = {
            if (Authorization.ownerRole.isAuthorized.applyOrElse[(TokenContent, RequestHeader), Boolean]((authority, headers), _ => false)) {
              walletService.refundDeposits(partyId)
                .handleRequestHeader(auth.authenticatedHandler(authorization))
                .invoke(ReleaseDeposit(portfolioId))
            } else {
              throw Forbidden(s"Not authorized: ${Authorization.ownerRole.hint}")
            }
          }

          for {
            entity  <- persistentEntityRegistry.refFor[PortfolioEntity](id).ask(GetPortfolio)
            tenant   = entity.flatMap(_.portfolio.tenants.find(_.isPrimary))
            _       <- (tenant, fields.releaseDeposit) match {
                         case (Some(t), Some(true)) => releaseDeposits(UUID.fromString(id), t.partyId)
                         case _                     => Future( Done )
                       }
            updatedFields = fields.terminatedAt match {
                   case None => fields.copy(terminatedAt = Some(DateTime.now().toLocalDate))
                   case _ => fields
                 }
            _       <- persistentEntityRegistry.refFor[PortfolioEntity](id).ask(TerminateLease(updatedFields, Some(UUID.fromString(authority.userId))))
          } yield (ResponseHeader.Ok, Done)
        })
      }
    }
  }

  override def terminateLeaseV2(id: String): ServiceCall[TerminationFields, Portfolio_v1] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { (headers, fields) =>
        authorize(authorization).flatMap({ authority =>
          // if true then make a call to release the funds
          def releaseDeposits(portfolioId: UUID, partyId: String): Future[Done] = {
            if (Authorization.ownerRole.isAuthorized.applyOrElse[(TokenContent, RequestHeader), Boolean]((authority, headers), _ => false)) {
              walletService.refundDeposits(partyId)
                .handleRequestHeader(auth.authenticatedHandler(authorization))
                .invoke(ReleaseDeposit(portfolioId))
            } else {
              throw Forbidden(s"Not authorized: ${Authorization.ownerRole.hint}")
            }
          }

          for {
            entity  <- persistentEntityRegistry.refFor[PortfolioEntity](id).ask(GetPortfolio)
            tenant   = entity.flatMap(_.portfolio.tenants.find(_.isPrimary))
            _       <- (tenant, fields.releaseDeposit) match {
              case (Some(t), Some(true)) => releaseDeposits(UUID.fromString(id), t.partyId)
              case _                     => Future( Done )
            }
            updatedFields = fields.terminatedAt match {
              case None => fields.copy(terminatedAt = Some(DateTime.now().toLocalDate))
              case _ => fields
            }
            reply       <- persistentEntityRegistry.refFor[PortfolioEntity](id).ask(TerminateLease(updatedFields, Some(UUID.fromString(authority.userId))))
              .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
          } yield (ResponseHeader.Ok, reply)
        })
      }
    }
  }

  override def terminationReasons(): ServiceCall[NotUsed, Seq[TerminationFields.TerminationReasonSummary]] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization).flatMap({ _ => Future(TerminationFields.TerminationReason.all.toSeq.map(_.summary)) })
      }
    }
  }

  override def deleteLease(id: String): ServiceCall[NotUsed, Done] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization).flatMap({ authority =>
          persistentEntityRegistry.refFor[PortfolioEntity](id).ask(DeleteLease(Some(UUID.fromString(authority.userId))))
        })
      }
    }
  }

  override def deleteLeaseV2(id: String): ServiceCall[NotUsed, Portfolio_v1] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization).flatMap({ authority =>
          persistentEntityRegistry.refFor[PortfolioEntity](id).ask(DeleteLease(Some(UUID.fromString(authority.userId))))
            .flatMap(_ => refFor(id).ask(GetPortfolio).map(_.get.portfolio))
        })
      }
    }
  }


  override def addApplication(portfolioId: String): ServiceCall[AddApplicationFields, ApplicationFeeResponse] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { request =>
        authorize(authorization, Some(portfolioId)).flatMap({ authority =>
          val agencyId = UUID.fromString(authority.agencyMembership.map(_.agencyId).get)
          val applicationId = pUUID.createV4

          for {
            _ <- persistentEntityRegistry.refFor[ApplicationFeeEntity](applicationId)
              .ask(CreateApplication(request.applicationFee, request.vat, request.partyId, agencyId, portfolioId, EventRecord(Some(authority.userId))))

            // Insert application into application_fees table
            _ <- applicationFeeConnector.insertApplicationFee(
              ApplicationFeeRow(portfolioId,
                applicationId,
                request.applicationFee,
                request.vat,
                request.partyId,
                agencyId,
                None
              ))

            pd = request.propDataFields
            _ <- pgApplicationConnector.insert(Some(applicationId), Some(agencyId), Some(UUID.fromString(portfolioId)),
              Some(request.partyId), Some(request.applicationFee), request.vat,
              pd.flatMap(_.applicationId), pd.flatMap(_.siteId), pd.flatMap(_.residentialId), pd.flatMap(_.lightstoneId), pd.flatMap(_.status))

            applications <- applicationFeeConnector.getApplicationFees(portfolioId)

          } yield ApplicationFeeResponse(portfolioId, applications)
        })
      }
    }
  }

  override def updateApplication(portfolioId: String, applicationId: String): ServiceCall[AddApplicationFields, ApplicationFeeResponse] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { request =>
        authorize(authorization, Some(portfolioId)).flatMap({ authority =>
          val agencyId = UUID.fromString(authority.agencyMembership.map(_.agencyId).get)

          for {
            _ <- persistentEntityRegistry.refFor[ApplicationFeeEntity](applicationId)
              .ask(UpdateApplication(request.applicationFee, request.vat, request.partyId, agencyId, portfolioId, EventRecord(Some(authority.userId))))

            _ <- applicationFeeConnector.insertApplicationFee(
              ApplicationFeeRow(portfolioId,
                applicationId,
                request.applicationFee,
                request.vat,
                request.partyId,
                agencyId,
                None
              ))

            _ <- pgApplicationConnector.update(applicationId, request.applicationFee, request.vat)

            applications <- applicationFeeConnector.getApplicationFees(portfolioId)

          } yield ApplicationFeeResponse(portfolioId, applications)
        })
      }
    }
  }

  override def removeApplication(portfolioId: String, applicationId: String): ServiceCall[NotUsed, ApplicationFeeResponse] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization, Some(portfolioId)).flatMap({ authority =>
          for {
            _ <- persistentEntityRegistry.refFor[ApplicationFeeEntity](applicationId)
              .ask(DeleteApplication(EventRecord(Some(authority.userId))))

            _ <- applicationFeeConnector.updateStatus(portfolioId, applicationId, ApplicationFee.ApplicationStatus.Deleted.toString)
            _ <- pgApplicationConnector.updateStatus(UUID.fromString(applicationId), ApplicationFee.ApplicationStatus.Deleted.toString).recover{
              case e:Exception => logger.info("Error updating application status in pg_application table", e)
            }

            applications <- applicationFeeConnector.getApplicationFees(portfolioId)
          } yield ApplicationFeeResponse(portfolioId, applications)
        })
      }
    }
  }

  override def getApplications(portfolioId: String): ServiceCall[NotUsed, ApplicationFeeResponse] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization, Some(portfolioId)).flatMap({ _ =>
          applicationFeeConnector.getApplicationFees(portfolioId).map {
            applications => ApplicationFeeResponse(portfolioId, applications)
          }
        })
      }
    }
  }

  override def acceptApplication(portfolioId: String, applicationId: String): ServiceCall[NotUsed, Portfolio_v1] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization, Some(portfolioId)).flatMap({ authority =>
          for{
            tenantParty <- persistentEntityRegistry.refFor[ApplicationFeeEntity](applicationId)
              .ask(GetApplicationFee).map(_.get.tenantPartyId)

            _ <- persistentEntityRegistry.refFor[ApplicationFeeEntity](applicationId)
              .ask(AcceptApplication(EventRecord(Some(authority.userId))))

            _ <- applicationFeeConnector.updateStatus(portfolioId, applicationId, ApplicationStatus.Accepted.toString)
            _ <- pgApplicationConnector.updateStatus(UUID.fromString(applicationId), ApplicationStatus.Accepted.toString)

            applications <- applicationFeeConnector.getApplicationFees(portfolioId)
            applicationsToReject = applications.filter(!_.status.contains(ApplicationStatus.Accepted.toString))

            // reject all non accepted applications
            _ <- Future.sequence(applicationsToReject.map(application =>
              persistentEntityRegistry.refFor[ApplicationFeeEntity](application.applicationId)
                .ask(RejectApplication(EventRecord(Some(authority.userId))))
            ))

            tenants = Tenants(DateUtcUtil.now(), Some(Tenants.TenantParty(tenantParty)), Seq.empty)

            response <- persistentEntityRegistry.refFor[PortfolioEntity](portfolioId).ask(
              AmendTenants(tenants, Some(authority.userId), System.currentTimeMillis())
            ).flatMap(_ => refFor(portfolioId).ask(GetPortfolio).map(_.get.portfolio))

          } yield response
        })
      }
    }
  }

  override def getApplicationsByAgency(agencyId: String): ServiceCall[NotUsed, ApplicationResponse] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { _ =>
        authorize(authorization).flatMap({ _ =>
          val agencyUUID = UUID.fromString(agencyId)
          pgApplicationConnector.getApplicationsByAgency(agencyUUID)
            .map(rows => ApplicationResponse(agencyUUID, rows))
        })
      }
    }
  }

  override def getHistoricalCommandsByAgency(agencyId: String): ServiceCall[HistoricalEventRequest, Seq[LatestCommandsResponse]] =
    logged {
      ServerServiceCall { req =>
          val agencyUUID = UUID.fromString(agencyId)
          val dateConverted = req.cutoffDate.map(DateTime.parse).getOrElse(DateUtcUtil.now())
          successfulCommandConnector.getHistoricalEventsByAgency(
            agencyUUID,
            dateConverted,
            req.commandName.getOrElse("AmendCommissionV2"))
      }
    }

  override def getApplicationsByAgencies(): ServiceCall[AgenciesList, ApplicationByAgencyIdResponse] = auth(Authorization.agency) { authorization =>
    logged {
      ServerServiceCall { body =>
        authorize(authorization).flatMap({ _ =>
          for {
            result <- pgApplicationConnector.getApplicationsByAgencies(body.agencies).map(_.groupBy(_.agencyId))
          } yield ApplicationByAgencyIdResponse(result)
        })
      }
    }
  }

  override def applicationFeeEvents(): Topic[api.ApplicationFeeEvent] = TopicProducer.singleStreamWithOffset { offset =>
    persistentEntityRegistry.eventStream(com.phoenix.portfolio.impl.applicationfee.ApplicationFeeEvent.Tag, offset)
      .via(ApplicationFeeEventsGraph())
  }

}
