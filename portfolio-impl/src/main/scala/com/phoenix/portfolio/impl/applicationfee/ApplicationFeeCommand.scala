package com.phoenix.portfolio.impl.applicationfee

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.phoenix.json.JsonFormats.singletonFormat
import com.phoenix.persistence.EventRecord
import com.phoenix.portfolio.api._
import com.phoenix.util.UUID
import play.api.libs.json.{Format, Json}

import java.util.{UUID => JavaUUID}

sealed trait ApplicationFeeCommand

case class ApplicationFeeReply(portfolio: Portfolio_v1, agencyId: JavaUUID)

object ApplicationFeeReply {
  implicit val format: Format[ApplicationFeeReply] = Json.format
}

case object GetApplicationFee extends ApplicationFeeCommand with ReplyType[Option[ApplicationFee]] {
  implicit val format: Format[GetApplicationFee.type] = singletonFormat(GetApplicationFee)
}

case class CreateApplication(applicationFee: BigDecimal, vat: Option[Boolean], partyId: UUID, agencyId: UUID, portfolioId: UUID,
                             eventRecord: EventRecord)
  extends ApplicationFeeCommand with ReplyType[Done]

object CreateApplication {
  implicit val format: Format[CreateApplication] = Json.format
}

case class UpdateApplication(applicationFee: BigDecimal, vat: Option[Boolean], partyId: UUID, agencyId: UUID, portfolioId: UUID,
                             eventRecord: EventRecord)
  extends ApplicationFeeCommand with ReplyType[Done]

object UpdateApplication {
  implicit val format: Format[UpdateApplication] = Json.format
}

case class DeleteApplication(eventRecord: EventRecord) extends ApplicationFeeCommand with ReplyType[Done]

object DeleteApplication {
  implicit val format: Format[DeleteApplication] = Json.format
}

case class AcceptApplication(eventRecord: EventRecord) extends ApplicationFeeCommand with ReplyType[Done]

object AcceptApplication {
  implicit val format: Format[AcceptApplication] = Json.format
}

case class RejectApplication(eventRecord: EventRecord) extends ApplicationFeeCommand with ReplyType[Done]

object RejectApplication {
  implicit val format: Format[RejectApplication] = Json.format
}

