package com.phoenix.portfolio.impl.listeners

import akka.Done
import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.InvalidCommandException
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.phoenix.logging.EventLogging
import com.phoenix.party.api._
import com.phoenix.persistence.PersistentEntityRegistrySugar
import com.phoenix.portfolio.impl.PortfolioSummaryImpl
import com.phoenix.portfolio.impl.connectors.{PartyConnector, PartyPortfolio, PartyRow, PortfolioConnector, PortfolioSummaryConnector}
import com.phoenix.util.UUID
import com.typesafe.scalalogging.LazyLogging

import scala.concurrent.{ExecutionContext, Future}

class PartyEventListener(override val entityRegistry: PersistentEntityRegistry,
                         partyConnector: PartyConnector,
                         portfolioConnector: PortfolioConnector,
                         portfolioSummaryConnector: PortfolioSummaryConnector,
                         portfolioSummaryImpl: PortfolioSummaryImpl,
                         partyService: PartyService)
                        (implicit executionContext: ExecutionContext)
  extends PersistentEntityRegistrySugar
    with EventLogging
    with LazyLogging {

  private def insertParty(partyId: UUID, taggableParty: TaggableParty): Future[Done] = taggableParty match {
    case party: Company_v1 =>
      partyConnector.insert(
        PartyRow(taggableParty.agency.map(UUID.fromString).get, partyId, isCompany = true, party.contactPersonFirstName.getOrElse(""), party.contactPersonLastName.getOrElse(""),
          party.emailAddress, party.vatNumber, party.companyName, party.tradingAs, party.telNumber,
          party.bank, party.branchCode, party.accountName, party.accountNumber, party.accountType)
      ).map(_ => Done)
    case party: Person_v1 =>
      partyConnector.insert(
        PartyRow(taggableParty.agency.map(UUID.fromString).get, partyId, isCompany = false, party.firstName.getOrElse(""), party.lastName.getOrElse(""),
          party.emailAddress, None, None, None,
          party.cellNumber, party.bank, party.branchCode, party.accountName, party.accountNumber, party.accountType)
      ).map(_ => Done)
    case _ => Future(Done)
  }

  private def updatePortfolioSummary(portfolio: PartyPortfolio): Future[Done] = {
    portfolioSummaryImpl.upsert(portfolio.portfolioId)
  }

  // Enable the portfolio summary upsert after RS is successful
  partyService.partyEvents
    .subscribe
    .withGroupId("portfolioPartyEvent_0_54")
    .atLeastOnce(
      Flow[PartyEvent].mapAsync(1)(logEventAsync)
        .mapAsync(1) {
          case PartyCreated_v1(partyId, taggableParty, _) if taggableParty.agency.exists(_.nonEmpty) => for {
            _               <- insertParty(partyId, taggableParty)
            partyPortfolios <- portfolioConnector.findPartyPortfolios(partyId)
            _               <- Future.sequence(partyPortfolios.map(updatePortfolioSummary(_)))
          } yield Done
          case PartyUpdated_v1(partyId, taggableParty, _, _) if taggableParty.agency.exists(_.nonEmpty) => for {
            _               <- insertParty(partyId, taggableParty)
            partyPortfolios <- portfolioConnector.findPartyPortfolios(partyId)
            _               <- Future.sequence(partyPortfolios.map(updatePortfolioSummary(_)))
          } yield Done
          case _ => Future.successful(Done)
        }).recover ({
          case  _: InvalidCommandException => Done
          case e                           => throw e
        })

}
