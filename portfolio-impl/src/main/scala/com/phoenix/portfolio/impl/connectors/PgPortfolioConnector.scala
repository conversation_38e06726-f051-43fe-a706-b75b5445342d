package com.phoenix.portfolio.impl.connectors

import akka.{Done, NotUsed}
import com.github.jasync.sql.db.RowData
import com.phoenix.common.data.{ReadsideConnector, ReadsideDatabase}
import com.phoenix.portfolio.api._
import com.phoenix.portfolio.api.request.{LeaseTermsFields_v1, Settings_v1, TerminationFields}
import com.phoenix.portfolio.impl.portfolio.{LeaseOwner, LeaseTenant}
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.{DateTime, DateTimeZone}
import play.api.libs.json.Json

import java.sql.Timestamp
import java.time.{LocalDateTime, ZoneOffset}
import java.util.UUID
import scala.collection.Seq
import scala.concurrent.{ExecutionContext, Future}

class PgPortfolioConnector(readsideDatabase: ReadsideDatabase)(implicit ec: ExecutionContext)
  extends ReadsideConnector(readsideDatabase.getConnectionPool) with LazyLogging {

  //scalastyle:off
  def update(portfolio: PortfolioTable): Future[Done] = {
    val query = """
    INSERT INTO portfolios (
      agency_id, portfolio_id, property_id, landlord, tenants, tags, contract_container, invoice_templates, agents,
      lease_terms, lease_identifier, settings, commission, renewal, segments, terminated_reason, created_by_user_id,
      updated_by_user_id, created_at, updated_at, deleted_at, meta_data
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ON CONFLICT (portfolio_id) DO UPDATE SET
      property_id = EXCLUDED.property_id,
      landlord = EXCLUDED.landlord,
      tenants = EXCLUDED.tenants,
      tags = EXCLUDED.tags,
      contract_container = EXCLUDED.contract_container,
      invoice_templates = EXCLUDED.invoice_templates,
      agents = EXCLUDED.agents,
      lease_terms = EXCLUDED.lease_terms,
      lease_identifier = EXCLUDED.lease_identifier,
      settings = EXCLUDED.settings,
      commission = EXCLUDED.commission,
      renewal = EXCLUDED.renewal,
      segments = EXCLUDED.segments,
      terminated_reason = EXCLUDED.terminated_reason,
      created_by_user_id = EXCLUDED.created_by_user_id,
      updated_by_user_id = EXCLUDED.updated_by_user_id,
      created_at = EXCLUDED.created_at,
      updated_at = EXCLUDED.updated_at,
      deleted_at = EXCLUDED.deleted_at,
      meta_data = EXCLUDED.meta_data
    """
    val values = List(
      portfolio.agencyId,
      portfolio.portfolioId,
      portfolio.propertyId,
      Json.toJson(portfolio.landlord),
      Json.toJson(portfolio.tenants),
      Json.toJson(portfolio.tags),
      portfolio.contractContainer.map(Json.toJson(_).toString()).orNull,
      Json.toJson(portfolio.invoiceTemplates),
      Json.toJson(portfolio.agents),
      portfolio.leaseTerms.map(Json.toJson(_).toString()).orNull,
      portfolio.leaseIdentifier.map(Json.toJson(_).toString()).orNull,
      portfolio.settings.map(Json.toJson(_).toString()).orNull,
      portfolio.commission.map(Json.toJson(_).toString()).orNull,
      portfolio.renewal.map(Json.toJson(_).toString()).orNull,
      portfolio.segments.map(Json.toJson(_).toString()).orNull,
      portfolio.terminatedReason.map(_.objectName).orNull,
      portfolio.createdByUserId,
      portfolio.updatedByUserId,
      new Timestamp(portfolio.createdAt.withZone(org.joda.time.DateTimeZone.UTC).getMillis),
      new Timestamp(portfolio.updatedAt.withZone(org.joda.time.DateTimeZone.UTC).getMillis),
      portfolio.deletedAt.map(d => new Timestamp(d.getMillis)).orNull,
      portfolio.metaData.orNull
    )
    query.execute(values).failOnUnaffected("Failed to update portfolio")
  }

  def findAgencyPortfolios(agencyId: UUID): Future[Seq[PortfolioTable]] = {
    val query = "SELECT * FROM portfolios WHERE agency_id = ?"
    query.execute(List(agencyId)).mapWith(mapToPortfolioTable)
  }

  def findPortfolio(agencyId: UUID, portfolioId: UUID): Future[Option[PortfolioTable]] = {
    val query = "SELECT * FROM portfolios WHERE agency_id = ? AND portfolio_id = ?"
    query.execute(List(agencyId, portfolioId)).mapWith(mapToPortfolioTable).map(_.headOption)
  }

  def updateFromEntity(portfolioAgency: PortfolioAgency, portfolio: Portfolio_v1): Future[Done] = {
    import portfolio._
    def toOwnerParty(landlord: Portfolio_v1.Landlord_v1) = LeaseOwner(landlord.id, landlord.partyId, landlord.isPrimary)
    def toTenantParty(tenant: Portfolio_v1.Tenant_v1) = LeaseTenant(tenant.id, tenant.partyId, tenant.isPrimary)
    val partyOwners = landlord.map(toOwnerParty)
    val partyTenant = tenants.map(toTenantParty)
    update(PortfolioTable(portfolioAgency.agencyId, portfolioAgency.portfolioId, UUID.fromString(propertyId.get),
      partyOwners, partyTenant, tags, contractContainer, invoiceTemplates, agents, leaseTerms, leaseIdentifier, settings, commission,
      renewal, segments, terminatedReason, UUID.fromString(createdByUserId),
      UUID.fromString(updatedByUserId), new DateTime(createdAt), new DateTime(updatedAt), deletedAt.map(s => new DateTime(s)), portfolio.metaData))
  }

  def insertPortfolioAgency(portfolioAgency: PortfolioAgency): Future[Done] = {
    val query = """
      INSERT INTO portfolio_agencies (portfolio_id, property_id, agency_id)
      VALUES (?, ?, ?)
      ON CONFLICT (portfolio_id) DO UPDATE SET
      property_id = EXCLUDED.property_id,
      agency_id = EXCLUDED.agency_id
    """
    val values = List(portfolioAgency.portfolioId, portfolioAgency.propertyId, portfolioAgency.agencyId)
    query.execute(values).failOnUnaffected("Failed to upsert portfolio agency")
  }

  def findPortfolioAgency(portfolioId: UUID): Future[Option[PortfolioAgency]] = {
    val query = "SELECT * FROM portfolio_agencies WHERE portfolio_id = ?"
    query.execute(List(portfolioId)).mapWith(mapToPortfolioAgency).map(_.headOption)
  }

  def upsertPartyPortfolio(partyPortfolio: PartyPortfolio): Future[Done] = {
    val query = """
      INSERT INTO party_portfolios (party_id, portfolio_id)
      VALUES (?, ?)
      ON CONFLICT (party_id, portfolio_id) DO UPDATE SET
      party_id = EXCLUDED.party_id,
      portfolio_id = EXCLUDED.portfolio_id
    """
    val values = List(partyPortfolio.partyId, partyPortfolio.portfolioId)
    query.execute(values).failOnUnaffected("Failed to upsert party portfolio")
  }

  def findPartyPortfoliosByParty(partyId: UUID): Future[Seq[PartyPortfolio]] = {
    val query = "SELECT * FROM party_portfolios WHERE party_id = ?"
    query.execute(List(partyId)).mapWith(mapToPartyPortfolio)
  }

  def findPartyPortfoliosByPortfolio(portfolioId: UUID): Future[Seq[PartyPortfolio]] = {
    val query = "SELECT * FROM party_portfolios WHERE portfolio_id = ?"
    query.execute(List(portfolioId)).mapWith(mapToPartyPortfolio)
  }

  def insertLeaseRenewal(leaseRenewalByDate: LeaseRenewalByDate): Future[Done] = {
    val query = """
      INSERT INTO lease_renewals_by_date (portfolio_id, date)
      VALUES (?, ?)
      ON CONFLICT (portfolio_id) DO UPDATE SET
      date = EXCLUDED.date
    """
    val values = List(leaseRenewalByDate.portfolioId, leaseRenewalByDate.date)
    query.execute(values).failOnUnaffected("Failed to insert or update lease renewal")
  }

  def findLeaseRenewals(date: String): Future[Seq[LeaseRenewalByDate]] = {
    val query = "SELECT * FROM lease_renewals_by_date WHERE date = ?"
    query.execute(List(date)).mapWith(mapToLeaseRenewalByDate)
  }

  private def mapToPortfolioTable(row: RowData): PortfolioTable = {
    PortfolioTable(
      agencyId = row.get("agency_id").asInstanceOf[UUID],
      portfolioId = row.get("portfolio_id").asInstanceOf[UUID],
      propertyId = row.get("property_id").asInstanceOf[UUID],
      landlord = Json.parse(row.getString("landlord")).as[scala.collection.immutable.Seq[LeaseOwner]],
      tenants = Json.parse(row.getString("tenants")).as[scala.collection.immutable.Seq[LeaseTenant]],
      tags = Json.parse(row.getString("tags")).as[Map[String, String]],
      contractContainer = Option(row.getString("contract_container")).filter(_.nonEmpty).flatMap(Json.parse(_).asOpt[ContractContainer]),
      invoiceTemplates = Json.parse(row.getString("invoice_templates")).as[scala.collection.immutable.Seq[InvoiceTemplate_v1]],
      agents = Json.parse(row.getString("agents")).as[scala.collection.immutable.Seq[Agent_v1]],
      leaseTerms = Option(row.getString("lease_terms")).flatMap(Json.parse(_).asOpt[LeaseTermsFields_v1]),
      leaseIdentifier = Option(row.getString("lease_identifier")),
      settings = Option(row.getString("settings")).flatMap(Json.parse(_).asOpt[Settings_v1]),
      commission = Option(row.getString("commission")).flatMap(Json.parse(_).asOpt[CommissionSplit_v1]),
      renewal = Option(row.getString("renewal")).flatMap(Json.parse(_).asOpt[Renewal]),
      segments = Option(row.getString("segments")).flatMap(Json.parse(_).asOpt[scala.collection.immutable.Seq[UUID]]),
      terminatedReason = Option(row.getString("terminated_reason")).flatMap(Json.parse(_).asOpt[TerminationFields.TerminationReason]),
      createdByUserId = row.get("created_by_user_id").asInstanceOf[UUID],
      updatedByUserId = row.get("updated_by_user_id").asInstanceOf[UUID],
      createdAt = new DateTime(Timestamp.valueOf(row.getAs[LocalDateTime]("created_at")).getTime),
      updatedAt = new DateTime(Timestamp.valueOf(row.getAs[LocalDateTime]("updated_at")).getTime),
      deletedAt = Option(new DateTime(Timestamp.valueOf(row.getAs[LocalDateTime]("deleted_at")).getTime)),
      metaData = Option(row.getString("meta_data"))
    )
  }

  private def mapToPortfolioAgency(row: RowData): PortfolioAgency = {
    PortfolioAgency(
      portfolioId = row.get("portfolio_id").asInstanceOf[UUID],
      propertyId = row.get("property_id").asInstanceOf[UUID],
      agencyId = row.get("agency_id").asInstanceOf[UUID]
    )
  }

  private def mapToPartyPortfolio(row: RowData): PartyPortfolio = {
    PartyPortfolio(
      partyId = row.get("party_id").asInstanceOf[UUID],
      portfolioId = row.get("portfolio_id").asInstanceOf[UUID]
    )
  }

  private def mapToLeaseRenewalByDate(row: RowData): LeaseRenewalByDate = {
    LeaseRenewalByDate(
      date = row.getString("date"),
      portfolioId = row.get("portfolio_id").asInstanceOf[UUID]
    )
  }

  def deletePartyPortfolio(partyPortfolio: PartyPortfolio): Future[Done] = {
    val query = """
      DELETE FROM party_portfolios
      WHERE party_id = ? AND portfolio_id = ?
    """
    val values = List(partyPortfolio.partyId, partyPortfolio.portfolioId)
    query.execute(values).recover {
      case _: Exception =>
        logger.error(s"Failed to delete party_portfolio with partyId: ${partyPortfolio.partyId}" +
          s" and portfolioId: ${partyPortfolio.portfolioId}")
    }.map(_ => Done)
  }


}