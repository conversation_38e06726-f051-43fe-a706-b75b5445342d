#play.crypto.secret is deprecated, use play.http.secret.key instead
play.http.secret.key = ${?APPLICATION_SECRET}
play.application.loader = com.phoenix.portfolio.impl.PortfolioLoader

portfolio.cassandra.keyspace = portfolio
jwt.issuer = portfolio

sentry-dsn = ${?SENTRY_DSN}

akka {
  actor {
    enable-additional-serialization-bindings = on

    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      jackson-cbor = "akka.serialization.jackson.JacksonCborSerializer"
    }

    serialization-bindings {
      "com.phoenix.portfolio.impl.agencyportfolio.AgencyPortfoliosCommand" = jackson-json
      "com.phoenix.portfolio.impl.agencyportfolio.AgencyPortfoliosEvent" = jackson-json
      "com.phoenix.portfolio.impl.agencyportfolio.AgencyPortfoliosState" = jackson-json
      "com.phoenix.portfolio.impl.applicationfee.ApplicationFeeCommand" = jackson-json
      "com.phoenix.portfolio.impl.applicationfee.ApplicationFeeEvent" = jackson-json
      "com.phoenix.portfolio.impl.applicationfee.ApplicationFeeState" = jackson-json
      "com.phoenix.portfolio.impl.actors.RenewalScheduler$Command" = jackson-json
      "com.phoenix.portfolio.impl.portfolio.PortfolioCommand" = jackson-json
      "com.phoenix.portfolio.impl.portfolio.PortfolioEvent" = jackson-json
      "com.phoenix.portfolio.impl.portfolio.PortfolioState" = jackson-json
      "com.phoenix.portfolio.impl.property.PropertyCommand" = jackson-json
      "com.phoenix.portfolio.impl.property.PropertyEvent" = jackson-json
      "com.phoenix.portfolio.impl.property.PropertyState" = jackson-json
    }
  }

  serialization.jackson {
    jackson-modules += "com.fasterxml.jackson.datatype.joda.JodaModule"
  }
}

cassandra-journal.keyspace = ${portfolio.cassandra.keyspace}
cassandra-snapshot-store.keyspace = ${portfolio.cassandra.keyspace}
lagom.persistence.read-side.cassandra.keyspace = ${portfolio.cassandra.keyspace}

lagom.persistence.read-side.global-prepare-timeout = 120s
lagom.broker.kafka.client.consumer.batching-size = 1

cassandra-journal.write-static-column-compat = off
cassandra-query-journal {
  write-static-column-compat = off
  first-time-bucket = "20190101T00:00"
}

# Enable new sharding state store mode by overriding Lagom's default
akka.cluster.sharding.state-store-mode = ddata

# Enable serializers provided in Akka 2.5.8+ to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done"                 = akka-misc
  "akka.actor.Address"        = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

akka.actor.allow-java-serialization=off

google {
  places {
    server-key = "AIzaSyD0UBLjCW3A3gm273Jz7gvaulcdKbYj96M"
  }
}

lightstone.api-key = ${?LIGHTSTONE_API_KEY}
lightstone.url = ${?LIGHTSTONE_URL}

readSidePostgres.username=${?PG_PHOENIX_USERNAME}
readSidePostgres.password=${?PG_PHOENIX_PASSWORD}
readSidePostgres.database=${?PG_PHOENIX_DATABASE}
readSidePostgres.port=${?PG_PHOENIX_PORT}
readSidePostgres.host=${?PG_PHOENIX_HOST}
readSidePostgres.maximumMessageSize=${?PG_PHOENIX_MAXMSG}
