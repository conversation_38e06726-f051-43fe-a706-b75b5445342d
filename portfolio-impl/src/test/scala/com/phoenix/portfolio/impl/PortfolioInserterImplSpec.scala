package com.phoenix.portfolio.impl

import akka.Done
import akka.stream.scaladsl.Sink
import com.phoenix.authentication.jwt.{AgencyMembership, UserSession}
import com.phoenix.common.data.ReadsideDatabase
import com.phoenix.date.DateUtcUtil
import com.phoenix.finance.Beneficiary.PartyBeneficiary
import com.phoenix.google.api.Address
import com.phoenix.lightstone.api.Request.PropertyPostRequest
import com.phoenix.party.api.AccountType
import com.phoenix.portfolio.api.Owners.OwnerParty
import com.phoenix.portfolio.api._
import com.phoenix.portfolio.api.property.request.PropertyCreation
import com.phoenix.portfolio.api.request.TerminationFields.TerminationReason
import com.phoenix.portfolio.api.request._
import com.phoenix.portfolio.impl.actors.RenewalScheduler
import com.phoenix.portfolio.impl.agencyportfolio.{AddAgency, AgencyPortfoliosEntity}
import com.phoenix.portfolio.impl.connectors.{Party<PERSON>ow, PgPortfolioSummaryConnector, PgPropertyConnector}
import com.phoenix.portfolio.impl.portfolio.{GetPortfolio, PortfolioEntity}
import com.phoenix.portfolio.impl.property.{GetProperty, PropertyEntity}
import com.phoenix.portfolio.impl.stubs.PlaceServiceStub
import com.phoenix.util
import com.phoenix.util.UUID
import org.joda.time.DateTime
import org.joda.time.format.ISODateTimeFormat
import org.scalatest.time.{Seconds, Span}

import scala.concurrent.{Await, Future}

class PortfolioInserterImplSpec extends BaseSpec {

  var propPGConnector: PgPropertyConnector = _
  var portfolioSummaryConnectorPG: PgPortfolioSummaryConnector = _
  val readside: ReadsideDatabase = new ReadsideDatabase("portfolio")

  private val ownerId = UUID.createV4
  private val ownerId2 = UUID.createV4
  private val tenantId = UUID.createV4
  private val contractTerms = ManagedTerms_v1(BigDecimal(1000), BigDecimal(1000.0), BigDecimal(5000),None, Some(FixedUndertaking_v1(BigDecimal(1000), false)),None, None)
  private val invoiceTemplate = InvoiceTemplateFields("Alarm", tenantId, AccountType.Tenant,"EndOfMonth", Some(BigDecimal(5000)), false, false,
    Seq(PaymentRule_v1(PartyBeneficiary(UUID.createV4, "Owner", "Commission", Some(BigDecimal(100)), Some(false), None), ClearanceContainer(FixedClearance_v1(BigDecimal(100), Some(false))))))
  private val invoiceTemplateUnmanagedLease = InvoiceTemplateFields("Rent", tenantId, AccountType.Tenant,"OnceOff", Some(BigDecimal(5000)), false, false,Seq())
  private val incorrectInvoiceTemplateUnmanagedLease = InvoiceTemplateFields("Rent", tenantId, AccountType.Tenant,"EndOfMonth", Some(BigDecimal(5000)), false, false,Seq())
  private val onceOffInvoiceTemplateUnmanagedLease = InvoiceTemplateFields("Rent", tenantId, AccountType.Tenant,"OnceOff", Some(BigDecimal(5000)), false, false,Seq())
  private val incorrectInvoiceTemplateUnmanagedLease2 = InvoiceTemplateFields("Damage Deposit", tenantId, AccountType.Tenant,"EndOfMonth", Some(BigDecimal(5000)), false, false,Seq())
  private lazy val schedulerRef = server.application.renewalScheduler.init
  protected val tokenTeamContent: UserSession =
    UserSession(UUID.createV4, Some(AgencyMembership.TeamMember(agencyId)), "Test", "<EMAIL>", Some("user"), totpSecret, None, None)

  var portfolioId: UUID = null
  var propertyId: UUID = null

  "PortfolioInserterImplSpec" when {
    "setup PG" in {
      eventually{
        Future(readside.migrate()).flatMap { result =>
          propPGConnector = new PgPropertyConnector(readside)
          portfolioSummaryConnectorPG = new PgPortfolioSummaryConnector(readside)
          assertResult(Done)(result)
        }
      }
    }

    "Portfolio connector" should {
      "portfolio connector find should return empty" in {
        eventually(timeout(Span(duration, Seconds))) {
          portfolioConnector.portfolio(UUID.createV4, UUID.createV4).flatMap{ res =>
            assert(res.isEmpty)
          }
        }
      }
    }

    "Party Creation" should {
      "create owner 1" in {
        server.application.partyConnector.insert(PartyRow(agencyId, ownerId, false, "Test Owner 1", "Test Owner 1", "<EMAIL>",
          None, None, None, None, None, None, None, None, None))
          .map(_ => assert(true))
      }
      "create owner 2" in {
        server.application.partyConnector.insert(PartyRow(agencyId, ownerId2, false, "Test Owner 2", "Test Owner 2", "<EMAIL>",
          None, None, None, None, None, None, None, None, None))
          .map(_ => assert(true))
      }
      "create tenant 1" in {
        server.application.partyConnector.insert(PartyRow(agencyId, tenantId, false, "Test Tenant 1", "Test Tenant 1", "<EMAIL>",
          None, None, None, None, None, None, None, None, None))
          .map(_ => assert(true))
      }
    }

    "Property Creation" should {
      "search for property" in {
        client.combinedSearchProperties("126 milford road")
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke()
          .map { f =>
            f.googleProperties.map(_.description) should ===(Seq("126 Milford Road, Plumstead, Cape Town"))
          }
      }
      "create new property" in {
        client.createProperty
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(PropertyCreation(Some(PlaceServiceStub.milfordPlaceId), None, Some("Test Building"), Some("4321")))
          .map { f =>
            propertyId = f.id
            f.addressComponents.find(_.types.contains(Address.StreetNumber())).map(_.longName) should ===(Some("126"))
            f.addressComponents.find(_.types.contains(Address.StreetName())).map(_.longName) should ===(Some("Milford Road"))
            f.addressComponents.find(_.types.contains(Address.SchemeNumber())).map(_.longName) should ===(None)
            f.addressComponents.find(_.types.contains(Address.SchemeName())).map(_.longName) should ===(None)
            f.addressComponents.find(_.types.contains(Address.BuildingName())).map(_.longName) should ===(Some("Test Building"))
            f.addressComponents.find(_.types.contains(Address.UnitNumber())).map(_.longName) should ===(Some("4321"))
          }
      }
      "eventually see property in readside table" in {
        eventually {
          val query = propertyConnector.findAll(agencyId)
            .filter(_.placeId.get == PlaceServiceStub.milfordPlaceId)
            .runWith(Sink.headOption)
          whenReady(query) { result => assert(result.isDefined) }
        }
      }
      "find and get newly saved property" in {
        client.combinedSearchProperties("126 milford road")
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke()
          .flatMap { f =>
            f.savedProperties.find(_.placeId.get == PlaceServiceStub.milfordPlaceId) match {
              case Some(result) =>
                persistentEntityRegistry.refFor[PropertyEntity](result.id.get).ask(GetProperty).map { response =>
                  assert(response.nonEmpty)
                  result.description should ===("4321 Test Building, 126 Milford Road, Plumstead, Cape Town")
                }
              case _ =>
                Future(assert(f.savedProperties.exists(_.placeId.get == PlaceServiceStub.milfordPlaceId)))
            }
          }
      }

      "Test lightstone service" in {
        val query = client.searchProperties("123 takalani Street")
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke()

        whenReady(query) { result =>
          result.properties.size should ===(1)
        }
      }
      "check 0 properties in postgres table" in {
        eventually {
          propertyPGConnector.getAll.flatMap{ v =>
            assert(v.nonEmpty)
          }
        }
      }
      "check property is in postgres table" in {
        eventually {
          propertyPGConnector.find(agencyId, propertyId).flatMap { result =>
            assert(result.isDefined)
          }
        }
      }
      "deactivate property in postgres" in {
        eventually {
          propertyPGConnector.markDeleted(agencyId, propertyId).flatMap { _ =>
            assert(true)
          }
        }
      }
      "check property in postgres is deactivated" in {
        eventually {
          propertyPGConnector.find(agencyId, propertyId).flatMap { result =>
            assert(result.isDefined)
            assert(result.get.deletedAt.isDefined)
          }
        }
      }
      val propertyPostRequestStub = PropertyPostRequest(
        maxRowsToReturn = Some(10),
        propertyType = Some("Residential"),
        streetNumber = Some("123"),
        streetName = Some("Main"),
        streetType = Some("Street"),
        estateName = Some("Estate"),
        suburb = Some("Suburb"),
        town = Some("Town"),
        municipality = Some("Municipality"),
        districtCouncil = Some("District Council"),
        province = Some("Province"),
        postCode = Some("12345"),
        deedsOfficeCode = Some("DO123"),
        township = Some("Township"),
        erfNumber = Some(123),
        portionNumber = Some(1),
        sectionalSchemeName = Some("Sectional Scheme"),
        sectionalSchemeYear = Some(2022),
        sectionalSchemeNumber = Some(1),
        sectionalSchemeUnitNumber = Some(1),
        registrationDivision = Some("RD123"),
        farmNumber = Some(123),
        farmName = Some("Farm"),
        holdingNumber = Some(1),
        holdingName = Some("Holding"),
        ownerName = Some("Owner"),
        ownerIdentifier = Some("ID123"),
        titleDeedNumber = Some("TD123"),
        bondNumber = Some("BN123"),
        rooftopCoordinatesLongitudeX = Some(40.7128),
        rooftopCoordinatesLatitudeY = Some(74.0060),
        radius = Some(1.0),
        discardNonUnique = true,
        highlights = true,
        explain = true,
        topLeftLat = Some(40.7128),
        topLeftLong = Some(74.0060),
        bottomRightLat = Some(40.7128),
        bottomRightLong = Some(74.0060)
      )

      "Test lightstone advanced search" in {
        val query = client.advancedSearchProperties()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(propertyPostRequestStub.copy(town = Some("fake town")))

        whenReady(query) { result =>
          result.properties.size should ===(1)
        }
      }
    }

    "Portfolio lifecycle" should {
      "add agency with lease ID format" in {
        persistentEntityRegistry.refFor[AgencyPortfoliosEntity](agencyId).ask(AddAgency("TEST", 0)).map { _ =>
          assert(true)
        }
      }
      "fail when opening portfolio with invalid request" in {
        val invalidRequest = OpenPortfolioFields(Some(UUID.createV4),
          Some(PropertyCreation(Some(PlaceServiceStub.dolphinPlaceId), None, Some("Test Building"), Some("1234"))),
          Some(UUID.createV4), "ManagedContract")
        val exception = recoverToExceptionIf[com.lightbend.lagom.scaladsl.api.transport.TransportException] {
          client.openPortfolio
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(invalidRequest)
        }
        exception.map {
          _.getMessage should ===("You can only include a propertyId or property object not both")
        }
      }
      "fail when opening portfolio with invalid lease type" in {
        val invalidRequest = OpenPortfolioFields(propertyId = Option.empty,
          Option.empty, Option.empty, "BogusContract")
        val exception = recoverToExceptionIf[com.lightbend.lagom.scaladsl.api.transport.TransportException] {
          client.openPortfolio
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(invalidRequest)
        }
        exception.map {
          _.getMessage should ===("{\"errors\":[{\"key\":\"leaseType\",\"message\":\"LeaseType is not valid. Lease " +
            "Type must be ManagedContract or UnmanagedContract.\"}]}")
        }
      }
      "open portfolio with new property" in {
        val validRequest = OpenPortfolioFields(propertyId = Option.empty,
          Some(PropertyCreation(Some(PlaceServiceStub.dolphinPlaceId), None, Some("Test Building"), Some("1234"))),
          Option.empty, "ManagedContract")
        client.openPortfolio
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(validRequest).flatMap { response =>
          portfolioId = UUID.fromString(response.portfolioId)
          portfolioConnector.portfolio(agencyId, portfolioId).map { portfolio =>
            propertyId = portfolio.map(x => UUID.fromUUID(x.propertyId)).orNull
            assert(portfolio.isDefined)
          }
        }
      }

      "eventually see new property in readside table" in {
        eventually {
          val query = propertyConnector.findAll(agencyId)
            .filter(_.placeId.get == PlaceServiceStub.dolphinPlaceId)
            .runWith(Sink.headOption)
          whenReady(query) { result => assert(result.isDefined) }
        }
      }

      "find and get property without combined search" in {
        client.combinedSearchProperties("15 dolphin drive")
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke()
          .map { f =>
            assert(f.savedProperties.exists(_.placeId.get == PlaceServiceStub.dolphinPlaceId))
          }
      }

      "Should create 1 entry on the readside" in {
        eventually {
          val q = portfolioConnector.find(agencyId)
            .filter(_.propertyId == propertyId.uuid)
            .runWith(Sink.seq)
          whenReady(q) { response =>
            response.nonEmpty should ===(true)
            response.last.propertyId should ===(propertyId.uuid)
            response.last.agencyId should ===(agencyId.uuid)
          }
        }
      }

      "Should retrieve a list of portfolios for a property" in {
        eventually {
          val query = client.getPropertyPortfolios(propertyId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke()
          whenReady(query) { response =>
            response.nonEmpty should ===(true)
            response.length should ===(1)
          }
        }
      }
      val maintext = "1234 Test Building, 15 Dolphin Drive"
      "find property in table" in {
        eventually {
          val query = propertyConnector.find(agencyId, UUID.fromString(propertyId))
          whenReady(query) { result =>
            assert(result.map(_.mainText).contains(maintext))
          }
        }
      }

      "Owner Added" in {
        for {
          _ <- client.updateOwnerParties(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(Owners(DateUtcUtil.now(), Some(Owners.OwnerParty(ownerId)), Seq.empty))

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.get.landlord.length should ===(1)
              response.get.landlord.head.partyId should ===(ownerId.uuid.toString)
            }
          }
        } yield assert
      }

      "Tenant Added" in {
        for {
          _ <- client
            .updateTenantParties(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(Tenants(DateTime.now(), Some(Tenants.TenantParty(tenantId)), Seq.empty))

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.get.tenants.length should ===(1)
              response.get.tenants.head.isPrimary should ===(true)
              response.get.tenants.head.partyId should ===(tenantId.uuid.toString)
            }
          }
        } yield assert
      }
      "ContractAmended" in {
        for {
          _ <- client.amendContractV2(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(ContractContainer(ManagedContract_v1(contractTerms)))

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.get.contractContainer should ===(Some(ContractContainer(ManagedContract_v1(contractTerms))))
            }
          }
        } yield assert
      }
      "InvoiceTemplateAdded" in {
        for {
          _ <- client.createInvoiceTemplate(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(invoiceTemplate)

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.get.invoiceTemplates.length should ===(5)
              response.get.invoiceTemplates.last.category should ===("Alarm")
              response.get.invoiceTemplates.last.netAmount should ===(invoiceTemplate.netAmount)
            }
          }
        } yield assert
      }
      "Lease terms Added" in {
        val leaseTerms = LeaseTermsFields_v1("2019-01-01", "2020-01-01", true)
        for {
          _ <- client.addLeaseTerms(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(leaseTerms)

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.nonEmpty should ===(true)
              response.get.leaseTerms should ===(Some(leaseTerms))
            }
          }
        } yield assert
      }
      "AgentAdded" in {
        val agents = Agent_v1(UUID.toString(userId), Some(UUID.toString(userId)))
        for {
          _ <- client.addAgent(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(AgentFields(agents))

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.nonEmpty should ===(true)
              response.get.agents.length should ===(1)
              response.get.agents.last should ===(agents)
            }
          }
        } yield assert
      }
      "SettingsAmended" in {
        val settings = SettingsFields("", "123", true, true, None)
        for {
          _ <- client.amendSettings(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(settings)

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.nonEmpty should ===(true)
              response.get.settings should ===(Some(Settings_v1(settings.notes, settings.customLeaseIdentifier,
                settings.autoSendPaymentReminderSms, settings.autoSendOwnerMonthlyEmail)))
            }
          }
        } yield assert
      }

      "MetaDataAmended" in {
        val md = PortfolioMetaData("Lease ID meta data: 1234")
        for {
          assert <- eventually {
            val res = client.amendMetaData(portfolioId)
              .handleRequestHeader(auth.authenticatedHandler(tokenContent))
              .invoke(md)

            whenReady(res) { response =>
              response.metaData should ===(Some(md.data))
            }
          }
        } yield assert
      }

      "MetaDataAmended and portfolio table updated" in {
        val md = PortfolioMetaData("Lease ID meta data: 1234")
        for {
          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.get.metaData should ===(Some(md.data))
            }
          }
        } yield assert
      }

      "MetaDataAmended and portfolio summary table updated" in {
        val md = PortfolioMetaData("Lease ID meta data: 1234")
        for {
          assert <- eventually {
            portfolioSummaryConnector.find(agencyId).runWith(Sink.seq)
              .map(_.find(x => x.portfolioId.toString == x.portfolioId.toString).map { summary =>
                    summary.metaData should ===(Some(md.data))
                  })
          }
        } yield assert.get
      }

      "confirm PG portfolio summary table updated" in {
        val md = PortfolioMetaData("Lease ID meta data: 1234")
        for {
          assert <- eventually {
            portfolioSummaryConnectorPG.findByAgency(agencyId)
              .map(_.find(x => x.portfolioId.toString == x.portfolioId.toString).map { summary =>
                summary.metaData should ===(Some(md.data))
              })
          }
        } yield assert.get
      }

      "confirm party_portfolio" in {
        for {
          assert <- eventually {
            pgPortfolioConnector.findPartyPortfoliosByPortfolio(portfolioId).map(_.find(x => x.portfolioId.toString == x.portfolioId.toString))
              .map { summary =>
                summary.isDefined should ===(true)
              }
          }
        } yield assert
      }

      "fail when amending commission with invalid agent party UUID" in {
        val invalidRequest = CommissionSplit_v1(Some(CommissionSetting(UndertakingContainer(FixedUndertaking_v1(BigDecimal(500), false)),
          Some(List(AgentSplit("invaild-uuid", 100.0)))
        )), None)
        val exception = recoverToExceptionIf[com.lightbend.lagom.scaladsl.api.transport.TransportException] {
          client.amendCommissionV2(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(invalidRequest)
        }
        exception.map {
          _.getMessage should ===("{\"errors\":[{\"key\":\"managementFee\",\"message\":\"is invalid\"}]}")
        }
      }
      "CommissionAmended" in {
        val commissionSplit = CommissionSplit_v1(Some(CommissionSetting(UndertakingContainer(FixedUndertaking_v1(BigDecimal(500), false)),
          Some(List(AgentSplit(UUID.toString(userId), 100.0)))
        )), None)
        for {
          _ <- client.amendCommissionV2(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(commissionSplit)

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.nonEmpty should ===(true)
              response.get.commission should ===(Some(commissionSplit))
            }
          }
        } yield assert
      }

      "Search properties available via API call (before approved)" in {
        eventually {
          val query = client.getProperties
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke()

          whenReady(query) { result =>
            result.length should ===(1)
            result.last.tag.get("contract") should ===(Some("inactive"))
            result.last.tag.get("status") should ===(Some("in progress"))
          }
        }
      }

      "Get portfolios available via API call (before approved)" in {
        eventually {
          client.getPortfolios
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke().flatMap { result =>
            result.length should ===(1)
            result.last.portfolios.length should ===(1)
            result.last.mainText should ===(maintext)
            result.last.secondaryText should ===("Table View, Cape Town")
            result.last.portfolios.last.tags.get("contract") should ===(Some("inactive"))
            result.last.portfolios.last.tags.get("status") should ===(Some("in progress"))

          }
        }
      }

      "Get getPortfoliosByAgency via API call" in {
        eventually {
          client.getPortfoliosByAgency(UUID.toString(agencyId))
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke().flatMap { result =>
            result.length should ===(1)
          }
        }
      }

      "fail when amending property with invalid request" in {
        val invalidRequest = PortfolioPropertyFields(Some(UUID.createV4),
          Some(PropertyCreation(Some(PlaceServiceStub.dolphinPlaceId), None, Some("Test Building"), Some("1234"))),
          Option.empty)
        recoverToExceptionIf[com.lightbend.lagom.scaladsl.api.transport.TransportException] {
            client.amendProperty(portfolioId)
              .handleRequestHeader(auth.authenticatedHandler(tokenContent))
              .invoke(invalidRequest)
          }
          .map {
            _.getMessage should ===("You can only include a propertyId or property object not both")
          }
      }

      "PropertyAmended" in {
        for {
          propertyId <- propertyConnector.findAll(agencyId)
            .filter(_.placeId.get == PlaceServiceStub.milfordPlaceId)
            .runWith(Sink.head)
            .map(_.propertyId)
          fields = PortfolioPropertyFields(Some(propertyId.toString), Option.empty, Option.empty)
          _ <- client.amendPropertyV2(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(fields)

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.nonEmpty should ===(true)
              response.get.propertyId should ===(propertyId)
              response.get.landlord.head.partyId should ===(ownerId.uuid.toString)
            }
          }
        } yield assert
      }

      "PortfolioApproved" in {
        for {
          _ <- client.approve(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(ApprovalFields(userId, "", Some("")))

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.nonEmpty should ===(true)
            }
          }
        } yield assert
      }

      "Search properties available via API call" in {
        eventually {
          val query = client.getProperties
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke()

          whenReady(query) { result =>
            result.length should ===(1)
            //            result.last.tag should ===(Map("contract" -> "inactive", "status" -> "approved"))
          }
        }
      }

      val maintext2 = "4321 Test Building, 126 Milford Road"
      "Get portfolios available via API call" in {
        eventually {
          client.getPortfolios
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke().flatMap { result =>
            result.length should ===(1)
            result.last.portfolios.length should ===(1)
            result.last.mainText should ===(maintext2)
            result.last.secondaryText should ===("Plumstead, Cape Town")//Making sure the secondaryText = Address.Sublocality(), Address.Locality()
            result.last.portfolios.last.tags.get("contract") should ===(Some("inactive"))
            result.last.portfolios.last.tags.get("status") should ===(Some("approved"))
          }
        }
      }

      "Create renewal in the future" in {
        val commissionSplit = CommissionSplit_v1(Some(
          CommissionSetting(
            UndertakingContainer(FixedUndertaking_v1(BigDecimal(1000), vatable = false)),
            Some(List(AgentSplit(util.UUID.toString(agencyId), 1.0))))
        ), None)
        val term = LeaseTermsFields_v1("2020-01-01", "2021-01-01", rolloverMonthToMonth = true)
        val renewal = Renewal(BigDecimal(11000), commissionSplit, BigDecimal(500), DateUtcUtil.now().plusYears(1).toLocalDate, term, Option.empty)
        for {
          resp <- client.renew(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(renewal)
        } yield assert(true)
      }

      "See renewal in readside table" in {
        val date = DateUtcUtil.now().plusYears(1).toString(ISODateTimeFormat.yearMonthDay().withZoneUTC())
        eventually {
          val query = portfolioConnector.findLeaseRenewals(date)
            .runWith(Sink.seq)

          whenReady(query) { result =>
            result.length should ===(1)
            result.headOption.map(_.date) should ===(Some(date))
          }
        }
      }

      "Get portfolios returns renewal" in {
        eventually {
          client.getPortfolios
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke().flatMap { result =>
            result.length should ===(1)
            result.last.portfolios.length should ===(1)
            result.last.mainText should ===(maintext2)
            result.last.secondaryText should ===("Plumstead, Cape Town")
            result.last.portfolios.last.tags.get("contract") should ===(Some("inactive"))
            result.last.portfolios.last.tags.get("status") should ===(Some("approved"))
            result.last.portfolios.head.leaseTerms.map(_.startDate) should ===(Some("2019-01-01"))
            result.last.portfolios.head.leaseTerms.map(_.endDate) should ===(Some("2020-01-01"))
            result.last.portfolios.head.renewal.map(_.leaseRenewsAt) should ===(Some(DateUtcUtil.now().plusYears(1).toLocalDate))
          }
        }
      }

      "Tick scheduler to renew portfolio in the future" in {
        schedulerRef ! RenewalScheduler.RenewPendingLeases(DateUtcUtil.now().plusYears(1).withTime(8, 0, 0, 0).getMillis)
        eventually {
          client.getPortfolios
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke().flatMap { result =>
            result.length should ===(1)
            result.last.portfolios.length should ===(1)
            result.last.mainText should ===(maintext2)
            result.last.secondaryText should ===("Plumstead, Cape Town")
            result.last.portfolios.head.leaseTerms.map(_.startDate) should ===(Some("2020-01-01"))
            result.last.portfolios.head.leaseTerms.map(_.endDate) should ===(Some("2021-01-01"))
            //            result.last.portfolios.last.tags should ===(Map("contract" -> "inactive", "status" -> "approved"))
            result.last.portfolios.head.renewal should ===(Option.empty[Renewal])
          }
        }
      }

      "fail when opening portfolio with invalid LeaseType" in {
        val invalidRequest = OpenPortfolioFields(None,
          Some(PropertyCreation(Some(PlaceServiceStub.dolphinPlaceId), None, Some("Test Building 2"), Some("12345"))),
          Some(UUID.createV4), "OopsNOTManaged")

        val exception = recoverToExceptionIf[com.lightbend.lagom.scaladsl.api.transport.Forbidden] {
          client.openPortfolio
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(invalidRequest)
        }
        exception.map { x =>
          assert(x.getMessage.split(":")(3) contains ("LeaseType is not valid. Lease Type must be ManagedContract or UnmanagedContract."))
        }

      }

      var newPortfolio = ""
      "open portfolio with unmanaged lease but OnceOff" in {
        val validRequest = OpenPortfolioFields(None,
          Some(PropertyCreation(Some(PlaceServiceStub.dolphinPlaceId), None, Some("Test Building 3"), Some("123456"))),
          Option.empty, UnmanagedContract_v1.NAME, metaData = Some("Lease ID meta data: Test1"))

        client.openPortfolio
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(validRequest).flatMap { response =>
          newPortfolio = response.portfolioId
          assert(response.portfolioId.nonEmpty)
        }
      }

      "confirm opened portfolio has MetaData set" in {
        for {
          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, UUID.fromString(newPortfolio))
            whenReady(query) { p =>
              assert(p.isDefined)
              assert(p.get.metaData.contains("Lease ID meta data: Test1"))
            }
          }
        } yield assert
      }

      "Owner Added to new portfolio" in {
        for {
          _ <- client.updateOwnerParties(newPortfolio.toString)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(Owners(DateUtcUtil.now(), Some(Owners.OwnerParty(ownerId2)), Seq.empty))

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, UUID.fromString(newPortfolio))
            whenReady(query) { response =>
              response.get.landlord.length should ===(1)
              assert(response.get.landlord.seq.map(x => x.partyId).contains(ownerId2.uuid.toString))
            }
          }
        } yield assert
      }

      var invoiceId = ""
      "create newInvoiceTemplate with OnceOff" in {
         for{
          validRequest <- client.createInvoiceTemplate(newPortfolio)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(invoiceTemplateUnmanagedLease)

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, UUID.fromString(newPortfolio))
            whenReady(query) { response =>
              invoiceId = response.get.invoiceTemplates.last.id
              response.get.invoiceTemplates.length should === (1)
              response.get.invoiceTemplates.last.interval should === ("OnceOff")
            }
          }
         } yield assert
      }

      "create newInvoiceTemplate with same Category and PartyId with OnceOff" in {
        for {
          _ <- client.createInvoiceTemplate(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(onceOffInvoiceTemplateUnmanagedLease)

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            var count = 0
            whenReady(query) { response =>
              response.get.invoiceTemplates.length should ===(7)
              response.get.invoiceTemplates.last.category should ===("Rent")
              response.get.invoiceTemplates.last.netAmount should ===(onceOffInvoiceTemplateUnmanagedLease.netAmount)
              response.get.invoiceTemplates.last.interval should ===("OnceOff")
            }
          }
        } yield assert
      }

      "create another newInvoiceTemplate with same Category and PartyId then catch error" in {
        val exception = recoverToExceptionIf[com.lightbend.lagom.scaladsl.api.transport.TransportException] {
          client.createInvoiceTemplate(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(onceOffInvoiceTemplateUnmanagedLease)
        }
        exception.map { x =>
          assert(x.getMessage contains ("A template with the same category and InvoicePartyId already exists"))
        }
      }

      "create newInvoiceTemplate with same Category and PartyId with another OnceOff" in {
        val exception = recoverToExceptionIf[com.lightbend.lagom.scaladsl.api.transport.TransportException] {
          client.createInvoiceTemplateV2(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(onceOffInvoiceTemplateUnmanagedLease)
        }
        exception.map { x =>
          assert(x.getMessage contains ("A template with the same category and InvoicePartyId already exists"))
        }
      }

      "create newInvoiceTemplate without OnceOff and catch error" in {
        val response = client.createInvoiceTemplate(newPortfolio)
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(incorrectInvoiceTemplateUnmanagedLease2)

        eventually(timeout(Span(duration, Seconds))) {
          whenReady(response.failed) { e =>
            val message = e.getMessage
            e.getMessage should ===("{\"errors\":[{\"key\":\"general\",\"message\":\"UnmanagedLease must have an interval set to OnceOff.\"}]}")
            e shouldBe a[com.lightbend.lagom.scaladsl.api.transport.Forbidden]
          }
        }
      }

      "Update invoice template" in {
          val response = client.updateInvoiceTemplate(newPortfolio, invoiceId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(incorrectInvoiceTemplateUnmanagedLease)

          eventually(timeout(Span(duration, Seconds))) {
            whenReady(response.failed) { e =>
              val message = e.getMessage
              e.getMessage should ===("{\"errors\":[{\"key\":\"general\",\"message\":\"UnmanagedLease must have an interval set to OnceOff.\"}]}")
              e shouldBe a[com.lightbend.lagom.scaladsl.api.transport.Forbidden]
            }
          }
      }

      "fetch termination reasons" in {
        val response = client.terminationReasons()
          .handleRequestHeader(auth.authenticatedHandler(tokenTeamContent))
          .invoke()

        eventually(timeout(Span(duration, Seconds))) {
          whenReady(response) { e =>
            e.size should ===(TerminationFields.TerminationReason.all.size)
          }
        }
      }

      "terminate lease with a non owner role and catch error" in {
        val fields = TerminationFields(DateUtcUtil.now().withDate(2019, 5, 5).toLocalDate,
          TerminationFields.TerminationReason.Mutual, understandAction = true, releaseDeposit = Some(true), None)
        val response = client.terminateLease(portfolioId)
          .handleRequestHeader(auth.authenticatedHandler(tokenTeamContent))
          .invoke(fields)

        eventually(timeout(Span(duration, Seconds))) {
          whenReady(response.failed) { e =>
            e.getMessage should ===("""{"name":"Forbidden","detail":"Not authorized: Owner role required"}""")
            e shouldBe a[com.lightbend.lagom.scaladsl.api.transport.TransportException]
          }
        }
      }

      val terminationDate = DateUtcUtil.now().withDate(2019, 5, 5).toLocalDate
      "terminate lease with a non owner role but do not release deposit" in {
        val fields = TerminationFields(DateUtcUtil.now().withDate(2019, 5, 5).toLocalDate,
          TerminationFields.TerminationReason.Mutual, understandAction = true, releaseDeposit = Some(false), Some(terminationDate))
        val response = client.terminateLease(portfolioId)
          .handleRequestHeader(auth.authenticatedHandler(tokenTeamContent))
          .invoke(fields)

        eventually(timeout(Span(duration, Seconds))) {
          whenReady(response) { e =>
            e should ===(Done)
          }
        }
      }

      "Check terminationDate is set" in {
        persistentEntityRegistry.refFor[PortfolioEntity](portfolioId).ask(GetPortfolio).map { response =>
          assert(response.nonEmpty)
          response.get.portfolio.terminationFields.get.terminatedAt should ===(Some(terminationDate))
        }
      }

      "see portfolio terminated" in {
        val response = client.getPortfolio(portfolioId)
          .handleRequestHeader(auth.authenticatedHandler(tokenTeamContent))
          .invoke()

        eventually(timeout(Span(duration, Seconds))) {
          whenReady(response) { e =>
            e.terminatedReason should ===(Some(TerminationReason.Mutual))
          }
        }
      }


    }

    "Test searching and using property already used on a lease" should {
      "open portfolio with new property" in {
        val validRequest = OpenPortfolioFields(propertyId = Option.empty,
          Some(PropertyCreation(Some(PlaceServiceStub.dolphinPlaceId), None, Some("Test Building"), Some("A1"))),
          Option.empty, "ManagedContract")
        client.openPortfolio
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(validRequest).flatMap { response =>
          portfolioId = UUID.fromString(response.portfolioId)
          portfolioConnector.portfolio(agencyId, portfolioId).map { portfolio =>
            propertyId = portfolio.map(x => UUID.fromUUID(x.propertyId)).orNull
            assert(portfolio.isDefined)
          }
        }
      }

      "find property in table" in {
        eventually {
          val query = propertyConnector.find(agencyId, UUID.fromString(propertyId))
          whenReady(query) { result =>
            assert(result.map(_.mainText).contains("A1 Test Building, 15 Dolphin Drive"))
          }
        }
      }

      "Owner Added" in {
        for {
          _ <- client
            .updateOwnerParties(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(Owners(DateTime.now(), Some(OwnerParty(ownerId.uuid.toString)), Seq.empty))

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.get.landlord.length should ===(1)
              response.get.landlord.head.partyId should ===(ownerId.uuid.toString)
            }
          }
        } yield assert
      }

      "Tenant Added" in {
        for {
          _ <- client
            .updateTenantParties(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(Tenants(DateTime.now(), Some(Tenants.TenantParty(tenantId)), Seq.empty))

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.get.tenants.length should ===(1)
              response.get.tenants.head.isPrimary should ===(true)
              response.get.tenants.head.partyId should ===(tenantId.uuid.toString)
            }
          }
        } yield assert
      }

      "Lease terms Added" in {
        val leaseTerms = LeaseTermsFields_v1("2019-01-01", "2050-01-01", true)
        for {
          _ <- client.addLeaseTerms(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(leaseTerms)

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.nonEmpty should ===(true)
              response.get.leaseTerms should ===(Some(leaseTerms))
            }
          }
        } yield assert
      }

      "PortfolioApproved" in {
        for {
          _ <- client.approve(portfolioId)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(ApprovalFields(userId, "", Some("")))

          assert <- eventually {
            val query = portfolioConnector.portfolio(agencyId, portfolioId)
            whenReady(query) { response =>
              response.nonEmpty should ===(true)
            }
          }
        } yield assert
      }
    }
  }
  "Test lease endpoint" when {
    "Get portfolio status'" in {
      eventually {
        val query = client.leaseStatus
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(Seq(portfolioId))

        whenReady(query) { result =>
          result.length should ===(1)
          result(0).portfolioId.toString should ===(UUID.toString(portfolioId))
          result(0).status should ===(Some("Active"))
        }
      }
    }

    var portfolioId2: UUID = null
    "open portfolio with new property" in {
      val validRequest = OpenPortfolioFields(propertyId = Option.empty,
        Some(PropertyCreation(Some(PlaceServiceStub.dolphinPlaceId), None, Some("Test Building 2"), Some("A2"))),
        Option.empty, "ManagedContract")
      client.openPortfolioV2
        .handleRequestHeader(auth.authenticatedHandler(tokenContent))
        .invoke(validRequest).flatMap { response =>
        portfolioId2 = UUID.fromString(response.id)
        portfolioConnector.portfolio(agencyId, portfolioId2).map { portfolio =>
          propertyId = portfolio.map(x => UUID.fromUUID(x.propertyId)).orNull
          assert(portfolio.isDefined)
        }
      }
    }
    "Get portfolio2 status'" in {
      eventually {
        val query = client.leaseStatus
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(Seq(portfolioId2))

        whenReady(query) { result =>
          result.length should ===(1)
          result(0).portfolioId.toString should ===(UUID.toString(portfolioId2))
          result(0).status should ===(Some("Draft"))
        }
      }
    }

  }
}
