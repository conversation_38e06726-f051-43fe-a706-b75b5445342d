-- Create pending_payments table for PostgreSQL
CREATE TABLE IF NOT EXISTS pending_payments (
    payment_id UUID PRIMARY KEY,
    to_party_id UUID NOT NULL,
    invoice_id UUID NOT NULL,
    agency_id UUID NOT NULL,
    user_id UUID,
    portfolio_id UUID,
    from_party_id UUID NOT NULL,
    to_party_tag VARCHAR(50) NOT NULL,
    gross_amount DECIMAL(19,2) NOT NULL,
    is_vat BOOLEAN NOT NULL DEFAULT FALSE,
    reference VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    beneficiary_id UUID
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_pending_payments_agency_id ON pending_payments(agency_id);
CREATE INDEX IF NOT EXISTS idx_pending_payments_invoice_id ON pending_payments(invoice_id);
CREATE INDEX IF NOT EXISTS idx_pending_payments_portfolio_id ON pending_payments(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_pending_payments_created_at ON pending_payments(created_at);

-- Create agency_recent_owner_payments table
CREATE TABLE IF NOT EXISTS agency_recent_owner_payments (
    agency_id UUID NOT NULL,
    lease_id UUID NOT NULL,
    payment_date TIMESTAMP NOT NULL,
    invoice_due_date VARCHAR(255) NOT NULL,
    payment_id UUID NOT NULL,
    party_id UUID NOT NULL,
    account_id UUID NOT NULL,
    PRIMARY KEY (agency_id, lease_id, payment_id)
);

-- Create indexes for agency_recent_owner_payments
CREATE INDEX IF NOT EXISTS idx_agency_recent_owner_payments_payment_id ON agency_recent_owner_payments(payment_id);
CREATE INDEX IF NOT EXISTS idx_agency_recent_owner_payments_party_id ON agency_recent_owner_payments(party_id);

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    payment_id UUID PRIMARY KEY,
    created_at TIMESTAMP NOT NULL,
    agency_id UUID,
    party_id UUID,
    lease_id UUID,
    to_party_tag VARCHAR(50) NOT NULL,
    owner_account_id UUID,
    invoice_id UUID,
    invoice_party_id UUID,
    entity_ref VARCHAR(255),
    was_manually_corrected BOOLEAN
);

-- Create indexes for payments table
CREATE INDEX IF NOT EXISTS idx_payments_agency_id ON payments(agency_id);
CREATE INDEX IF NOT EXISTS idx_payments_party_id ON payments(party_id);
CREATE INDEX IF NOT EXISTS idx_payments_lease_id ON payments(lease_id);
CREATE INDEX IF NOT EXISTS idx_payments_invoice_id ON payments(invoice_id);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- Create legacy_recon_payment_beneficiaries table
CREATE TABLE IF NOT EXISTS legacy_recon_payment_beneficiaries (
    payment_id UUID PRIMARY KEY,
    beneficiary_id UUID NOT NULL
);

-- Create index for legacy_recon_payment_beneficiaries
CREATE INDEX IF NOT EXISTS idx_legacy_recon_payment_beneficiaries_beneficiary_id ON legacy_recon_payment_beneficiaries(beneficiary_id);
