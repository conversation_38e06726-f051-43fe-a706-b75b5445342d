-- auto-generated definition
CREATE TABLE report_commission (
    agency_id                 UUID,
    year                      INT,
    month                     INT,
    asset                     TEXT,
    agent_id                  TEXT,
    created_at                TIMESTAMP,
    payment_id                UUID,
    agent_name                TEXT,
    agent_split               DOUBLE PRECISION,
    meta_data                 TEXT,
    main_text                 TEXT,
    secondary_text            TEXT,
    gross_commission          DECIMAL,
    gross_payment_rule_amount DECIMAL,
    invoice_amount            DECIMAL,
    invoice_date              TIMESTAMP,
    invoice_id                UUID,
    invoice_name              TEXT,
    invoice_type              TEXT,
    lease_id                  UUID,
    net_commission            DECIMAL,
    net_invoice_amount        DECIMAL,
    net_percent_invoice       DECIMAL,
    payment_amount            DECIMAL,
    royalty_amount            DECIMAL,
    vat_amount                DECIMAL,
    PRIMARY KEY (agency_id, year, month, asset, agent_id, created_at, payment_id)
);
CREATE INDEX idx_account_statement_agency_id
    ON account_statement (agency_id);
