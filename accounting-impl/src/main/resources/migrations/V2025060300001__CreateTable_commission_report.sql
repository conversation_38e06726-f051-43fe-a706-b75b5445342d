CREATE TABLE IF NOT EXISTS account_statement
(
    account_type  varchar(20),
    agency_id     uuid,
    account_id    uuid,
    created_at    timestamptz,
    source_id     varchar(80),
    asset         varchar(80),
    credit_amount decimal,
    debit_amount  decimal,
    party_id      uuid,
    precision     int,
    reference     varchar(160),
    remittance_at timestamptz,
    source_type   varchar(80),
    primary key (agency_id, account_id, created_at)
);
CREATE INDEX idx_account_statement_agency_id
    ON account_statement (agency_id);
CREATE INDEX idx_account_statement_account_id
    ON account_statement (account_id);
CREATE INDEX idx_account_statement_party_id
    ON account_statement (party_id);
CREATE INDEX idx_account_statement_reference
    ON account_statement (reference);
