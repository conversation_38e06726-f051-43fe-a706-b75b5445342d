#play.crypto.secret is deprecated, use play.http.secret.key instead
play.http.secret.key = ${?APPLICATION_SECRET}
play.application.loader = com.phoenix.accounting.impl.AccountingLoader

accounting.cassandra.keyspace = accounting
jwt.issuer = accounting

sentry-dsn = ${?SENTRY_DSN}

statementDeliverySchedulerDelay=10s

akka.actor.enable-additional-serialization-bindings = on

cassandra-journal.keyspace = ${accounting.cassandra.keyspace}
cassandra-snapshot-store.keyspace = ${accounting.cassandra.keyspace}
lagom.persistence.read-side.cassandra.keyspace = ${accounting.cassandra.keyspace}

lagom.persistence.read-side.global-prepare-timeout = 120s
lagom.broker.kafka.client.consumer.batching-size = 1

cassandra-journal.write-static-column-compat = off
cassandra-query-journal {
  write-static-column-compat = off
  first-time-bucket = "20190101T00:00"
}

lagom.persistence.ask-timeout=60s
billingSchedulerDelay=1h

akka {
  actor {
    enable-additional-serialization-bindings = on

    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      jackson-cbor = "akka.serialization.jackson.JacksonCborSerializer"
    }

    serialization-bindings {
      "com.phoenix.accounting.impl.actors.BillingScheduler$Command" = jackson-json
      "com.phoenix.accounting.impl.commands.PartyAccountingCommands" = jackson-json
      "com.phoenix.accounting.impl.commands.BatchFeedCommand" = jackson-json
      "com.phoenix.accounting.impl.events.PartyAccountingEvent" = jackson-json
      "com.phoenix.accounting.impl.events.BatchFeedEvent" = jackson-json
      "com.phoenix.accounting.impl.PartyAccountingState" = jackson-json
      "com.phoenix.accounting.impl.BatchFeedState" = jackson-json
    }
  }

  serialization.jackson {
    jackson-modules += "com.fasterxml.jackson.datatype.joda.JodaModule"
  }
}

# Enable new sharding state store mode by overriding Lagom's default
akka.cluster.sharding.state-store-mode = ddata

# Enable serializers provided in Akka 2.5.8+ to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done"                 = akka-misc
  "akka.actor.Address"        = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

akka.actor.allow-java-serialization=off

lagom.circuit-breaker {
  default {
    exception-whitelist = [
      "com.lightbend.lagom.scaladsl.api.transport.NotFound"
    ]
  }
  pdf-api {
    call-timeout = 57s
    reset-timeout = 10s
  }
}

phoenix.templates {
    customer-statement = "resource:/customer-statement.mustache"
    bank-statement = "resource:/bank-statement.mustache"
    lease-statement = "resource:/lease-statement.mustache"
    deposit-statement = "resource:/deposit-statement.mustache"
    deposit-bank-statement = "resource:/deposit-bank-statement.mustache"
    commission-statement = "resource:/commission-statement.mustache"
    receipt-fee-report = "resource:/receipt-fee-report.mustache"
    property-tax-statement = "resource:/property-tax-statement.mustache"
}

readSidePostgres.username=${?PG_PHOENIX_USERNAME}
readSidePostgres.password=${?PG_PHOENIX_PASSWORD}
readSidePostgres.database=${?PG_PHOENIX_DATABASE}
readSidePostgres.port=${?PG_PHOENIX_PORT}
readSidePostgres.host=${?PG_PHOENIX_HOST}
readSidePostgres.maximumMessageSize=${?PG_PHOENIX_MAXMSG}

slack.token = ${?SLACK_TOKEN}

api-pdf.username=${?PDF_API_USER}
api-pdf.password=${?PDF_API_PASSWORD}
