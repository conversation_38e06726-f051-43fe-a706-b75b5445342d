package com.phoenix.accounting.impl.connectors

import akka.Done
import com.github.jasync.sql.db.RowData
import com.phoenix.accounting.impl.PendingPayment
import com.phoenix.common.data.{ReadsideConnector, ReadsideDatabase}
import com.phoenix.party.api.PartyTag
import com.phoenix.util.UUID
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.DateTime

import java.util.{UUID => JavaUUID}
import scala.concurrent.{ExecutionContext, Future}
import scala.language.implicitConversions

class PgPaymentConnector(readsideDatabase: ReadsideDatabase)(implicit ec: ExecutionContext)
  extends ReadsideConnector(readsideDatabase.getConnectionPool) with LazyLogging {

  def insert(pendingPayment: PendingPayment): Future[Done] = {
    val query = """
      |INSERT INTO pending_payments (
      |  payment_id, to_party_id, invoice_id, agency_id, user_id, portfolio_id, from_party_id,
      |  to_party_tag, gross_amount, is_vat, reference, created_at, beneficiary_id
      |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      |ON CONFLICT (payment_id) DO UPDATE SET
      |  to_party_id = EXCLUDED.to_party_id,
      |  invoice_id = EXCLUDED.invoice_id,
      |  agency_id = EXCLUDED.agency_id,
      |  user_id = EXCLUDED.user_id,
      |  portfolio_id = EXCLUDED.portfolio_id,
      |  from_party_id = EXCLUDED.from_party_id,
      |  to_party_tag = EXCLUDED.to_party_tag,
      |  gross_amount = EXCLUDED.gross_amount,
      |  is_vat = EXCLUDED.is_vat,
      |  reference = EXCLUDED.reference,
      |  created_at = EXCLUDED.created_at,
      |  beneficiary_id = EXCLUDED.beneficiary_id
    """.stripMargin

    val values = List(
      pendingPayment.paymentId.uuid,
      pendingPayment.toPartyId.uuid,
      pendingPayment.invoiceId.uuid,
      pendingPayment.agencyId.uuid,
      pendingPayment.userId.map(_.uuid).orNull,
      pendingPayment.portfolioId.map(_.uuid).orNull,
      pendingPayment.fromPartyId.uuid,
      pendingPayment.toPartyTag.getObjectName,
      pendingPayment.grossAmount,
      pendingPayment.isVat,
      pendingPayment.reference,
      timestampFromDateTime(pendingPayment.createdAt),
      pendingPayment.beneficiaryId.orNull
    )

    query.execute(values).failOnUnaffected(s"Insert into pending_payments failed for paymentId: ${pendingPayment.paymentId}")
  }

  def findPendingPayment(paymentId: UUID): Future[Option[PendingPayment]] = {
    val query = "SELECT * FROM pending_payments WHERE payment_id = ?"
    
    query.execute(List(paymentId.uuid)).mapSingle(Convert.fromRow).recover {
      case ex: Exception =>
        logger.error(s"Error finding pending payment with paymentId: $paymentId", ex)
        None
    }
  }

  def insertRecentOwnerPayment(recentOwnerPayment: AgencyRecentOwnerPayment): Future[Done] = {
    val query = """
      |INSERT INTO agency_recent_owner_payments (
      |  agency_id, lease_id, payment_date, invoice_due_date, payment_id, party_id, account_id
      |) VALUES (?, ?, ?, ?, ?, ?, ?)
      |ON CONFLICT (agency_id, lease_id, payment_id) DO UPDATE SET
      |  payment_date = EXCLUDED.payment_date,
      |  invoice_due_date = EXCLUDED.invoice_due_date,
      |  party_id = EXCLUDED.party_id,
      |  account_id = EXCLUDED.account_id
    """.stripMargin

    val values = List(
      recentOwnerPayment.agencyId.uuid,
      recentOwnerPayment.leaseId.uuid,
      timestampFromDateTime(recentOwnerPayment.paymentDate),
      recentOwnerPayment.invoiceDueDate,
      recentOwnerPayment.paymentId.uuid,
      recentOwnerPayment.partyId.uuid,
      recentOwnerPayment.accountId.uuid
    )

    query.execute(values).failOnUnaffected(s"Insert into agency_recent_owner_payments failed for agencyId: ${recentOwnerPayment.agencyId}, leaseId: ${recentOwnerPayment.leaseId}")
  }

  def insertPayment(payment: AccountPayment): Future[Done] = {
    val query = """
      |INSERT INTO payments (
      |  payment_id, created_at, agency_id, party_id, lease_id, to_party_tag, owner_account_id,
      |  invoice_id, invoice_party_id, entity_ref, was_manually_corrected
      |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      |ON CONFLICT (payment_id) DO UPDATE SET
      |  created_at = EXCLUDED.created_at,
      |  agency_id = EXCLUDED.agency_id,
      |  party_id = EXCLUDED.party_id,
      |  lease_id = EXCLUDED.lease_id,
      |  to_party_tag = EXCLUDED.to_party_tag,
      |  owner_account_id = EXCLUDED.owner_account_id,
      |  invoice_id = EXCLUDED.invoice_id,
      |  invoice_party_id = EXCLUDED.invoice_party_id,
      |  entity_ref = EXCLUDED.entity_ref,
      |  was_manually_corrected = EXCLUDED.was_manually_corrected
    """.stripMargin

    val values = List(
      payment.paymentId.uuid,
      timestampFromDateTime(payment.createdAt),
      payment.agencyId.map(_.uuid).orNull,
      payment.partyId.map(_.uuid).orNull,
      payment.leaseId.map(_.uuid).orNull,
      payment.toPartyTag.getObjectName,
      payment.ownerAccountId.map(_.uuid).orNull,
      payment.invoiceId.map(_.uuid).orNull,
      payment.invoicePartyId.map(_.uuid).orNull,
      payment.entityRef.orNull,
      payment.wasManuallyCorrected.orNull
    )

    query.execute(values).failOnUnaffected(s"Insert into payments failed for paymentId: ${payment.paymentId}")
  }

  def findPayment(paymentId: UUID): Future[Option[AccountPayment]] = {
    val query = "SELECT * FROM payments WHERE payment_id = ?"
    
    query.execute(List(paymentId.uuid)).mapSingle(Convert.fromAccountPaymentRow).recover {
      case ex: Exception =>
        logger.error(s"Error finding payment with paymentId: $paymentId", ex)
        None
    }
  }

  def insertLegacyReconPaymentBeneficiary(row: LegacyReconPaymentBeneficiary): Future[Done] = {
    val query = """
      |INSERT INTO legacy_recon_payment_beneficiaries (payment_id, beneficiary_id)
      |VALUES (?, ?)
      |ON CONFLICT (payment_id) DO UPDATE SET
      |  beneficiary_id = EXCLUDED.beneficiary_id
    """.stripMargin

    val values = List(row.paymentId, row.beneficiaryId)

    query.execute(values).failOnUnaffected(s"Insert into legacy_recon_payment_beneficiaries failed for paymentId: ${row.paymentId}")
  }

  def findLegacyReconBeneficiaryFor(paymentId: JavaUUID): Future[Option[JavaUUID]] = {
    val query = "SELECT beneficiary_id FROM legacy_recon_payment_beneficiaries WHERE payment_id = ?"
    
    query.execute(List(paymentId)).mapSingle(row => row.get("beneficiary_id").asInstanceOf[JavaUUID]).recover {
      case ex: Exception =>
        logger.error(s"Error finding legacy recon beneficiary for paymentId: $paymentId", ex)
        None
    }
  }

  object Convert {
    def fromRow(row: RowData): PendingPayment = {
      PendingPayment(
        paymentId = UUID(getUUID(row, "payment_id")),
        toPartyId = UUID(getUUID(row, "to_party_id")),
        invoiceId = UUID(getUUID(row, "invoice_id")),
        agencyId = UUID(getUUID(row, "agency_id")),
        userId = Option(getUUID(row, "user_id")).map(UUID(_)),
        portfolioId = Option(getUUID(row, "portfolio_id")).map(UUID(_)),
        fromPartyId = UUID(getUUID(row, "from_party_id")),
        toPartyTag = PartyTag(row.getString("to_party_tag")),
        grossAmount = row.getAs[java.math.BigDecimal]("gross_amount"),
        isVat = row.getBoolean("is_vat"),
        reference = row.getString("reference"),
        createdAt = getTimestamp(row, "created_at"),
        beneficiaryId = Option(row.get("beneficiary_id").asInstanceOf[JavaUUID])
      )
    }

    def fromAccountPaymentRow(row: RowData): AccountPayment = {
      AccountPayment(
        paymentId = UUID(getUUID(row, "payment_id")),
        createdAt = getTimestamp(row, "created_at"),
        agencyId = Option(getUUID(row, "agency_id")).map(UUID(_)),
        partyId = Option(getUUID(row, "party_id")).map(UUID(_)),
        leaseId = Option(getUUID(row, "lease_id")).map(UUID(_)),
        toPartyTag = PartyTag(row.getString("to_party_tag")),
        ownerAccountId = Option(getUUID(row, "owner_account_id")).map(UUID(_)),
        invoiceId = Option(getUUID(row, "invoice_id")).map(UUID(_)),
        invoicePartyId = Option(getUUID(row, "invoice_party_id")).map(UUID(_)),
        entityRef = Option(row.getString("entity_ref")),
        wasManuallyCorrected = Option(row.getBoolean("was_manually_corrected"))
      )
    }
  }
}
