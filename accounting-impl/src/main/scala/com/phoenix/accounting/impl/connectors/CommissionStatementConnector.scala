package com.phoenix.accounting.impl.connectors

import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.phoenix.date.{DateTimeJsonFormatter, DateUtcUtil}
import io.getquill
import io.getquill.{CassandraLagomAsyncContext, CassandraLagomStreamContext, SnakeCase}
import org.joda.time.DateTime
import play.api.libs.json.{Format, JsError, JsResult, JsString, JsSuccess, JsValue, Json}

import java.util.{Date, UUID}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class CommissionStatementConnector(session: CassandraSession)(implicit ec: ExecutionContext) {
  val asyncCtx = new CassandraLagomAsyncContext(SnakeCase, session)
  val streamCtx = new CassandraLagomStreamContext(SnakeCase, session)

  private implicit val encodeTimestamp = getquill.MappedEncoding[Date, DateTime](i =>
    DateUtcUtil.now().withMillis(i.getTime)
  )
  private implicit val decodeTimestamp = getquill.MappedEncoding[DateTime, Date](_.toDate)

  private implicit val encodeSourceCategory = getquill.MappedEncoding[CommissionStatementSource, String](_.getObjectName)
  private implicit val decodeSourceCategory = getquill.MappedEncoding[String, CommissionStatementSource](CommissionStatementSource(_))

  private val asyncStatements = asyncCtx.quote {
    asyncCtx.querySchema[CommissionStatementRow]("commission_statement")
  }

  private val streamStatements = streamCtx.quote {
    streamCtx.querySchema[CommissionStatementRow]("commission_statement")
  }

  def insert(statement: CommissionStatementRow): Future[Done] = {
    import asyncCtx._
    val q = quote { asyncStatements.insert(lift(statement)) }
    run(q)
  }

  def fetch(agencyId: UUID): Source[CommissionStatementRow, NotUsed] = {
    import streamCtx._
    val q = quote {
      streamStatements.filter { p =>
        p.agencyId == lift(agencyId)
      }
    }
    run(q)
  }
}

sealed trait CommissionStatementSource {
  def getObjectName: String = {
    val name = getClass.getSimpleName
    name.lastOption match {
      case Some(char) if char == '$' => name.substring(0, name.length - 1)
      case _ => name
    }
  }
}

object CommissionStatementSource {
  def apply(category: String): CommissionStatementSource = category match {
    case "Receipt"  => Receipt
    case "Payout"   => Payout
    case "Royalty"  => Royalty
    case "Transfer" => Transfer
    case "Reversed" => Reversed
    case _          => throw new Error(s"Invalid SourceCategory passed: [$category]")
  }

  final case object Receipt extends CommissionStatementSource
  final case object Payout extends CommissionStatementSource
  final case object Royalty extends CommissionStatementSource
  final case object Transfer extends CommissionStatementSource
  final case object Reversed extends CommissionStatementSource

  implicit val format: Format[CommissionStatementSource] = new Format[CommissionStatementSource] {
    def writes(sourceCategory: CommissionStatementSource): JsValue = JsString(sourceCategory.getObjectName)

    def reads(json: JsValue): JsResult[CommissionStatementSource] = {
      Try(CommissionStatementSource(json.as[String])) match {
        case Success(invoiceType) => JsSuccess(invoiceType)
        case Failure(exception) => JsError(s"Invalid Source Type: ${exception.getMessage}")
      }
    }
  }
}

case class CommissionStatementRow(agencyId: UUID, createdAt: DateTime, sourceId: String, sourceType: CommissionStatementSource, partyId: Option[UUID],
                                  propertyName: Option[String], invoiceName: Option[String], remittanceAt: Option[DateTime],
                                  asset: String, precision: Int, reference: String,
                                  debitAmount: Option[BigDecimal], creditAmount: Option[BigDecimal])

object CommissionStatementRow {
  implicit val formatDateTime: Format[DateTime] = DateTimeJsonFormatter.format
  implicit val format: Format[CommissionStatementRow] = Json.format
}
