package com.phoenix.accounting.impl

import akka.actor.ActorSystem
import akka.actor.typed.scaladsl.adapter._
import akka.actor.typed.{ActorSystem => TypedActorSystem}
import akka.cluster.typed.ClusterSingleton
import akka.serialization.SerializationExtension
import akka.stream.Materializer
import com.lightbend.lagom.internal.client.CircuitBreakerMetricsProviderImpl
import com.lightbend.lagom.scaladsl.akka.discovery.AkkaDiscoveryComponents
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.devmode.LagomDevModeComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.pubsub.{PubSubComponents, PubSubRegistry}
import com.lightbend.lagom.scaladsl.server._
import com.phoenix.accounting.api.AccountingService
import com.phoenix.accounting.impl.actors.BillingScheduler
import com.phoenix.accounting.impl.connectors._
import com.phoenix.accounting.impl.entities.{BatchFeedEntity, PartyAccountingEntity}
import com.phoenix.accounting.impl.listeners._
import com.phoenix.accounting.impl.readside._
import com.phoenix.accounting.impl.statements.PropertyTaxStatementImpl
import com.phoenix.agency.api.AgencyService
import com.phoenix.authentication.jwt.AuthenticationComponents
import com.phoenix.common.data.ReadsideDatabase
import com.phoenix.exception.CustomExceptionSerializer
import com.phoenix.invoice.api.InvoiceService
import com.phoenix.party.api.PartyService
import com.phoenix.pdf.api.PdfService
import com.phoenix.portfolio.api.PortfolioService
import com.phoenix.projections.lib.ProjectionRegistryProxy
import com.phoenix.recon.api.ReconService
import com.phoenix.render.{TemplateSettings, TemplateSettingsExtension}
import com.phoenix.wallet.api.WalletService
import com.softwaremill.macwire._
import com.typesafe.config.ConfigFactory
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import slack.api.SlackApiClient

import scala.concurrent.ExecutionContext
import scala.concurrent.duration._

trait AccountingComponents extends LagomServerComponents
  with AuthenticationComponents

  with CassandraPersistenceComponents {

  implicit def executionContext: ExecutionContext
  def environment: Environment
  val pubSubRegistry: PubSubRegistry
  implicit def actorSys: ActorSystem
  implicit val typedSystem: TypedActorSystem[Nothing] = actorSystem.toTyped
  implicit val materializer: Materializer
  implicit def templateSettings: TemplateSettings
  implicit val readSideDatabase: ReadsideDatabase = new ReadsideDatabase("accounting")
  readSideDatabase.migrate()

  val pdfService: PdfService

  override lazy val lagomServer = serverFor[AccountingService](wire[AccountingServiceImpl])
  lazy val partyConnector = wire[PartyConnector]
  lazy val portfolioConnector = wire[PortfolioConnector]
  lazy val billingItemConnector = wire[BillingConnector]
  lazy val invoiceConnector = wire[InvoiceConnector]
  lazy val agencyConnector = wire[AgencyConnector]
  lazy val paymentConnector = wire[PaymentConnector]
  lazy val pgPaymentConnector = wire[PgPaymentConnector]
  lazy val incomeAndExpenseConnector = wire[IncomeAndExpenseConnector]
  lazy val incomeReportConnector = wire[IncomeReportConnector]
  lazy val pgIncomeReportConnector = wire[PgIncomeReportConnector]
  lazy val commissionIncomeConnector = wire[CommissionIncomeConnector]
  lazy val propertyConnector = wire[PropertyConnector]
  lazy val propertyVatConnector = wire[PropertyVatConnector]
  lazy val leaseStreamConnector = wire[LeaseStreamConnector]
  lazy val royaltyStatementConnector = wire[RoyaltyStatementConnector]
  lazy val bankStatementConnector = wire[BankStatementConnector]
  lazy val bankStatementStreamConnector = wire[BankStatementStreamConnector]
  lazy val commissionStatementConnector = wire[CommissionStatementConnector]
  lazy val reportCommissionConnector = wire[ReportCommissionConnector]
  lazy val jsonSerializerRegistry = AccountingSerializerRegistry
  lazy val customerStatementImpl = wire[statements.CustomerStatementImpl]
  lazy val customerDepositStatementImpl = wire[statements.CustomerDepositStatementImpl]
  lazy val depositBankStatementImpl = wire[statements.DepositBankStatementImpl]
  lazy val bankStatementImpl = wire[statements.BankStatementImpl]
  lazy val commissionStatementImpl = wire[statements.CommissionStatementImpl]
  lazy val leaseStatementImpl = wire[statements.LeaseStatementImpl]
  lazy val receiptFeeReportImpl = wire[reports.ReceiptFeeReportImpl]
  lazy val propertyTaxStatementImpl = wire[PropertyTaxStatementImpl]
  lazy val reportCashReceiptsConnector = wire[ReportCashReceiptsConnector]
  lazy val reportCashPaymentsConnector = wire[ReportCashPaymentsConnector]
  lazy val commissionStatementCsvReport = wire[reports.CommissionStatementCsvReport]
  lazy val bankStatementCsvReport = wire[reports.BankStatementCsvReport]
  lazy val propertyVatCsvReport = wire[reports.PropertyVatCsvReport]
  lazy val incomeAndExpenseReport = wire[reports.IncomeAndExpenseReport]
  lazy val walletPartyStatementConnector = wire[WalletPartyStatementConnector]
  lazy val walletConnector = wire[WalletConnector]
  lazy val accountStatementCassConnector = wire[AccountStatementCassConnector]
  lazy val accountStatementConnector = wire[AccountStatementConnector]
  lazy val receiptFeeConnector = wire[ReceiptFeeConnector]
  lazy val billingStatementConnector = wire[BillingStatementConnector]
  lazy val pgBillingStatementConnector = wire[PgBillingStatementConnector]
  lazy val auditTransactionsConnector = wire[AuditTransactionsConnector]
  lazy val phoenixProjectionRegistry = wire[ProjectionRegistryProxy]
  lazy val serialization = SerializationExtension(actorSystem)
  lazy val slackApiClient: Option[SlackApiClient] =
    scala.util.Try(ConfigFactory.load().getString("slack.token")).toOption.map {
      key => SlackApiClient(key)
    }

  persistentEntityRegistry.register(wire[PartyAccountingEntity])
  persistentEntityRegistry.register(wire[BatchFeedEntity])

  protected def registerReadsides(): Unit = {
    readSide.register(wire[BankStatementProcessor])
    readSide.register(wire[CommissionIncomeProcessor])
    readSide.register(wire[LeaseStatementProcessor])
    readSide.register(wire[DepositBankStatementProcessor])
    readSide.register(wire[PendingPaymentProcessor])
    readSide.register(wire[CommissionStatementProcessor])
    readSide.register(wire[InvoiceProcessor])
    readSide.register(wire[VatProcessor])
    readSide.register(wire[ReportCashReceiptProcessor])
    readSide.register(wire[ReportCashPaymentProcessor])
    readSide.register(wire[IncomeReportProcessor])
    readSide.register(wire[PartyWalletStatementProcessor])
    readSide.register(wire[BillingItemReadsideProcessor])
    readSide.register(wire[AccountStatementProcessor])
    readSide.register(wire[AccountStatementPgProcessor])
    readSide.register(wire[ReceiptFeeReportProcessor])
    readSide.register(wire[AuditTransactionsReadsideProcessor])
    readSide.register(wire[ReportCommissionReadsideProcessor])
    readSide.register(wire[ReportCommissionReadsidePgProcessor])
    readSide.register(wire[BillingStatementReadsideProcessor])
    readSide.register(wire[IncomeAndExpenseReadsideProcessor])
  }
}

abstract class AccountingApplication(context: LagomApplicationContext) extends LagomApplication(context)
  with AccountingComponents
  with AhcWSComponents
  with PubSubComponents {

  override lazy val defaultExceptionSerializer = new CustomExceptionSerializer(environment,
    title = "accounting",
    version = getClass.getPackage.getImplementationVersion)
  implicit def actorSys: ActorSystem = actorSystem
  lazy val singletonManager = ClusterSingleton(typedSystem)

  lazy val portfolioService: PortfolioService = serviceClient.implement[PortfolioService]
  lazy val partyService: PartyService = serviceClient.implement[PartyService]
  lazy val invoiceService: InvoiceService = serviceClient.implement[InvoiceService]
  lazy val agencyService: AgencyService = serviceClient.implement[AgencyService]
  lazy val walletService: WalletService = serviceClient.implement[WalletService]
  lazy val reconService: ReconService = serviceClient.implement[ReconService]
  lazy val pdfService: PdfService = serviceClient.implement[PdfService]
  lazy val billingScheduler: BillingScheduler = wire[BillingScheduler]

  implicit def templateSettings: TemplateSettings = TemplateSettingsExtension(actorSystem).settings

  // scalastyle:off
  def subscribeSome(listeners: String*)(implicit executionContext: ExecutionContext): Unit = {
    val all = listeners.contains("*")
    if (all || listeners.contains("portfolio")) wire[PortfolioEventListener]
    if (all || listeners.contains("party")) wire[PartyEventListener]
    if (all || listeners.contains("invoice")) {
      actorSys.scheduler.scheduleOnce(2.minute) {
        wire[InvoiceEventListener]
      }
    }
    if (all || listeners.contains("recon")) {
      actorSys.scheduler.scheduleOnce(2.minute) {
        wire[ReconEventListener]
      }
    }
    if (all || listeners.contains("wallet")) {
      actorSys.scheduler.scheduleOnce(2.minute) {
        wire[WalletEventListener]
        wire[SendLeaseStatementListener]
      }
    }
    if (all || listeners.contains("blockchain")) {
      actorSys.scheduler.scheduleOnce(2.minute) {
        wire[BlockchainEventListener]
      }
    }
    if (all || listeners.contains("invoice-batch")) {
      actorSys.scheduler.scheduleOnce(3.minute) {
        wire[InvoiceBatchEventListener]
      }
    }
    if (all || listeners.contains("agency")) wire[AgencyEventListener]
    if (all || listeners.contains("agency-group")) wire[AgencyGroupEventListener]
  }
  // scalastyle:off

  def subscribeAll()(implicit executionContext: ExecutionContext): Unit = subscribeSome("*")

  def startScheduler(): Unit = {
    billingScheduler.initWithTimers
  }
}

class AccountingLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext): LagomApplication =
    new AccountingApplication(context) with LagomDevModeComponents with LagomKafkaComponents {
      registerReadsides()
      subscribeAll()
      startScheduler()
    }

  override def load(context: LagomApplicationContext): LagomApplication =
    new AccountingApplication(context) with AkkaDiscoveryComponents with LagomKafkaComponents {
      override lazy val circuitBreakerMetricsProvider = new CircuitBreakerMetricsProviderImpl(actorSystem)
      registerReadsides()
      subscribeAll()
      startScheduler()
    }

  override def describeService = Some(readDescriptor[AccountingService])
}
