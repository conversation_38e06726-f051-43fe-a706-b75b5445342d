package com.phoenix.accounting.impl.readside

import akka.stream.Materializer
import akka.stream.scaladsl.{Sink, Source}
import com.datastax.driver.core.BoundStatement
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, EventStreamElement, ReadSideProcessor}
import com.phoenix.accounting.impl.Vat
import com.phoenix.accounting.impl.connectors.{AgencyConnector, LeaseStreamConnector, PartyConnector, PaymentConnector, PortfolioCommissionSplit, PortfolioConnector, ReportCommissionConnector, ReportCommissionRow}
import com.phoenix.accounting.impl.events.{BlockchainTransferCreated, PartyAccountingEvent}
import com.phoenix.date.DateUtcUtil
import com.phoenix.logging.EventLogging
import com.phoenix.util
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.DateTime

import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}
import java.util.UUID
import scala.math.BigDecimal.javaBigDecimal2bigDecimal

class ReportCommissionReadsideProcessor(session: CassandraSession, readSide: CassandraReadSide,
                                        leaseStreamConnector: LeaseStreamConnector,
                                        commissionConnector: ReportCommissionConnector,
                                        portfolioConnector: PortfolioConnector,
                                        paymentConnector: PaymentConnector,
                                        agencyConnector: AgencyConnector,
                                        val partyConnector: PartyConnector)
                                       (implicit ec: ExecutionContext, mat: Materializer)
  extends ReadSideProcessor[PartyAccountingEvent] with EventLogging with LazyLogging {

  override def aggregateTags: Set[AggregateEventTag[PartyAccountingEvent]] = Set(PartyAccountingEvent.Tag)

  // scalastyle:off
  override def buildHandler(): ReadSideProcessor.ReadSideHandler[PartyAccountingEvent] = {
    readSide.builder[PartyAccountingEvent]("reportCommissionReadsideProcessor")
      .setEventHandler[BlockchainTransferCreated] {
        case EventStreamElement(_, BlockchainTransferCreated(blockchainTransfer, eventRecord), _)
          if blockchainTransfer.from.wallet == "<EMAIL>" &&
            blockchainTransfer.payment.flatMap(_.invoice).exists(_.portfolioId.isDefined) &&
            blockchainTransfer.to.party.isDefined && blockchainTransfer.to.agency.isDefined &&
            blockchainTransfer.to.party.get == blockchainTransfer.to.agency.get &&
          blockchainTransfer.payment.flatMap(_.invoice).isDefined =>
          val eventTime = DateUtcUtil.now().withMillis(eventRecord.createdAt)
          val payment = blockchainTransfer.payment.get
          val invoice = payment.invoice.get
          val agencyId = blockchainTransfer.to.agency.get
          val asset = blockchainTransfer.asset.name
          val year = eventTime.getYear
          val month = eventTime.getMonthOfYear
          val portfolioId = invoice.portfolioId.get
          val paymentId = blockchainTransfer.paymentId.get

          val paymentAmount = blockchainTransfer.asset.amount

          def insertRowFor(
                            agent: Option[PortfolioCommissionSplit],
                            ptyTpl: (Option[String], Option[String], Option[String]),
                            beneficiaryId: Option[UUID],
                            calculatedRoyalty: Option[BigDecimal]
                          ) = {
            val grossComm:BigDecimal = agent.map(s => (paymentAmount / BigDecimal(100)) * BigDecimal(s.percentageSplit)).getOrElse(paymentAmount)
            val (propertyAddress, buildingName, unitNumber) = ptyTpl
            //Main text
            //Secondary Text
            //Meta Data
            val (prAmount, percentage, netComm) = invoice.beneficiaries
              .filter(_.beneficiaryId.isDefined)
              .filter(x => beneficiaryId.map(_.toString).contains(x.beneficiaryId.get.uuid.toString))
              .filter(_.amount.isDefined)
              .lastOption match {
              case Some(paymentRule) =>
                val pr = paymentRule.amount.get.setScale(2, BigDecimal.RoundingMode.FLOOR)
                val p = (pr / invoice.grossAmount) * BigDecimal(100)
                val nc = Vat.netAmount(asset, grossComm.setScale(2, BigDecimal.RoundingMode.FLOOR), paymentRule.vat.exists(x => x))
                (pr, p, nc)
              case None => (BigDecimal(0), BigDecimal(0), paymentAmount)
            }

            val vat:BigDecimal = grossComm - netComm
            commissionConnector.insert(ReportCommissionRow(
              agencyId.uuid, year, month,
              agentId = agent.map(_.agentId).map(_.uuid.toString).getOrElse(""),
              asset = asset,
              createdAt = eventTime,
              paymentId = paymentId,
              buildingName = buildingName,
              unitNumber = unitNumber,
              leaseId = invoice.portfolioId.map(_.uuid),
              propertyName = propertyAddress,
              invoiceDate = invoice.dueDate,
              invoiceId = invoice.invoiceId,
              invoiceName = invoice.nameWithoutRepetition,
              invoiceAmount = invoice.grossAmount.setScale(2, BigDecimal.RoundingMode.FLOOR),
              netInvoiceAmount = invoice.netAmount.setScale(2, BigDecimal.RoundingMode.FLOOR),
              invoiceType = invoice.invoiceType,
              paymentAmount = blockchainTransfer.asset.amount,
              grossPaymentRuleAmount = prAmount.setScale(2, BigDecimal.RoundingMode.FLOOR),
              netPercentInvoice = percentage,
              agentName = agent.map(_.agentName).getOrElse(""),
              agentSplit = agent.map(_.percentageSplit).getOrElse(0),
              grossCommission = grossComm.setScale(2, BigDecimal.RoundingMode.FLOOR),
              netCommission = netComm.setScale(2, BigDecimal.RoundingMode.FLOOR),
              vatAmount = vat.setScale(2, BigDecimal.RoundingMode.FLOOR),
              royaltyAmount = calculatedRoyalty
            ))
          }

          for {
            beneficiaryId   <- payment.beneficiaryId match {
              case None => paymentConnector.findLegacyReconBeneficiaryFor(paymentId)
              case id => Future(id)
            }
            pendingPayment  <- paymentConnector.findPendingPayment(paymentId)
            propertyAddress <- fetchAddress(agencyId, pendingPayment.flatMap(_.portfolioId).map(_.uuid))
            royalty         <- calculateRoyalty(agencyId, eventTime, paymentAmount)
            _ <- Source.fromFuture(portfolioConnector.findCommissionFor(portfolioId, eventTime))
              .flatMapConcat {
                case Nil =>
                  // no agents
                  Source.fromFuture(insertRowFor(None, propertyAddress, beneficiaryId, royalty))
                case x =>
                  // iterate over each agent
                  Source(immutable.Seq(x: _*))
                    .mapAsync(1) { agent =>
                      insertRowFor(Some(agent), propertyAddress, beneficiaryId, royalty)
                    }
              }
              .runWith(Sink.ignore)
          } yield ok
        case _ => Future(ok)
      }
      .build()
  }

  private def calculateRoyalty(agencyId: UUID, startDate: DateTime, amount: BigDecimal) = {
    agencyConnector.findAgencyGroup(agencyId, startDate).map {
      case Some(row) if row.royaltyPercentage > 0 => Option(amount * (row.royaltyPercentage / BigDecimal(100)))
      case _ => Option.empty[BigDecimal]
    }
  }

  private def fetchAddress(agencyId: UUID, portfolioId: Option[UUID]): Future[(Option[String], Option[String], Option[String])] = {
    portfolioId match {
      case Some(id) => for {
        addr <- portfolioConnector.find(agencyId, id)
        blding <- portfolioConnector.findBuildingFor(id)
      } yield (addr.flatMap(_.address), blding.map(_.buildingName), blding.flatMap(_.unitNumber))
      case None     => Future((Option.empty[String], Option.empty[String], Option.empty[String]))
    }
  }

  val ok: immutable.Seq[BoundStatement] = List()
}
