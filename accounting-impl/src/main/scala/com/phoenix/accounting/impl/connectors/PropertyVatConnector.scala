package com.phoenix.accounting.impl.connectors

import java.util.Date

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.phoenix.date.DateUtcUtil
import com.phoenix.party.api.PartyTag
import com.phoenix.util.UUID
import org.joda.time.DateTime
import com.phoenix.persistence.QuillHelper
import io.getquill
import io.getquill.{CassandraLagomAsyncContext, SnakeCase}

import scala.concurrent.{ExecutionContext, Future}

class PropertyVatConnector(session: CassandraSession)(implicit ec: ExecutionContext) {
  val ctx = new CassandraLagomAsyncContext(SnakeCase, session)
  import ctx._
  import QuillHelper.Implicits.{ encodeUUID, decodeUUID }

  implicit val encodeSourceType    = getquill.MappedEncoding[VatSourceType, String](_.getObjectName)
  implicit val decodeSourceType    = getquill.MappedEncoding[String, VatSourceType](VatSourceType(_))
  implicit val encodeIncomeAccount = getquill.MappedEncoding[IncomeAccount, String](_.getObjectName)
  implicit val decodeIncomeAccount = getquill.MappedEncoding[String, IncomeAccount](IncomeAccount(_))
  implicit val encodeTimestamp     = getquill.MappedEncoding[Date, DateTime](i => DateUtcUtil.now().withMillis(i.getTime))
  implicit val decodeTimestamp     = getquill.MappedEncoding[DateTime, Date](_.toDate)
  implicit val encodePartyTag      = getquill.MappedEncoding[PartyTag, String](_.getObjectName)
  implicit val decodePartyTag      = getquill.MappedEncoding[String, PartyTag](PartyTag(_))

  val vat = quote { querySchema[VatEntry]("property_vat_entries") }

  def insert(portfolio: VatEntry): Future[Done] = {
    val q = quote { vat.insert(lift(portfolio)) }
    ctx.run(q)
  }

  def remove(agencyId: UUID, property: UUID, portfolio: UUID, sourceId: UUID): Future[Done] = {
    val q = quote { vat.filter(s => s.agencyId == lift(agencyId) && s.propertyId == lift(property)
                      && s.portfolioId == lift(portfolio)
                      && s.sourceId == lift(sourceId)).delete }
    ctx.run(q)
  }

  def all(agencyId: UUID): Future[Seq[VatEntry]] = {
    val q = quote { vat.filter(p => p.agencyId == lift(agencyId)) }
    ctx.run(q)
  }

  def allProperty(agencyId: UUID, property: UUID): Future[Seq[VatEntry]] = {
    val q = quote { vat.filter(p => p.agencyId == lift(agencyId) && p.propertyId == lift(property)) }
    ctx.run(q)
  }

  def allPortfolio(agencyId: UUID, property: UUID, portfolio: UUID): Future[Seq[VatEntry]] = {
    val q = quote { vat.filter(p => p.agencyId == lift(agencyId) &&
                                    p.propertyId == lift(property) &&
                                    p.portfolioId == lift(portfolio)) }
    ctx.run(q)
  }

}


sealed trait VatSourceType {
  def getObjectName: String = {
    val name = getClass.getSimpleName
    name.lastOption match {
      case Some(char) if char == '$' => name.substring(0, name.length - 1)
      case _ => name
    }
  }
}

object VatSourceType {
  def apply(category: String): VatSourceType = category match {
    case "Invoice"    => Invoice
    case "Payment"    => Payment
    case "CreditNote" => CreditNote
    case _            => throw new Error(s"Invalid VatSourceType passed: [$category]")
  }
}

final case object Invoice extends VatSourceType
final case object Payment extends VatSourceType
final case object CreditNote extends VatSourceType

sealed trait IncomeAccount {
  def getObjectName: String = {
    val name = getClass.getSimpleName
    name.lastOption match {
      case Some(char) if char == '$' => name.substring(0, name.length - 1)
      case _ => name
    }
  }
}

object IncomeAccount {
  def apply(category: String): IncomeAccount = category.replace(" ", "") match {
    case "LandlordIncome"       => LandlordIncome
    case "AgencyIncome"         => AgencyIncome
    case "TenantDepositIncome"  => TenantDepositIncome
    case "TenantRefundIncome"  => TenantRefundIncome
    case "LandlordDepositIncome" => LandlordDepositIncome
    case _            => throw new Error(s"Invalid IncomeAccount passed: [$category]")
  }
}

final case object LandlordIncome extends IncomeAccount
final case object AgencyIncome extends IncomeAccount
final case object TenantDepositIncome extends IncomeAccount
final case object TenantRefundIncome extends IncomeAccount
final case object LandlordDepositIncome extends IncomeAccount

case class VatEntry(agencyId: UUID, propertyId: UUID, portfolioId: UUID, sourceId: UUID, sourceType: VatSourceType,
                    description: String, incomeAccount: IncomeAccount, createdAt: DateTime, dated: DateTime, vatAmount: BigDecimal,
                    netAmount: BigDecimal, grossAmount: BigDecimal, invoiceType: Option[String], beneficiaryCategory: Option[PartyTag])
