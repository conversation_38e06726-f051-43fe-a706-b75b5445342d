package com.phoenix.accounting.impl.connectors

import java.util.UUID
import akka.{Done, NotUsed}
import akka.stream.scaladsl.Source
import com.github.jasync.sql.db.RowData
import com.phoenix.common.data.{ReadsideConnector, ReadsideDatabase}
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.DateTime

import scala.concurrent.{ExecutionContext, Future}

class ReportCommissionConnector(readsideDatabase: ReadsideDatabase)(implicit ec: ExecutionContext)
  extends ReadsideConnector(readsideDatabase.getConnectionPool) with LazyLogging {

//  private val report = quote {
//    querySchema[ReportCommissionRow]("report_commission")
//  }

  def fetch(agencyId: UUID, year: Int, month: Int, asset: String): Source[ReportCommissionRow, NotUsed] = {
    val query = """
              |SELECT *
              |FROM report_commission
              |WHERE agency_id = ? AND year = ? AND month = ? AND asset = ?;
           """.stripMargin
  val values = List(agencyId, year, month, asset)
    query.execute(values).mapWith(Convert.fromRow)
  }

  def insert(row: ReportCommissionRow): Future[Done] = {
    val query = """
              |INSERT INTO report_commission (
              |  agency_id, year, month, asset, agent_id, created_at, payment_id, building_name, unit_number, lease_id,
              |  property_name, invoice_date, invoice_id, invoice_name, invoice_amount, net_invoice_amount, invoice_type,
              |  payment_amount, gross_payment_rule_amount, net_percent_invoice, agent_name, agent_split, gross_commission,
              |  net_commission, vat_amount, royalty_amount)
              |VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
           """.stripMargin
    val values = List(row.agencyId, row.year, row.month, row.asset, row.agentId, row.createdAt, row.paymentId, row.buildingName.orNull,
      row.unitNumber.orNull, row.leaseId.orNull, row.propertyName.orNull, row.invoiceDate, row.invoiceId, row.invoiceName,
      row.invoiceAmount, row.netInvoiceAmount, row.invoiceType, row.paymentAmount, row.grossPaymentRuleAmount, row.netPercentInvoice,
      row.agentName, row.agentSplit, row.grossCommission, row.netCommission, row.vatAmount, row.royaltyAmount.orNull)
      query.execute(values).failOnUnaffected(s"Insert into report_commission failed. Params: $values")
  }
}

final case class ReportCommissionRow(agencyId: UUID, year: Int, month: Int, asset: String, agentId: String, createdAt: DateTime, paymentId: UUID,
                                     buildingName: Option[String], unitNumber: Option[String],
                                     leaseId: Option[UUID], propertyName: Option[String], invoiceDate: DateTime, invoiceId: UUID, invoiceName: String,
                                     invoiceAmount: BigDecimal, netInvoiceAmount: BigDecimal, invoiceType: String,
                                     paymentAmount: BigDecimal, grossPaymentRuleAmount: BigDecimal,
                                     netPercentInvoice: BigDecimal, agentName: String, agentSplit: Double, grossCommission: BigDecimal,
                                     netCommission: BigDecimal, vatAmount: BigDecimal, royaltyAmount: Option[BigDecimal])

private object Convert {
  def fromRow(row: RowData): ReportCommissionRow = ReportCommissionRow(
    agencyId = row.getAs[UUID]("agency_id"),
    year = row.getInt("year"),
    month = row.getInt("month"),
    asset = row.getString("asset"),
    agentId = row.getString("agent_id"),
    createdAt = row.getAs[DateTime]("created_at"),
    paymentId = row.getAs[UUID]("payment_id"),
    buildingName = Option(row.getString("building_name")),
    unitNumber = Option(row.getString("unit_number")),
    leaseId = Option(row.getAs[UUID]("lease_id")),
    propertyName = Option(row.getString("property_name")),
    invoiceDate = row.getAs[DateTime]("invoice_date"),
    invoiceId = row.getAs[UUID]("invoice_id"),
    invoiceName = row.getString("invoice_name"),
    invoiceAmount = BigDecimal.valueOf(row.getDouble("invoice_amount")),
    netInvoiceAmount = BigDecimal.valueOf(row.getDouble("net_invoice_amount")),
    invoiceType = row.getString("invoice_type"),
    paymentAmount = BigDecimal.valueOf(row.getDouble("payment_amount")),
    grossPaymentRuleAmount = BigDecimal.valueOf(row.getDouble("gross_payment_rule_amount")),
    netPercentInvoice = BigDecimal.valueOf(row.getDouble("net_percent_invoice")),
    agentName = row.getString("agent_name"),
    agentSplit = row.getDouble("agent_split"),
    grossCommission = BigDecimal.valueOf(row.getDouble("gross_commission")),
    netCommission = BigDecimal.valueOf(row.getDouble("net_commission")),
    vatAmount = BigDecimal.valueOf(row.getDouble("vat_amount")),
    royaltyAmount = Option(BigDecimal.valueOf(row.getDouble("royalty_amount")))
  )
}
