package com.phoenix.accounting.impl.listeners

import akka.Done
import akka.actor.ActorSystem
import akka.stream.Materializer
import akka.stream.scaladsl.{Flow, Source}
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.phoenix.accounting.impl.{AccountingPayment, AssetReceipt, BlockchainAsset, BlockchainTransfer, Invoice,PartyAccount, PendingPayment, WalletPartyAccount, WalletPayment}
import com.phoenix.accounting.impl.commands.PartyAccountingCommands.{CreateBlockchainTransfers, FindAccountingPayment, FindInvoice, FindPendingPayment}
import com.phoenix.accounting.impl.connectors.{PartyAccountRow, PartyConnector, PaymentConnector, WalletConnector, WalletTransaction}
import com.phoenix.accounting.impl.entities.{PartyAccountingEntity, WalletPaymentNotFound}
import com.phoenix.party.api.PartyTag
import com.phoenix.util.UUID
import com.phoenix.wallet.api._
import com.typesafe.scalalogging.LazyLogging

import scala.concurrent.{ExecutionContext, Future}

class BlockchainEventListener(entityRegistry: PersistentEntityRegistry,
                              walletService: WalletService,
                              paymentConnector: PaymentConnector,
                              partyConnector: PartyConnector,
                              walletConnector: WalletConnector)
                             (implicit executionContext: ExecutionContext,
                          implicit val mat: Materializer,
                          implicit val system: ActorSystem)
  extends LazyLogging {

  private def queryAccount(blockchainAccount: BlockchainCommand.Account) = {
    blockchainAccount match {
      case BlockchainCommand.Account(Some(account), Some(party), Some(agency), _, _, _, _, _) =>
        partyConnector.findPartyAccountId(agency, party, account).map(_.map(_.convertToPartyAccount))
      case _ => Future(Option.empty[PartyAccount])
    }
  }

  private implicit class BlockchainAccountConverter(account: BlockchainCommand.Account) {
    def toWalletPartyAccount: WalletPartyAccount = {
      import com.phoenix.util.{UUID => PhxUUID}
      WalletPartyAccount(account.irohaWalletString, Some(account.walletType),
        account.account.map(PhxUUID(_)), account.party.map(PhxUUID(_)),
        account.agency.map(PhxUUID(_)), account.agencyGroupId.map(PhxUUID(_)))
    }
  }


  private implicit class BlockchainAssetConverter(asset: BlockchainCommand.Asset) {
    def toAsset: BlockchainAsset = BlockchainAsset(asset.amount, asset.name, asset.precision)
  }

  // scalastyle:off
  private def queryBlockchainTransfer(command: BlockchainCommand.TransferAsset, addCommand: Option[BlockchainCommand.AddAsset], transaction: String): Future[Either[BlockchainCommand.TransferAsset, BlockchainTransfer]] = {
    def transfer(paymentId: Option[UUID], payment: Option[WalletPayment]) = {
      for {
        fromAccount <- queryAccount(command.srcAccount)
        toAccount <- queryAccount(command.destAccount)
        assetReceipt = addCommand.map(ac => AssetReceipt(ac.receiptReference, ac.bankingUid,
          ac.cashDepositFee.map(_.toAsset), ac.externalBank, ac.internalBank))
      } yield Right(BlockchainTransfer(command.position, transaction,
        command.srcAccount.toWalletPartyAccount, command.destAccount.toWalletPartyAccount,
        command.asset.toAsset, command.description, assetReceipt,
        fromAccount, toAccount, paymentId.map(_.uuid), payment, command.authorizedBy, remittanceAt = command.remittanceAt))
    }
    (command.payment, command.approvedPayment) match {
      case (_, Some(ap)) =>
        val accountingPayment = AccountingPayment(ap.payment, WalletPayment(
          ap.fromAccount.toWalletPartyAccount,
          ap.toAccount.toWalletPartyAccount,
          command.asset.name,
          command.asset.amount,
          command.asset.precision,
          ap.paymentKind.reference,
          ap.description,
          PartyTag(ap.paymentKind.beneficiaryTag),
          ap.approvedAt,
          ap.approvedBy.map(UUID.fromUUID),
          paymentKind = Option(ap.paymentKind)
        ))

        for {
          toPartyAccount <- accountingPayment.walletPayment.to match {
            case WalletPartyAccount(_, _, Some(account), Some(party), Some(agency), _) =>
              partyConnector.findPartyAccountId(agency, party, account).map(_.map(_.convertToPartyAccount))
            case _ => Future(Option.empty[PartyAccount])
          }
          fromPartyAccount <- accountingPayment.walletPayment.from match {
            case WalletPartyAccount(_, _, Some(account), Some(party), Some(agency), _) =>
              partyConnector.findPartyAccountId(agency, party, account).map(_.map(_.convertToPartyAccount))
            case _ => Future(Option.empty[PartyAccount])
          }
          entityRef = entityRegistry.refFor[PartyAccountingEntity](command.srcAccount.party.map(_.toString).getOrElse(command.srcAccount.irohaWalletString))
          invoice <- ap.invoice match {
            case Some(value) => entityRef.ask(FindInvoice(value))
            case None => Future(Option.empty[Invoice])
          }
          pendingPayment <- entityRef.ask(FindPendingPayment(accountingPayment.paymentId)).map(Option(_))
            .recover {
              case _: WalletPaymentNotFound => Option.empty[PendingPayment]
            }
          walletPayment = accountingPayment.walletPayment.copy(
            invoice = invoice,
            fromAccount = fromPartyAccount,
            toAccount = toPartyAccount,
            beneficiaryId = pendingPayment.flatMap(_.beneficiaryId)
          )
          result <- transfer(Option(ap.payment), Option(walletPayment))
        } yield result
      case (Some(paymentId), _) =>
        paymentConnector.findPayment(paymentId).flatMap {
          case Some(paymentRow) if paymentRow.entityRef.isDefined =>
            val entityRef = paymentRow.entityRef
            for {
              payment <- if (paymentRow.wasManuallyCorrected.getOrElse(false)) {
                Future(Option.empty[WalletPayment])
              } else {
                entityRegistry.refFor[PartyAccountingEntity](entityRef.get).ask(FindAccountingPayment(paymentId))
                  .map(reply => Option(reply.walletPayment))
              }
              blockchainTransfer <- transfer(Option(paymentId), payment)
            } yield blockchainTransfer
          case _ =>
            logger.info("Payment entity not resolved {}", paymentId)
            Future(Left(command))
        }
      case (paymentId, _) => transfer(paymentId.map(UUID.fromUUID), Option.empty)
    }
  }
  // scalastyle:on

  logger.info("[BCEL] Starting")

  import scala.collection.immutable
  import scala.concurrent.duration._

  private def logEventAsync[T](event: T): Future[T] = {
    logger.info(s"Received $event")
    Future.successful(event)
  }

  walletService.blockchainEvents
    .subscribe
    .withGroupId("accountingBlockchainEvents")
    .atLeastOnce(
      Flow[BlockchainEvent].mapAsync(1)(logEventAsync)
        .mapAsync(1) { event =>
          walletConnector.selectTransaction(event.transaction).map {
            case Some(_) => None
            case None => Option(event)
          }
        }
        .flatMapMerge(1, {
          case Some(BlockchainEvent(commands, creatorWalletString, transaction, timestamp)) =>
            if (commands.nonEmpty) {
              val addCommand = commands.collect { case addAsset: BlockchainCommand.AddAsset => addAsset }
              Source(immutable.Seq(commands: _*))
                .collect { case transfer: BlockchainCommand.TransferAsset => transfer }
                .mapAsync(1)(transfer => queryBlockchainTransfer(transfer, addCommand.headOption, transaction))
                .flatMapConcat {
                  case Left(transfer) => Source.single(transfer)
                    .throttle(1, 60.seconds)
                    .mapAsync(1)(queryBlockchainTransfer(_, addCommand.headOption, transaction))
                    .map {
                      _.toOption match {
                        case Some(value) => value
                        case None => throw new Exception("Payment entity not found")
                      }
                    }
                  case Right(bt) => Source.single(bt)
                }
                .fold(Seq.empty[BlockchainTransfer]) { case (acc, i) => acc :+ i }
                .flatMapConcat { blockchainTransfers =>
                  val grouped = blockchainTransfers.map { t =>
                      val resolvedEntityId = t.from.party.orElse(t.to.party).map(_.uuid.toString).getOrElse(t.from.wallet)
                      (resolvedEntityId, t)
                    }
                    .groupBy(_._1)
                    .toSeq
                  Source(immutable.Seq(grouped: _*))
                    .mapAsync(1) { case (entityId, payload) =>
                      val transfers = payload.map(_._2)
                      entityRegistry.refFor[PartyAccountingEntity](entityId).ask(CreateBlockchainTransfers(transfers, timestamp.getMillis))
                    }
                    .fold(Seq.empty[Done]) { case (acc, i) => acc :+ i }
                    .mapAsync(1) { _ =>
                      walletConnector.insertTransaction(WalletTransaction(transaction, timestamp))
                    }
                }
            } else {
              Source.fromFuture(walletConnector.insertTransaction(WalletTransaction(transaction, timestamp)))
            }
          case None => Source.single(Done)
        })
    )
}
