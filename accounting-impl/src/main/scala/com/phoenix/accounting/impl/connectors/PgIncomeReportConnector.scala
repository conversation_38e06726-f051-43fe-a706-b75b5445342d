package com.phoenix.accounting.impl.connectors

import akka.Done
import com.github.jasync.sql.db.RowData
import com.phoenix.accounting.api.response.IncomeEntryResponse
import com.phoenix.common.data.{ReadsideConnector, ReadsideDatabase}
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.DateTime

import java.util.{UUID => jUUID}
import scala.concurrent.{ExecutionContext, Future}
import scala.language.implicitConversions


class PgIncomeReportConnector(readsideDatabase: ReadsideDatabase)(implicit ec: ExecutionContext)
  extends ReadsideConnector(readsideDatabase.getConnectionPool) with LazyLogging {

  def upsert(entry: PgIncomeEntry): Future[Done] = {
    val query =
      """
        |INSERT INTO income_report (
        |  agency_id, portfolio_id, invoice_id, party_id, account_type, description, income_account, dated,
        |  vat_amount, net_amount, gross_amount, invoice_type
        |) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        |ON CONFLICT (agency_id, portfolio_id, invoice_id, dated) DO UPDATE SET
        |  agency_id = EXCLUDED.agency_id,
        |  portfolio_id = EXCLUDED.portfolio_id,
        |  party_id = EXCLUDED.party_id,
        |  account_type = EXCLUDED.account_type,
        |  description = EXCLUDED.description,
        |  income_account = EXCLUDED.income_account,
        |  dated = EXCLUDED.dated,
        |  vat_amount = EXCLUDED.vat_amount,
        |  net_amount = EXCLUDED.net_amount,
        |  gross_amount = EXCLUDED.gross_amount,
        |  invoice_type = EXCLUDED.invoice_type
    """.stripMargin

    val values = List(
      entry.agencyId,
      entry.portfolioId,
      entry.invoiceId,
      entry.partyId,
      entry.accountType,
      entry.description,
      entry.incomeAccount,
      entry.dated,
      entry.vatAmount,
      entry.netAmount,
      entry.grossAmount,
      entry.invoiceType
    )

    query.execute(values).failOnUnaffected(s"Insert into income_report failed. Params: $values")
  }

  def getAllIncomeInDateRange(startDate: String, endDate: String): Future[Seq[PgIncomeEntry]] = {
    val query =
      """
        |SELECT agency_id, portfolio_id, invoice_id, party_id, account_type, description, income_account, dated,
        |       vat_amount, net_amount, gross_amount, invoice_type
        |FROM income_report
        |WHERE dated BETWEEN ? AND ?
      """.stripMargin

    val values = List(new DateTime(startDate), new DateTime(endDate))

    query.execute(values).mapWith(Convert.fromRow)
  }

  def findAll(): Future[Seq[PgIncomeEntry]] = {
    val query ="SELECT * FROM income_report"
    query.execute(List.empty).mapWith(Convert.fromRow)
  }

  private object Convert {
    def fromRow(row: RowData): PgIncomeEntry = PgIncomeEntry(
      agencyId = row.getAs[jUUID]("agency_id"),
      portfolioId = row.getAs[jUUID]("portfolio_id"),
      invoiceId = row.getAs[jUUID]("invoice_id"),
      partyId = row.getAs[jUUID]("party_id"),
      accountType = row.getString("account_type"),
      description = row.getString("description"),
      incomeAccount = row.getString("income_account"),
      dated = new DateTime(row.getAs[java.time.OffsetDateTime]("dated").toInstant.toEpochMilli),
      vatAmount = BigDecimal.valueOf(row.getDouble("vat_amount")),
      netAmount = BigDecimal.valueOf(row.getDouble("net_amount")),
      grossAmount = BigDecimal.valueOf(row.getDouble("gross_amount")),
      invoiceType = row.getString("invoice_type")
    )
  }
}
object PgIncomeEntry {
  def fromIncomeEntry(i: IncomeEntry): PgIncomeEntry = PgIncomeEntry(
    agencyId = i.agencyId.uuid,
    portfolioId = i.portfolioId,
    invoiceId = i.invoiceId,
    partyId = i.partyId,
    accountType = i.accountType.getObjectName,
    description = i.description,
    incomeAccount = i.incomeAccount.getObjectName,
    dated = i.dated,
    vatAmount = i.vatAmount.bigDecimal,
    netAmount = i.netAmount.bigDecimal,
    grossAmount = i.grossAmount.bigDecimal,
    invoiceType = i.invoiceType
  )

  def toAPI(i: PgIncomeEntry): IncomeEntryResponse = IncomeEntryResponse(
    agencyId = i.agencyId,
    portfolioId = i.portfolioId,
    invoiceId = i.invoiceId,
    partyId = i.partyId,
    accountType = i.accountType,
    description = i.description,
    incomeAccount = i.incomeAccount,
    dated = i.dated,
    vatAmount = i.vatAmount,
    netAmount = i.netAmount,
    grossAmount = i.grossAmount,
    invoiceType = i.invoiceType
  )

}
case class PgIncomeEntry(agencyId: jUUID,
                           portfolioId: jUUID,
                           invoiceId: jUUID,
                           partyId: jUUID,
                           accountType: String,
                           description: String,
                           incomeAccount: String,
                           dated: DateTime,
                           vatAmount: BigDecimal,
                           netAmount: BigDecimal,
                           grossAmount: BigDecimal,
                           invoiceType: String)


