package com.phoenix.accounting.impl

import org.joda.time.format.{DateTimeFormatter, DateTimeFormatterBuilder}

object DateFormatters {
  // @todo move this to library/extension DateTimeJsonFormatters -> DateTimeFormatters
  val dayMonthNameYearFormat: DateTimeFormatter = new DateTimeFormatterBuilder()
    .appendDayOfMonth(1)
    .appendLiteral(' ')
    .appendMonthOfYearText()
    .appendLiteral(' ')
    .appendYear(4, 4)
    .toFormatter
}
