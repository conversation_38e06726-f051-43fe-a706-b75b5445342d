package com.phoenix.accounting.impl.entities

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import com.lightbend.lagom.scaladsl.pubsub.PubSubRegistry
import com.phoenix.accounting.impl.commands.BatchFeedCommand.CreateSummaryAccountStatement
import com.phoenix.accounting.impl.commands.PartyAccountingCommands._
import com.phoenix.accounting.impl.{AccountingPayment, Invoice, PartiallyCreatedTransfer, PartyAccount, PartyAccountingState, PendingPayment, WalletTransfer}
import com.phoenix.accounting.impl.commands._
import com.phoenix.accounting.impl.connectors.PartyAccountRow
import com.phoenix.accounting.impl.events._
import com.phoenix.date.DateUtcUtil
import com.phoenix.json.JsonFormats
import com.phoenix.persistence.EventRecord
import com.typesafe.scalalogging.LazyLogging
import play.api.libs.json.{<PERSON>at, <PERSON><PERSON>}

import java.util.UUID
import scala.concurrent.Future
import scala.util.control.NoStackTrace

class PartyAccountingEntity(pubSubRegistry: PubSubRegistry) extends PersistentEntity with LazyLogging {
  override type Command = PartyAccountingCommands[_]
  override type Event = PartyAccountingEvent
  override type State = PartyAccountingState

  override def initialState: PartyAccountingState = PartyAccountingState()

  override def behavior: Behavior = { _ =>
    if (entityId.startsWith("UUID(")) {
      logger.error("Invalid entityId: {}", entityId)
      Actions()
    } else {
      invoices orElse payments orElse accounts orElse walletPayments orElse walletStatuses orElse walletRejection orElse
        walletEft orElse easypayEft orElse walletTransfer orElse queryWalletPayments orElse queryInvoices orElse
        blockchain orElse depositFeeUpdated orElse state
    }
  }

  private def state = {
    Actions()
      .onReadOnlyCommand[GetState, PartyAccountingState] {
        case (GetState(_), ctx, state) => ctx.reply(state)
      }
  }

  //scalastyle:off
  private def invoices = {
    Actions()
      .onCommand[CreateInvoice, Done] {
        case (CreateInvoice(invoice), ctx, state) if state.invoices.contains(invoice) =>
          ctx.commandFailed(InvoiceAlreadyExists())
          ctx.done
        case (CreateInvoice(invoice), ctx, _) =>
          ctx.thenPersist(InvoiceCreated(invoice))(_ => ctx.reply(Done))
      }
      .onCommand[UpdateBeneficiaries, Done] {
        case (UpdateBeneficiaries(invoice, beneficiaries), ctx, state) =>
          invoice match {
            case Some(i) if i.beneficiaryUpdatedAfter(beneficiaries.head.updatedAt.get) =>
              ctx.commandFailed(BeneficiariesUpdatedRecently())
              ctx.done
            case Some(i) if i.beneficiaries.isEmpty || !i.beneficiaryUpdatedAfter(beneficiaries.head.updatedAt.get) =>
              ctx.thenPersist(InvoiceBeneficiariesUpdated(i.copy(beneficiaries = beneficiaries)))(_ => ctx.reply(Done))
            case _ =>
              ctx.commandFailed(InvoiceNotFound())
              ctx.done
          }
      }
      .onCommand[CreateCreditNote, Done] {
        case (CreateCreditNote(creditNote, _), ctx, state) if state.creditNotes.exists(_.creditNoteId == creditNote.creditNoteId) =>
          ctx.commandFailed(CreditNoteAlreadyExists())
          ctx.done
        case (CreateCreditNote(creditNote, _), ctx, state) if creditNote.invoice.isEmpty =>
          ctx.commandFailed(CreditNoteInvoiceNotFound())
          ctx.done
        case (CreateCreditNote(creditNote, _), ctx, state) if creditNote.account.isEmpty =>
          ctx.commandFailed(CreditNoteAccountNotFound())
          ctx.done
        case (CreateCreditNote(creditNote, beneficiaries), ctx, state) =>
          if (creditNote.invoice.get.beneficiaries.nonEmpty && !creditNote.invoice.get.beneficiaryUpdatedAfter(beneficiaries.head.updatedAt.getOrElse(System.currentTimeMillis()))) {
            ctx.thenPersistAll(CreditNoteCreated(creditNote),
              InvoiceBeneficiariesUpdated(creditNote.invoice.get.copy(beneficiaries = beneficiaries))
            )(() => ctx.reply(Done))
          } else {
              ctx.thenPersist(CreditNoteCreated(creditNote))(_ => ctx.reply(Done))
          }
      }
      .onEvent {
        case (InvoiceCreated(invoice), state) => state.copy(invoices = state.invoices :+ invoice)
        case (CreditNoteCreated(creditNote), state) => state.copy(creditNotes = state.creditNotes :+ creditNote)
        case (InvoiceBeneficiariesUpdated(invoice), state) => state.copy(
          invoices = state.invoices.filterNot(_.invoiceId == invoice.invoiceId) :+ invoice
        )
      }
  }

  private def payments = {
    Actions()
      .onCommand[CreateReconPendingPayment, Done] {
        case (CreateReconPendingPayment(pendingPayment), ctx, state)
          if state.pendingPayments.exists(_.paymentId == pendingPayment.paymentId) =>
          ctx.commandFailed(PendingPaymentAlreadyExists())
          ctx.done
        case (CreateReconPendingPayment(pendingPayment), ctx, state) if !state.invoices.exists(inv => pendingPayment.invoiceId == inv.invoiceId) =>
          ctx.commandFailed(PendingPaymentInvoiceNotFound())
          ctx.done
        case (CreateReconPendingPayment(pendingPayment), ctx, state) =>
          val portfolioId = state.invoices.find(_.invoiceId == pendingPayment.invoiceId).flatMap(_.portfolioId)
          ctx.thenPersist(ReconPendingPaymentCreated(pendingPayment.copy(portfolioId = portfolioId)))(_ => ctx.reply(Done))
      }
      .onCommand[CreateDepositPendingPayment, Done] {
        case (CreateDepositPendingPayment(pendingPayment, _, _), ctx, state)
          if state.pendingDepositPayments.exists(_.paymentId == pendingPayment.paymentId) || state.payments.exists(_.paymentId == pendingPayment.paymentId) =>
          ctx.commandFailed(PendingDepositPaymentAlreadyExists())
          ctx.done
        case (CreateDepositPendingPayment(pendingPayment, accountingPayment, isDummy), ctx, _) =>
          ctx.thenPersistAll(DepositPendingPaymentCreated(pendingPayment),
            WalletPaymentDepositEFT(accountingPayment, isDummy, EventRecord(Option.empty[String])))(() => ctx.reply(Done))
      }
      .onEvent {
        case (ReconPendingPaymentCreated(pendingPayment), state) => state.copy(pendingPayments = state.pendingPayments :+ pendingPayment)
        case (DepositPendingPaymentCreated(pendingPayment), state) => state.copy(pendingDepositPayments = state.pendingDepositPayments :+ pendingPayment )
        case (WalletPaymentDepositEFT(accountingPayment, _, _), state) =>
          state.copy(payments = state.payments.filterNot(_.paymentId == accountingPayment.paymentId) :+ accountingPayment)
      }
    }

  private def walletPayments = {
    Actions()
      .onCommand[CreateWalletPayment, Done] {
        case (CreateWalletPayment(accountingPayment), ctx, state) if state.payments.exists(_.paymentId == accountingPayment.paymentId) =>
          ctx.commandFailed(WalletPaymentAlreadyExists())
          ctx.done
        case (CreateWalletPayment(accountingPayment), ctx, _)
          if accountingPayment.walletPayment.from.account.nonEmpty && accountingPayment.walletPayment.fromAccount.isEmpty =>
          ctx.commandFailed(WalletPaymentFromAccountNotFound())
          ctx.done
        case (CreateWalletPayment(accountingPayment), ctx, _)
          if accountingPayment.walletPayment.to.account.nonEmpty && accountingPayment.walletPayment.toAccount.isEmpty =>
          ctx.commandFailed(WalletPaymentToAccountNotFound())
          ctx.done
        case (CreateWalletPayment(accountingPayment), ctx, state) =>
          val paymentCreatedEvent = WalletPaymentCreated(accountingPayment.copy(statuses = Seq.empty))
          val partiallyAddedTransfer = state.partiallyAddedTransfers.filter(_.transfer.paymentId.map(_.uuid.toString) == Some(accountingPayment.paymentId.uuid.toString))
          partiallyAddedTransfer.headOption match {
            case Some(walletTransfer) if !state.walletTransfers.exists(_.paymentId.map(_.uuid.toString).contains(accountingPayment.paymentId.uuid.toString)) =>
              val walletTransferEvent = WalletTransferCreated(walletTransfer.transfer.copy(payment = Some(accountingPayment.walletPayment)), walletTransfer.eventRecord)
              ctx.thenPersistAll(paymentCreatedEvent, walletTransferEvent)(() => ctx.reply(Done))
            case _ =>
              ctx.thenPersist(paymentCreatedEvent)(_ => ctx.reply(Done))
          }
      }
      .onEvent {
        case (WalletPaymentCreated(accountingPayment), state) => state.copy(payments = state.payments :+ accountingPayment)
      }
  }

  private def queryWalletPayments = {
    Actions()
      .onReadOnlyCommand[FindAccountingPayment, AccountingPayment] {
        case (FindAccountingPayment(payment), ctx, state) =>
          state.payments.find(_.paymentId == payment) match {
            case Some(accountingPayment) => ctx.reply(accountingPayment)
            case None => ctx.commandFailed(WalletPaymentNotFound())
          }
      }
      .onReadOnlyCommand[FindPendingPayment, PendingPayment] {
        case (FindPendingPayment(payment), ctx, state) =>
          state.pendingPayments.find(_.paymentId == payment) match {
            case Some(accountingPayment) => ctx.reply(accountingPayment)
            case None => ctx.commandFailed(WalletPaymentNotFound())
          }
      }
  }

  private def queryInvoices = {
    Actions()
      .onReadOnlyCommand[FindInvoice, Option[Invoice]] {
        case (FindInvoice(invoiceId), ctx, state) =>
          ctx.reply(state.invoices.find(_.invoiceId == invoiceId))
      }
      .onReadOnlyCommand[FindInvoices, Seq[Invoice]] {
        case (FindInvoices(invoices), ctx, state) =>
          ctx.reply(state.invoices.filter(inv => invoices.contains(inv.invoiceId.uuid)))
      }
  }

  private def walletStatuses = {
    Actions()
      .onCommand[VerifyPayment, Done] {
        case (VerifyPayment(payment, _), ctx, state) if !state.payments.exists(_.paymentId == payment) =>
          ctx.commandFailed(WalletPaymentNotFound())
          ctx.done
        case (VerifyPayment(payment, _), ctx, state) if state.payments.find(_.paymentId == payment).forall(_.statuses.contains("verified")) =>
          ctx.commandFailed(WalletPaymentAlreadyVerified())
          ctx.done
        case (VerifyPayment(payment, verifiedAt), ctx, state) =>
          val accountingPayment = state.payments.find(_.paymentId == payment).map(p => p.copy(statuses = p.statuses :+ "verified")).get
          ctx.thenPersist(WalletPaymentVerified(accountingPayment, EventRecord(Option.empty[String], verifiedAt)))(_ => ctx.reply(Done))
      }
      .onEvent {
        case (WalletPaymentVerified(accountingPayment, _), state) =>
          state.copy(payments = state.payments.filterNot(_.paymentId == accountingPayment.paymentId) :+ accountingPayment)
      }
  }

  private def walletRejection = {
    Actions()
      .onCommand[RejectPayment, Done] {
        case (RejectPayment(payment, _, _), ctx, state) if !state.payments.exists(_.paymentId == payment) =>
          ctx.commandFailed(WalletPaymentNotFound())
          ctx.done
        case (RejectPayment(payment, _, _), ctx, state) if state.payments.find(_.paymentId == payment).forall(_.statuses.contains("rejected")) =>
          ctx.commandFailed(WalletPaymentAlreadyRejected())
          ctx.done
        case (RejectPayment(payment, error, failedAt), ctx, state) =>
          val accountingPayment = state.payments.find(_.paymentId == payment).map(p => p.copy(statuses = p.statuses :+ "rejected")).get
          ctx.thenPersist(WalletPaymentRejected(accountingPayment, error, EventRecord(Option.empty[String], failedAt)))(_ => ctx.reply(Done))
      }
      .onCommand[RejectPaymentRequest, Done] {
        case (RejectPaymentRequest(payment, _, _), ctx, state) if !state.payments.exists(_.paymentId == payment) =>
          ctx.commandFailed(WalletPaymentNotFound())
          ctx.done
        case (RejectPaymentRequest(payment, _, _), ctx, state) if state.payments.find(_.paymentId == payment).forall(_.statuses.contains("request_rejected")) =>
          ctx.commandFailed(WalletPaymentRequestAlreadyRejected())
          ctx.done
        case (RejectPaymentRequest(payment, errors, failedAt), ctx, state) =>
          val accountingPayment = state.payments.find(_.paymentId == payment).map(p => p.copy(statuses = p.statuses :+ "request_rejected")).get
          ctx.thenPersist(WalletPaymentRequestRejected(accountingPayment, errors, EventRecord(Option.empty[String], failedAt)))(_ => ctx.reply(Done))
      }
      .onEvent {
        case (WalletPaymentRejected(accountingPayment, _, _), state) =>
          state.copy(payments = state.payments.filterNot(_.paymentId == accountingPayment.paymentId) :+ accountingPayment)
        case (WalletPaymentRequestRejected(accountingPayment, _, _), state) =>
          state.copy(payments = state.payments.filterNot(_.paymentId == accountingPayment.paymentId) :+ accountingPayment)
      }
  }

  private def walletEft = {
    Actions()
      .onCommand[RequestEFTPayment, Done] {
        case (RequestEFTPayment(payment, _), ctx, state) if !state.payments.exists(_.paymentId == payment) =>
        ctx.commandFailed(WalletPaymentNotFound())
        ctx.done
        case (RequestEFTPayment(payment, _), ctx, state) if state.payments.find(_.paymentId == payment).forall(_.statuses.contains("eft_requested")) =>
          ctx.commandFailed(WalletPaymentEFTAlreadyRequested())
          ctx.done
        case (RequestEFTPayment(payment, isDummy), ctx, state) =>
          val accountingPayment = state.payments.find(_.paymentId == payment).map(p => p.copy(statuses = p.statuses :+ "eft_requested")).get
          ctx.thenPersist(WalletPaymentEFT(accountingPayment, isDummy, EventRecord(Option.empty[String])))(_ => ctx.reply(Done))
      }
      .onEvent {
        case (WalletPaymentEFT(accountingPayment, _, _), state) =>
          state.copy(payments = state.payments.filterNot(_.paymentId == accountingPayment.paymentId) :+ accountingPayment)
      }
  }

  private def easypayEft = {
    Actions()
      .onCommand[RequestEasypayPayment, Done] {
        case (RequestEasypayPayment(payment, _, _), ctx, state) if !state.payments.exists(_.paymentId == payment) =>
          ctx.commandFailed(WalletPaymentNotFound())
          ctx.done
        case (RequestEasypayPayment(payment, _, _), ctx, state) if state.payments.find(_.paymentId == payment).forall(_.statuses.contains("eft_requested")) =>
          ctx.commandFailed(WalletPaymentEFTAlreadyRequested())
          ctx.done
        case (RequestEasypayPayment(payment, isDummy, details), ctx, state) =>
          val accountingPayment = state.payments.find(_.paymentId == payment).map(p => p.copy(statuses = p.statuses :+ "eft_requested")).get
          ctx.thenPersist(WalletPaymentEasypay(accountingPayment, isDummy, details, EventRecord(Option.empty[String])))(_ => ctx.reply(Done))
      }
      .onEvent {
        case (WalletPaymentEasypay(accountingPayment, _, _, _), state) =>
          state.copy(payments = state.payments.filterNot(_.paymentId == accountingPayment.paymentId) :+ accountingPayment)
      }
  }

  private def walletTransfer = {
    def walletTransferExists(walletTransfer: WalletTransfer, state: PartyAccountingState): Boolean = {
      val transaction = walletTransfer.transaction
      val transfers = state.walletTransfers.filter(_.transaction == transaction) ++ state.partiallyAddedTransfers.map(_.transfer).filter(_.transaction == transaction)
      val normalized = transfers.map(_.copy(fromAccount = Option.empty, toAccount = Option.empty, payment = Option.empty))
      val walletTransferNormalized = walletTransfer.copy(fromAccount = Option.empty, toAccount = Option.empty, payment = Option.empty)
      normalized.contains(walletTransferNormalized)
    }
    Actions()
      .onCommand[CreateWalletTransfer, Done] {
        case (CreateWalletTransfer(walletTransfer, _, fromPartyAccount, _), ctx, state)
          if walletTransfer.from.account.nonEmpty && fromPartyAccount.isEmpty =>
          ctx.commandFailed(WalletPaymentFromAccountNotFound())
          ctx.done
        case (CreateWalletTransfer(walletTransfer, toPartyAccount, _, _), ctx, state)
          if walletTransfer.to.account.nonEmpty && toPartyAccount.isEmpty =>
          ctx.commandFailed(WalletPaymentToAccountNotFound())
          ctx.done
        case (CreateWalletTransfer(walletTransfer, _, _, _), ctx, state) if walletTransferExists(walletTransfer, state) =>
          ctx.commandFailed(WalletTransferAlreadyCreated(walletTransfer.transaction))
          ctx.done
        case (CreateWalletTransfer(walletTransfer, toPartyAccount, fromPartyAccount, transferredAt), ctx, state) =>
          val modifiedWalletTransfer = walletTransfer.copy(fromAccount = fromPartyAccount, toAccount = toPartyAccount)
          import com.phoenix.util.UUID
          def isPartiallyCreated(id: UUID) = !state.payments.exists(_.paymentId == id) && entityId != "<EMAIL>"
          walletTransfer.paymentId match {
            case Some(id) if isPartiallyCreated(id) =>
              ctx.thenPersist(WalletTransferPartiallyCreated(PartiallyCreatedTransfer(modifiedWalletTransfer, EventRecord(Option.empty[String], transferredAt))))(_ => ctx.reply(Done))
            case paymentId =>
              val payment = paymentId.flatMap(id => state.payments.find(_.paymentId == id)).map(_.walletPayment)
              ctx.thenPersist(WalletTransferCreated(modifiedWalletTransfer.copy(payment = payment), EventRecord(Option.empty[String], transferredAt)))(_ => ctx.reply(Done))
          }
      }
      .onEvent {
        case (WalletTransferCreated(walletTransfer, _), state) => state.copy(
          walletTransfers = state.walletTransfers :+ walletTransfer,
          partiallyAddedTransfers = state.partiallyAddedTransfers.filterNot(_.transfer.paymentId == walletTransfer.paymentId))
        case (WalletTransferPartiallyCreated(walletTransfer), state) => state.copy(partiallyAddedTransfers = state.partiallyAddedTransfers :+ walletTransfer)
      }
  }

  private def accounts = {
    Actions()
      .onEvent {
        case (_: AccountCreated, state) => state
        case (_: AccountUpdated, state) => state
      }
  }

  private def blockchain = {
    Actions()
      .onCommand[CreateBlockchainTransfers, Done] {
        case (CreateBlockchainTransfers(transfers, transferredAt), ctx, state) =>
          val events = transfers.map(t => BlockchainTransferCreated(t, EventRecord(t.authorizedBy.map(_.toString), transferredAt)))
          ctx.thenPersistAll(events:_ *)(() => ctx.reply(Done))
      }
      .onEvent {
        case (_: BlockchainTransferCreated, state) => state
      }
  }

  private def depositFeeUpdated = {
    Actions()
      .onCommand[UpdateDepositFeeForTransaction, Done] {
        case (cmd: UpdateDepositFeeForTransaction, ctx, state) =>
          import cmd._
          val event = DepositFeeUpdated(transaction, partyAccount, receiptAmount, receiptTimestamp, identifier,
            reference, cashDepositFee, chargeLevied, assetName, precision, eventRecord)
          ctx.thenPersist(event)(_ => ctx.reply(Done))
      }
      .onEvent {
        case (_: DepositFeeUpdated, state) => state
      }
  }
}


case class CreditNoteAccountNotFound() extends Exception("Account not found for credit note") with NoStackTrace
object CreditNoteAccountNotFound {
  implicit val format: Format[CreditNoteAccountNotFound] = JsonFormats.singletonFormat(CreditNoteAccountNotFound())
}

case class CreditNoteInvoiceNotFound() extends Exception("Invoice not found for credit note") with NoStackTrace
object CreditNoteInvoiceNotFound {
  implicit val format: Format[CreditNoteInvoiceNotFound] = JsonFormats.singletonFormat(CreditNoteInvoiceNotFound())
}

case class CreditNoteAlreadyExists() extends Exception("Credit note already exists in entity") with NoStackTrace
object CreditNoteAlreadyExists {
  implicit val format: Format[CreditNoteAlreadyExists] = JsonFormats.singletonFormat(CreditNoteAlreadyExists())
}

case class InvoiceAlreadyExists() extends Exception("Invoice already exists in entity") with NoStackTrace
object InvoiceAlreadyExists {
  implicit val format: Format[InvoiceAlreadyExists] = JsonFormats.singletonFormat(InvoiceAlreadyExists())
}

case class InvoiceNotFound() extends Exception("Invoice not found in entity") with NoStackTrace
object InvoiceNotFound {
  implicit val format: Format[InvoiceNotFound] = JsonFormats.singletonFormat(InvoiceNotFound())
}

case class BeneficiariesUpdatedRecently() extends Exception("Beneficiaries updated more recently") with NoStackTrace
object BeneficiariesUpdatedRecently {
  implicit val format: Format[BeneficiariesUpdatedRecently] = JsonFormats.singletonFormat(BeneficiariesUpdatedRecently())
}

case class PendingPaymentInvoiceNotFound() extends Exception("Invoice not found for pending payment") with NoStackTrace
object PendingPaymentInvoiceNotFound {
  implicit val format: Format[PendingPaymentInvoiceNotFound] = JsonFormats.singletonFormat(PendingPaymentInvoiceNotFound())
}

case class PendingPaymentAlreadyExists() extends Exception("Pending payment already exists in entity") with NoStackTrace
object PendingPaymentAlreadyExists {
  implicit val format: Format[PendingPaymentAlreadyExists] = JsonFormats.singletonFormat(PendingPaymentAlreadyExists())
}

case class PendingDepositPaymentAlreadyExists() extends Exception("Pending deposit payment already exists in the entity") with NoStackTrace
object PendingDepositPaymentAlreadyExists {
  implicit val format: Format[PendingDepositPaymentAlreadyExists] = JsonFormats.singletonFormat(PendingDepositPaymentAlreadyExists())
}

case class WalletPaymentAlreadyVerified() extends Exception("Wallet payment already verified entity") with NoStackTrace
object WalletPaymentAlreadyVerified {
  implicit val format: Format[WalletPaymentAlreadyVerified] = JsonFormats.singletonFormat(WalletPaymentAlreadyVerified())
}

case class WalletPaymentAlreadyRejected() extends Exception("Wallet payment already rejected in entity") with NoStackTrace
object WalletPaymentAlreadyRejected {
  implicit val format: Format[WalletPaymentAlreadyRejected] = JsonFormats.singletonFormat(WalletPaymentAlreadyRejected())
}

case class WalletPaymentRequestAlreadyRejected() extends Exception("Wallet payment request already rejected in entity") with NoStackTrace
object WalletPaymentRequestAlreadyRejected {
  implicit val format: Format[WalletPaymentRequestAlreadyRejected] = JsonFormats.singletonFormat(WalletPaymentRequestAlreadyRejected())
}

case class WalletPaymentEFTAlreadyRequested() extends Exception("Wallet payment already requested in entity") with NoStackTrace
object WalletPaymentEFTAlreadyRequested {
  implicit val format: Format[WalletPaymentEFTAlreadyRequested] = JsonFormats.singletonFormat(WalletPaymentEFTAlreadyRequested())
}

case class WalletPaymentNotFound() extends Exception("Wallet payment not found in entity") with NoStackTrace
object WalletPaymentNotFound {
  implicit val format: Format[WalletPaymentNotFound] = JsonFormats.singletonFormat(WalletPaymentNotFound())
}

case class WalletPaymentAlreadyExists() extends Exception("Wallet payment already exists in entity") with NoStackTrace
object WalletPaymentAlreadyExists {
  implicit val format: Format[WalletPaymentAlreadyExists] = JsonFormats.singletonFormat(WalletPaymentAlreadyExists())
}

case class WalletPaymentFromAccountNotFound() extends Exception("From account not found for wallet payment") with NoStackTrace
object WalletPaymentFromAccountNotFound {
  implicit val format: Format[WalletPaymentFromAccountNotFound] = JsonFormats.singletonFormat(WalletPaymentFromAccountNotFound())
}

case class WalletPaymentToAccountNotFound() extends Exception("To account not found for wallet payment") with NoStackTrace
object WalletPaymentToAccountNotFound {
  implicit val format: Format[WalletPaymentToAccountNotFound] = JsonFormats.singletonFormat(WalletPaymentToAccountNotFound())
}

case class WalletTransferAlreadyCreated(transaction: String) extends Exception(s"Wallet transfer already exists in entity: $transaction") with NoStackTrace
object WalletTransferAlreadyCreated {
  implicit val format: Format[WalletTransferAlreadyCreated] = Json.format
}

case class PartyAccountAlreadyExists() extends Exception("Party account already exists and does not need updating") with NoStackTrace
object PartyAccountAlreadyExists {
  implicit val format: Format[PartyAccountAlreadyExists] = JsonFormats.singletonFormat(PartyAccountAlreadyExists())
}
