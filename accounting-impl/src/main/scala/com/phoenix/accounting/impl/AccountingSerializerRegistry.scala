package com.phoenix.accounting.impl

import com.lightbend.lagom.scaladsl.playjson.JsonSerializer
import com.lightbend.lagom.scaladsl.playjson.JsonSerializerRegistry
import com.phoenix.accounting.api.Base64PdfResponse
import com.phoenix.accounting.api.JsonResponse
import com.phoenix.accounting.api.StatementItem
import com.phoenix.accounting.api.StatementPeriod
import com.phoenix.accounting.impl.connectors.Party
import com.phoenix.util.JsonSerializerImpl.generateJsonSerializersFor

object AccountingSerializerRegistry extends JsonSerializerRegistry {
  private def api = {
    import com.phoenix.accounting.api._
    Seq(
      JsonSerializer[StatementPartyDetails],
      <PERSON>sonSerializer[StatementPeriod],
      JsonSerializer[StatementItem],
      JsonSerializer[CustomerStatement]
    )
  }

  private def state = {
    import com.phoenix.persistence._
    Seq(
      JsonSerializer[StateRecord],
      JsonSerializer[CreatedState],
      <PERSON>sonSerializer[DeletedState],
      JsonSerializer[EventRecord],
      JsonSerializer[BatchFeedState],
      JsonSerializer[PartyAccountingState]
    )
  }

  private def requests = Seq()

  private def responses = Seq(
    JsonSerializer[Base64PdfResponse],
    JsonSerializer[JsonResponse]
  )

  private def objects = {
    import com.phoenix.accounting.impl.entities._
    Seq(
      JsonSerializer[IdAlreadyExists],
      JsonSerializer[EventTooOld],
      JsonSerializer[Party],
      JsonSerializer[CreditNoteAccountNotFound],
      JsonSerializer[CreditNoteInvoiceNotFound],
      JsonSerializer[CreditNoteAlreadyExists],
      JsonSerializer[InvoiceAlreadyExists],
      JsonSerializer[InvoiceNotFound],
      JsonSerializer[BeneficiariesUpdatedRecently],
      JsonSerializer[PendingPaymentInvoiceNotFound],
      JsonSerializer[PendingPaymentAlreadyExists],
      JsonSerializer[PendingDepositPaymentAlreadyExists],
      JsonSerializer[WalletPaymentAlreadyVerified],
      JsonSerializer[WalletPaymentAlreadyRejected],
      JsonSerializer[WalletPaymentRequestAlreadyRejected],
      JsonSerializer[WalletPaymentEFTAlreadyRequested],
      JsonSerializer[WalletPaymentNotFound],
      JsonSerializer[WalletPaymentAlreadyExists],
      JsonSerializer[WalletPaymentFromAccountNotFound],
      JsonSerializer[WalletPaymentToAccountNotFound],
      JsonSerializer[WalletTransferAlreadyCreated],
      JsonSerializer[PartyAccountAlreadyExists]
    )
  }

  private def events = {
    import com.phoenix.accounting.impl.events._
    generateJsonSerializersFor[PartyAccountingEvent] ++ generateJsonSerializersFor[BatchFeedEvent]
  }

  private def commands = {
    import com.phoenix.accounting.impl.commands._
    Seq(
      JsonSerializer[StatementPeriod],
      JsonSerializer[StatementItem]
    ) ++ generateJsonSerializersFor[PartyAccountingCommands[_]] ++ generateJsonSerializersFor[BatchFeedCommand[_]]

  }

  override def serializers: Seq[JsonSerializer[_]] = api ++ state ++ requests ++ responses ++ events ++ commands ++ objects
}
