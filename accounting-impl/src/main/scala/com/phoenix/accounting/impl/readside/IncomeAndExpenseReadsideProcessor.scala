package com.phoenix.accounting.impl.readside

import akka.Done
import akka.event.Logging
import com.datastax.driver.core.BoundStatement
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraReadSide
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, EventStreamElement, PersistentEntityRegistry, ReadSideProcessor}
import com.phoenix.accounting.impl.Invoice
import com.phoenix.accounting.impl.commands.PartyAccountingCommands.FindInvoice
import com.phoenix.accounting.impl.connectors._
import com.phoenix.accounting.impl.entities.PartyAccountingEntity
import com.phoenix.accounting.impl.events.{CreditNoteCreated, InvoiceBeneficiariesUpdated, InvoiceCreated, PartyAccountingEvent}
import com.phoenix.common.data.ReadsideDatabase
import com.phoenix.invoice.api.Accounting.{GrossExpense, NoVatGroup, VatGroup15}
import com.phoenix.invoice.api.InvoiceType
import com.phoenix.logging.EventLogging
import com.phoenix.party.api.AccountType
import com.phoenix.util.UUID
import com.typesafe.scalalogging.LazyLogging

import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}

class IncomeAndExpenseReadsideProcessor(readsideDatabase: ReadsideDatabase,
                                        readSide: CassandraReadSide,
                                        persistentEntityRegistry: PersistentEntityRegistry,
                                        portfolioConnector: PortfolioConnector,
                                        incomeAndExpenseConnector: IncomeAndExpenseConnector)
                                       (implicit ec: ExecutionContext)
   extends ReadSideProcessor[PartyAccountingEvent] with EventLogging with LazyLogging {
  override def readSideName: String = Logging.simpleName(getClass) + "@240410"

   override def aggregateTags: Set[AggregateEventTag[PartyAccountingEvent]] = Set(PartyAccountingEvent.Tag)

   //scalastyle:off
   override def buildHandler(): ReadSideProcessor.ReadSideHandler[PartyAccountingEvent] = {
     readSide.builder[PartyAccountingEvent]("incomeAndExpenseReadsideProcessor")
       .setGlobalPrepare(() => Future { readsideDatabase.migrate() })
       .setEventHandler[InvoiceCreated] {
         case EventStreamElement(_, InvoiceCreated(invoice), _) if invoice.portfolioId.nonEmpty && invoice.accountType == AccountType.Owner =>
           // create landlord expense if invoice is to a landlord
           val incomeAccount = InvoiceType.invoiceTypeSummary(InvoiceType(invoice.invoiceType)).incomeType
           for {
             portfolio <- portfolioConnector.find(invoice.agencyId, invoice.portfolioId.get)
             entry     <- portfolio match {
               case None    => Future( Done )
               case Some(p) =>
                 val entry = IncomeAndExpenseEntry(invoice.agencyId, p.propertyId, p.portfolioId, invoice.invoiceId,
                   invoice.invoiceId, Expense, invoice.name, IncomeAccount(incomeAccount), invoice.forMonth,
                   invoice.vatAmount, invoice.netAmount, invoice.grossAmount, invoice.invoiceType, Some(invoice.invoiceType))
                 incomeAndExpenseConnector.upsert(entry)
             }
           } yield ok
         case EventStreamElement(_, InvoiceCreated(invoice), _) if invoice.portfolioId.nonEmpty =>
           // create income and expenses
           val incomeAccount = InvoiceType.invoiceTypeSummary(InvoiceType(invoice.invoiceType)).incomeType

           for {
             portfolio <- portfolioConnector.find(invoice.agencyId, invoice.portfolioId.get)
             entries    = portfolio match {
                           case None    => Seq.empty[IncomeAndExpenseEntry]
                           case Some(p) => {
                             val entry   = IncomeAndExpenseEntry(invoice.agencyId, p.propertyId, p.portfolioId, invoice.invoiceId,
                               invoice.invoiceId, Income, invoice.name, IncomeAccount(incomeAccount), invoice.forMonth,
                               invoice.vatAmount, invoice.netAmount, invoice.grossAmount, invoice.invoiceType,
                               Option.empty[String])

                             val expenses = refreshedExpenses(invoice, p)

                             entry +: expenses
                           }
                         }
             _        <- Future.sequence(entries.map(incomeAndExpenseConnector.upsert))


           } yield ok
         case _ => Future(ok)
       }
       .setEventHandler[InvoiceBeneficiariesUpdated] {
         case EventStreamElement(_, InvoiceBeneficiariesUpdated(invoice), _) if invoice.portfolioId.nonEmpty && invoice.accountType == AccountType.Tenant => for {
           portfolio <- portfolioConnector.find(invoice.agencyId, invoice.portfolioId.get)
           entries   <- portfolio match {
                          case None    => Future(ok)
                          case Some(p) => for {
                            _       <- incomeAndExpenseConnector.removeExpenses(invoice.agencyId, p.propertyId, p.portfolioId, invoice.invoiceId)
                            entries  = refreshedExpenses(invoice, p)
                            _       <- Future.sequence(entries.map(incomeAndExpenseConnector.upsert))
                          } yield ok
                        }

         } yield entries
         case _ => Future(ok)
       }
       .setEventHandler[CreditNoteCreated] {
         case EventStreamElement(_, CreditNoteCreated(creditNote), _) => for {
           invoice <- persistentEntityRegistry.refFor[PartyAccountingEntity](creditNote.partyId).ask(FindInvoice(creditNote.invoiceId))
           _ <- invoice match {
             case Some(inv) if inv.portfolioId.nonEmpty => for {
               portfolio <- portfolioConnector.find(inv.agencyId, inv.portfolioId.get)
               incomeAccount = InvoiceType.invoiceTypeSummary(InvoiceType(inv.invoiceType)).incomeType
               negativeIncome = inv.vatAmount match {
                 case x if x > BigDecimal(0) => GrossExpense(creditNote.amount, VatGroup15())
                 case _                      => GrossExpense(creditNote.amount, NoVatGroup())
               }

               _ <- portfolio match {
                  case Some(p) => {
                    val entry = IncomeAndExpenseEntry(creditNote.agencyId, p.propertyId, p.portfolioId,
                            inv.invoiceId, creditNote.creditNoteId, Income, creditNote.name, IncomeAccount(incomeAccount), inv.forMonth,
                            -negativeIncome.vat, -negativeIncome.net, -negativeIncome.gross, inv.invoiceType, Option.empty[String]
                          )
                    incomeAndExpenseConnector.upsert(entry)
                  }
                  case None => Future( Done )
               }
             } yield ok
             case _ => Future(ok)
           }

         } yield ok
         case _ => Future(ok)
       }
       .build()
   }

   private def refreshedExpenses(invoice: Invoice, portfolio: Portfolio): Seq[IncomeAndExpenseEntry] = {
     val incomeAccount = InvoiceType.invoiceTypeSummary(InvoiceType(invoice.invoiceType)).incomeType
     invoice.beneficiaries.filter(_.amount.nonEmpty).map(ben => {
       val expense = ben.vat match {
         case Some(x) if x => GrossExpense(ben.amount.get, VatGroup15())
         case _ => GrossExpense(ben.amount.get, NoVatGroup())
       }
       IncomeAndExpenseEntry(invoice.agencyId, portfolio.propertyId, portfolio.portfolioId, invoice.invoiceId,
         ben.beneficiaryId.getOrElse(UUID.createV4), Expense, ben.category,
         IncomeAccount(incomeAccount), invoice.forMonth, expense.vat, expense.net, expense.gross,
         invoice.invoiceType, Some(ben.category)
       )
     })
   }

   val ok: immutable.Seq[BoundStatement] = List()
 }

