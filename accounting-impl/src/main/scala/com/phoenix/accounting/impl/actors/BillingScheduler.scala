package com.phoenix.accounting.impl.actors

import akka.actor.typed.scaladsl.Behaviors
import akka.actor.typed.{ActorRef, Behavior, SupervisorStrategy}
import akka.cluster.typed.{Cluster<PERSON>ingleton, SingletonActor}
import akka.stream.Materializer
import akka.stream.scaladsl.{Sink, Source}
import com.phoenix.accounting.impl.connectors.{AgencyConnector, BillingConnector}
import com.phoenix.accounting.impl.graphs.BillingCalculationGraph
import com.phoenix.accounting.impl.reports.ReceiptFeeReportImpl
import com.phoenix.date.DateUtcUtil
import org.joda.time.DateTime
import slack.api.SlackApiClient

import java.util.concurrent.TimeUnit
import scala.concurrent.ExecutionContext
import scala.concurrent.duration._
import scala.util.{Failure, Success}

object BillingScheduler {
  sealed trait Command
  case object BillingTick extends Command
  case object CompleteMessage extends Command
  case class ErrorMessage(error: Throwable) extends Command
}

class BillingScheduler(billingConnector: BillingConnector,
                       receiptFeeReportImpl: ReceiptFeeReportImpl,
                       singletonManager: ClusterSingleton,
                       agencyConnector: AgencyConnector,
                       slackClient: Option[SlackApiClient])
                      (implicit mat: Materializer) {

  import BillingScheduler._

  private def supervise(behavior: Behavior[Command]) = {
    Behaviors.supervise[Command](behavior)
      .onFailure[Exception](SupervisorStrategy.restart.withLimit(-1, 5.minutes))
  }

  private def timer(behavior: Behavior[Command]): Behavior[Command] = {
    val TIMER_KEY = "billing-tick"
    Behaviors.setup { context =>
      Behaviors.withTimers { timers =>
        val delay = context.system.settings.config.getDuration("billingSchedulerDelay", TimeUnit.MILLISECONDS).milliseconds
        timers.startPeriodicTimer(TIMER_KEY, BillingTick, delay)
        behavior
      }
    }
  }

  private def billingSchedulerBehaviour: Behavior[Command] = {
    Behaviors.receivePartial {
      case (context, BillingTick) =>
        implicit val ec: ExecutionContext = context.executionContext
        val logger = context.log

        val futureResult = billingConnector.fetch(BillingConnector.BillingCalculation)
          .filter(_.asset == "zar#phoenix.coin")
          .filter(!_.hasStarted)
          .filter(r => {
            val currentDate = DateUtcUtil.now()
            val testDate = currentDate.withDate(r.year, r.month, 1)
            currentDate.isAfter(testDate.dayOfMonth().withMaximumValue())
          })
          .take(1)
          .mapAsync(1) { row =>
            logger.info("Running billing calculation for {} {}", row.year, row.month)
            billingConnector.updateStarted(BillingConnector.BillingCalculation, row.asset, row.year, row.month, hasStarted = true)
              .map(_ => row)
          }
          .flatMapConcat(row =>
            Source.fromGraph(BillingCalculationGraph(row.asset, row.year, row.month)
                                                    (mat.system, receiptFeeReportImpl, billingConnector, agencyConnector, slackClient))
          )
          .runWith(Sink.seq)
        context.pipeToSelf(futureResult) {
          case Success(output) => CompleteMessage
          case Failure(exception) => ErrorMessage(exception)
        }
        Behaviors.same
      case (_, ErrorMessage(ex)) => throw ex
      case (context, CompleteMessage) =>
        Behaviors.same
    }
  }

  def initWithTimers: ActorRef[Command] =
    singletonManager.init(SingletonActor(supervise(timer(billingSchedulerBehaviour)), "BillingScheduler"))

  def init: ActorRef[Command] =
    singletonManager.init(SingletonActor(supervise(billingSchedulerBehaviour), "BillingScheduler"))
}
