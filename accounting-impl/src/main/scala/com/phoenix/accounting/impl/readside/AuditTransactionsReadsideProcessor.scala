package com.phoenix.accounting.impl.readside

import akka.Done
import akka.actor.ActorSystem
import akka.event.Logging
import com.datastax.driver.core.BoundStatement
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraReadSide
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, EventStreamElement, ReadSideProcessor}
import com.phoenix.accounting.impl.WalletAddresses._
import com.phoenix.accounting.impl.connectors.{AuditTransactionRow, AuditTransactionsConnector, PartyConnector, PortfolioConnector}
import com.phoenix.accounting.impl.events.{BlockchainTransferCreated, PartyAccountingEvent}
import com.phoenix.common.data.ReadsideDatabase
import com.phoenix.date.DateUtcUtil
import com.phoenix.logging.EventLogging
import com.phoenix.util.UUID
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.DateTime
import slack.api.SlackApiClient
import slack.models.{Divider, MarkdownTextObject, Section}

import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}

class AuditTransactionsReadsideProcessor(readsideDatabase: ReadsideDatabase,
                                         readSide: CassandraReadSide,
                                         partyConnector: PartyConnector,
                                         auditConnector: AuditTransactionsConnector,
                                         portfolioConnector: PortfolioConnector,
                                         slackApiClient: Option[SlackApiClient])
                                        (implicit ac: ActorSystem)
  extends ReadSideProcessor[PartyAccountingEvent] with EventLogging with LazyLogging {

  implicit val ec: ExecutionContext = ac.dispatcher
  private val slackChannelId = "C06N7RELWJ0" // #system-fic-alerts
  val slackAlertCashThreshold: Int = 50000 * 100 // cents

  val cashWallets: Seq[String] = Seq(eftIncoming, eftOutgoing, depositIncoming, depositOutgoing, depositInterest, easypayOutgoing)

  override def aggregateTags: Set[AggregateEventTag[PartyAccountingEvent]] = Set(PartyAccountingEvent.Tag)

  def finYear(timestamp: DateTime): String =
    if (timestamp.getMonthOfYear > 2) {
      s"${timestamp.getYear}/${timestamp.getYear + 1}"
    } else {
      s"${timestamp.getYear - 1}/${timestamp.getYear}"
    }

  // scalastyle:off
  override def buildHandler(): ReadSideProcessor.ReadSideHandler[PartyAccountingEvent] = {
    readSide.builder[PartyAccountingEvent]("auditTransactionsReadsideProcessor")
      .setGlobalPrepare(() => Future { readsideDatabase.migrate() })
      .setEventHandler[BlockchainTransferCreated] {
        case EventStreamElement(_, BlockchainTransferCreated(walletTransfer, eventRecord), _) =>
          val timestamp = DateUtcUtil.now().withMillis(eventRecord.createdAt)
          val payment = walletTransfer.payment
          val invoice = payment.flatMap(_.invoice)
          val receipt = walletTransfer.receipt
          val agencyId = invoice.map(_.agencyId)
          val portfolioId = invoice.flatMap(_.portfolioId).map(_.uuid)

          for {
            toPartyFullName <- walletTransfer.toAccount.map(_.partyId) match {
              case Some(id) => partyConnector.find(id).map(_.map(_.fullName))
              case None => Future(Option.empty[String])
            }

            fromPartyFullName <- walletTransfer.fromAccount.map(_.partyId) match {
              case Some(id) => partyConnector.find(id).map(_.map(_.fullName))
              case None => Future(Option.empty[String])
            }

            propertyId <- (agencyId, portfolioId) match {
              case (Some(a), Some(p)) => portfolioConnector.find(a, p).map(_.map(_.propertyId))
              case _ => Future(Option.empty[UUID])
            }

            row = AuditTransactionRow(
              walletTransactionUid = walletTransfer.uid,
              asset = walletTransfer.asset.name,
              financialYear = finYear(timestamp),
              transactionTimestamp = timestamp,
              transactionPosition = walletTransfer.uid,
              fromWalletRef = walletTransfer.from.wallet,
              toWalletRef = walletTransfer.to.wallet,
              fromFullname = fromPartyFullName,
              toFullname = toPartyFullName,
              fromAddress = walletTransfer.fromAccount.flatMap(_.address),
              toAddress = walletTransfer.toAccount.flatMap(_.address),
              fromAccountId = walletTransfer.fromAccount.map(_.accountId),
              toAccountId = walletTransfer.toAccount.map(_.accountId),
              description = walletTransfer.description,
              reference = payment.map(_.reference).orElse(receipt.flatMap(_.receiptReference)),
              paymentId = walletTransfer.paymentId,
              authorizedBy = walletTransfer.authorizedBy.orElse(payment.flatMap(_.approvedBy).map(_.uuid)),
              amount = walletTransfer.asset.amount,
              beneficiaryCategory = payment.map(_.beneficiaryCategory.getObjectName),
              approvedAt = walletTransfer.payment.map(opt => DateUtcUtil.now().withMillis(opt.approvedAt)),
              invoiceId = invoice.map(_.invoiceId),
              invoicePartyId = invoice.map(_.partyId),
              portfolioId = invoice.flatMap(_.portfolioId).map(_.uuid),
              propertyId = propertyId.map(_.uuid),
              invoiceAccountType = invoice.map(_.accountType.getObjectName),
              invoiceType = invoice.map(_.invoiceType),
              invoiceDueDate = invoice.map(_.dueDate),
              invoiceGrossAmount = invoice.map(_.grossAmount),
              invoiceVatAmount = invoice.map(_.vatAmount),
              invoiceNetAmount = invoice.map(_.netAmount),
              invoiceNumber = invoice.map(_.invoiceNumber),
              invoiceCreatedDate = invoice.map(_.createdDate),
              invoiceDescription = invoice.flatMap(_.description),
              bankingUid = receipt.map(_.bankingUid),
              cashDepositFee = receipt.flatMap(_.cashDepositFee).map(_.amount),
              externalBank = receipt.flatMap(_.externalBank),
              internalBank = receipt.flatMap(_.internalBank)
            )

            _ <- auditConnector.upsert(row)
            _ <- sendSlackAlert(row)
          } yield ok
      }
      .build()
  }

  def sendSlackAlert(row: AuditTransactionRow): Future[Done] = {
    if (slackApiClient.isDefined && row.amount >= slackAlertCashThreshold &&
      (cashWallets.contains(row.fromWalletRef) || cashWallets.contains(row.toWalletRef)) &&
      row.cashDepositFee.getOrElse(BigDecimal(0.00)) > BigDecimal(0.00)) {
      val msg = Seq(
        Section(MarkdownTextObject(":small_red_triangle: Banking transaction exceeds threshold:"),
          Some(Seq(
            MarkdownTextObject(s"*Date:*\t${row.transactionTimestamp}"),
            MarkdownTextObject(s"*From:*\t${row.fromFullname.getOrElse("-")}"),
            MarkdownTextObject(s"*From wallet:*\t${row.fromWalletRef}"),
            MarkdownTextObject(s"*To:*\t${row.toFullname.getOrElse("-")}"),
            MarkdownTextObject(s"*To wallet:*\t${row.toWalletRef}"),
            MarkdownTextObject(s"*Description:*\t${row.description}"),
            MarkdownTextObject(s"*Amount:*\t${row.amount}"),
            MarkdownTextObject(s"*Banking UID:*\t${row.bankingUid.getOrElse("-")}")
          )),
          None, None),
        Divider())
      slackApiClient.get.postChatMessage(channelId = slackChannelId, text = "", blocks = Some(msg))(ac)
    }
    Future.successful(Done)
  }

  val ok: immutable.Seq[BoundStatement] = List()

  override def readSideName: String = Logging.simpleName(getClass) + "@240610"
}
