package com.phoenix.agency.impl

import akka.Done
import com.phoenix.agency.impl.connectors._
import com.phoenix.agency.impl.integrations.IntegrationType
import com.phoenix.common.data.ReadsideDatabase
import org.scalatest.BeforeAndAfterAll
import org.scalatest.funspec.AnyFunSpec

import java.util.UUID.randomUUID
import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}

class IntegrationTypeConnectorSpec extends AnyFunSpec with BeforeAndAfterAll {

  implicit var ec: ExecutionContext = ExecutionContext.global
  var integrationTypesConnector: IntegrationTypeConnector = _
  val readside: ReadsideDatabase = new ReadsideDatabase("agency")
  val agencyId = randomUUID()
  val id = randomUUID()
  val userId = randomUUID()

  override def beforeAll(): Unit = {
    val future = Future(readside.migrate())
    future map { result =>
      assertResult(Done)(result)
    } recover {
      case timeout: java.util.concurrent.TimeoutException => None
    }
    Await.result(future, 5000.millis)
    integrationTypesConnector = new IntegrationTypeConnector(readside)
  }

  it("should find all inserted PropertyInspect Types") {
    val findFuture = integrationTypesConnector.getAll

    val findResult = Await.result(findFuture, 5.seconds)

    val r = findResult
    assertResult(5)(r.size)
    r.find(_.`type` == IntegrationType.PropertyInspect.toString)
      .foreach { i =>
        assertResult("Property Inspect")(i.name)
        assertResult(true)(i.available)
        assertResult(true)(i.logo.isDefined)
        assertResult(true)(i.short.isDefined)
        assertResult(true)(i.status.isDefined)
        assertResult(true)(i.orderNo.isDefined)
      }
  }
}
