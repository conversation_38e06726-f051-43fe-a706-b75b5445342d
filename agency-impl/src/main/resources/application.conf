#play.crypto.secret is deprecated, use play.http.secret.key instead
play.http.secret.key = ${?APPLICATION_SECRET}
play.application.loader = com.phoenix.agency.impl.AgencyLoader

agency.cassandra.keyspace = agency
jwt.issuer = agency

sentry-dsn = ${?SENTRY_DSN}

akka {
  actor {
    enable-additional-serialization-bindings = on

    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      jackson-cbor = "akka.serialization.jackson.JacksonCborSerializer"
    }

    serialization-bindings {
      "com.phoenix.agency.impl.AgencyCommand" = jackson-json
      "com.phoenix.agency.impl.commands.AgencyGroupCommand" = jackson-json
      "com.phoenix.agency.impl.AgencyEvent" = jackson-json
      "com.phoenix.agency.impl.events.AgencyGroupEvent" = jackson-json
      "com.phoenix.agency.impl.AgencyState" = jackson-json
      "com.phoenix.agency.impl.AgencyGroupState" = jackson-json
    }
  }

  serialization.jackson {
    jackson-modules += "com.fasterxml.jackson.datatype.joda.JodaModule"
  }
}

cassandra-journal.keyspace = ${agency.cassandra.keyspace}
cassandra-snapshot-store.keyspace = ${agency.cassandra.keyspace}
lagom.persistence.read-side.cassandra.keyspace = ${agency.cassandra.keyspace}

cassandra-journal.write-static-column-compat = off
cassandra-query-journal {
  write-static-column-compat = off
  first-time-bucket = "20190101T00:00"
}

# Enable new sharding state store mode by overriding Lagom's default
akka.cluster.sharding.state-store-mode = ddata

# Enable serializers provided in Akka 2.5.8+ to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done"                 = akka-misc
  "akka.actor.Address"        = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

akka.actor.allow-java-serialization=off

readSidePostgres.username=${?PG_PHOENIX_USERNAME}
readSidePostgres.password=${?PG_PHOENIX_PASSWORD}
readSidePostgres.database=${?PG_PHOENIX_DATABASE}
readSidePostgres.port=${?PG_PHOENIX_PORT}
readSidePostgres.host=${?PG_PHOENIX_HOST}
readSidePostgres.maximumMessageSize=${?PG_PHOENIX_MAXMSG}

propertyInspect.clientId=${?PROPERTY_INSPECT_CLIENT_ID}
propertyInspect.secret=${?PROPERTY_INSPECT_CLIENT_SECRET}
propertyInspect.url = ${?PROPERTY_INSPECT_URL}
propertyInspect.oauthUrl = ${?PROPERTY_INSPECT_OAUTH_URL}
propData.url = ${?PROP_DATA_URL}
propDataOAuth.url = ${?PROP_DATA_OAUTH_URL}
propData.username = ${?PROP_DATA_USERNAME}
propData.password = ${?PROP_DATA_PASSWORD}
