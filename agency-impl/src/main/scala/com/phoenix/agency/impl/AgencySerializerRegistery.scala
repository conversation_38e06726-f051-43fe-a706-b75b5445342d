package com.phoenix.agency.impl

import com.lightbend.lagom.scaladsl.playjson.{JsonSerializer, JsonSerializerRegistry}
import com.phoenix.agency.impl.commands.{AgencyGroupCommand, AgencyGroupReply}
import com.phoenix.agency.impl.events.AgencyGroupEvent
import com.phoenix.party.api.Company_v1
import com.phoenix.util.JsonSerializerImpl.generateJsonSerializersFor

object AgencySerializerRegistery extends JsonSerializerRegistry {
  private def apis = {
    import com.phoenix.agency.api
    Seq(
      JsonSerializer[api.AgencyEvent],
      JsonSerializer[api.AgencyDetails],
      JsonSerializer[api.AgencyAddress],
      JsonSerializer[api.AgencyBankDetails],
      JsonSerializer[api.AgencyContactDetails],
      JsonSerializer[api.Agency_v1],
      JsonSerializer[api.AgencyCreated_v1],
      JsonSerializer[api.AgencyDeactivated_v1],
      JsonSerializer[api.AgencyActivated_v1],
      JsonSerializer[api.MemberInvitedToAgency],
      JsonSerializer[api.AgencyUpdated_v1],
      JsonSerializer[api.AgencyLogoUpdated],
      JsonSerializer[api.AgencyThemeUpdated],
      JsonSerializer[api.InvitationStatus.InvitationStatus],
      JsonSerializer[api.MemberRole.MemberRole],
      JsonSerializer[api.Member],
      JsonSerializer[api.Invitation],
      JsonSerializer[api.InvitationOTPRequest],
      JsonSerializer[api.InvitationRequest],
      JsonSerializer[api.AgencyInvitationToken],
      JsonSerializer[api.IToken],
      JsonSerializer[api.Segment],
      JsonSerializer[api.SegmentDeleted],
      JsonSerializer[api.AgencyGroupEvent],
      JsonSerializer[api.AgencyGroupCreated],
      JsonSerializer[api.AgencyGroupUpdated],
      JsonSerializer[api.AgencyAddedToGroup],
      JsonSerializer[api.GroupMember],
      JsonSerializer[api.response.AgencyGroupResponse],
      JsonSerializer[api.response.AgencyGroupResponse.AgencyGroupMember]
    )
  }

  private def state = {
    import com.phoenix.persistence._
    Seq(
      JsonSerializer[StateRecord],
      JsonSerializer[CreatedState],
      JsonSerializer[DeletedState],
      JsonSerializer[EventRecord],
      JsonSerializer[AgencyState],
      JsonSerializer[DeletableSegmentNotFound],
      JsonSerializer[SegmentNotFound],
      JsonSerializer[IntegrationNotFound],
      JsonSerializer[IntegrationAlreadyExists],
      JsonSerializer[AgencyGroupState]
    )
  }

  private def requests = {
    import com.phoenix.agency.api.request._
    import com.phoenix.agency.api.CreateSegment
    Seq(
      JsonSerializer[Theme],
      JsonSerializer[ThemeRgba],
      JsonSerializer[Logo],
      JsonSerializer[CreateSegment],
      JsonSerializer[Company_v1],
      JsonSerializer[ActivationRequest],
      JsonSerializer[DeactivationRequest],
      JsonSerializer[IntegrationRequest]
    )
  }

  private def responses = {
    import com.phoenix.agency.api.response._
    Seq(
      JsonSerializer[Agency],
      JsonSerializer[IntegrationResponse],
      JsonSerializer[IntegrationTypeRow],
    )
  }

  private def events = generateJsonSerializersFor[AgencyEvent] ++ generateJsonSerializersFor[AgencyGroupEvent]

  private def commands = {
    generateJsonSerializersFor[AgencyCommand] ++ generateJsonSerializersFor[AgencyGroupCommand] ++ Seq(
      JsonSerializer[InvitationsReply],
      JsonSerializer[MembersReply],
      JsonSerializer[SegmentsReply],
      JsonSerializer[AgencyGroupReply],
      JsonSerializer[IntegrationReply]
    )
  }

  override def serializers: Seq[JsonSerializer[_]] = apis ++ state ++ requests ++ responses ++ events ++ commands
}
