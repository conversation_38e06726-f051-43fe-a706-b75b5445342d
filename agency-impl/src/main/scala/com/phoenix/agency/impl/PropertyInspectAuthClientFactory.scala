package com.phoenix.agency.impl

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.client.{LagomClientFactory, StaticServiceLocatorComponents}
import play.api.libs.ws.ahc.AhcWSComponents

import java.net.URI

class PropertyInspectAuthClientFactory(val actorSystem: ActorSystem, val mat: Materializer)
  extends LagomClientFactory("property-inspect-oauth")
    with StaticServiceLocatorComponents
    with AhcWSComponents {

  override def materializer: Materializer = mat
  override def staticServiceUri: URI = URI.create(config.getString("propertyInspect.oauthUrl"))
}

