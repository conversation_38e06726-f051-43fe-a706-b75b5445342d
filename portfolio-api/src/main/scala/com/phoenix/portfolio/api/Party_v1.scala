package com.phoenix.portfolio.api

import play.api.libs.json.{Format, Json}

case class Party_v1(personId: Option[String] = None, companyId: Option[String] = None)

object Party_v1 {
  implicit val format: Format[Party_v1] = Json.format
}

object PersonParty {
  def unapply(arg: Party_v1): Option[String] = arg match {
    case Party_v1(Some(personId), None) => Some(personId)
    case _ => None
  }
}

object CompanyParty {
  def unapply(arg: Party_v1): Option[String] = arg match {
    case Party_v1(None, Some(companyId)) => Some(companyId)
    case _ => None
  }
}

object EmptyParty {
  def unapply(arg: Party_v1): Option[Party_v1] = arg match {
    case Party_v1(personId, companyId) if personId.isEmpty && companyId.isEmpty => Some(arg)
    case _ => None
  }
}
