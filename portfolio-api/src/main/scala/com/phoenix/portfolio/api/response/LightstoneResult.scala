package com.phoenix.portfolio.api.response

import com.phoenix.google.api.Address.Component_v1
import play.api.libs.json.{Format, Json}

case class LightstoneResult(
                             lightstoneId: String,
                             addressComponents: List[Component_v1],
                           )

object LightstoneResult {
  implicit val format: Format[LightstoneResult] = Json.format
}