package com.phoenix.portfolio.api

import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.broker.kafka.{KafkaProperties, PartitionKeyStrategy}
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceCall}
import com.phoenix.lightstone.api.Request.PropertyPostRequest
import com.phoenix.portfolio.api.property.request.{PropertyCreation, PropertyList, PropertyUpdate}
import com.phoenix.portfolio.api.property.response._
import com.phoenix.portfolio.api.property.{PropertyEvent, Property_v1}
import com.phoenix.portfolio.api.request._
import com.phoenix.portfolio.api.response._
import com.phoenix.projections.api.ProjectionEndpoints
import com.phoenix.util.UUID

object PortfolioService  {
  val PORTFOLIO_TOPIC = "portfolio-PortfolioEvents"
  val PROPERTY_TOPIC = "portfolio-PropertyEvents"
  val APPLICATION_FEE_TOPIC = "ApplicationFeeEvents"
}
// scalastyle:off
trait PortfolioService extends Service with ProjectionEndpoints {
  def addLeaseTerms(id: String): ServiceCall[LeaseTermsFields_v1, LeaseTermsFields_v1]
  def addLeaseTermsV2(id: String): ServiceCall[LeaseTermsFields_v1, Portfolio_v1]
  def amendCommission(id: String): ServiceCall[CommissionSplit_v1, Portfolio_v1]
  def amendCommissionV2(id: String): ServiceCall[CommissionSplit_v1, Portfolio_v1]
  def amendContract(id: String): ServiceCall[ContractContainer, Portfolio_v1]
  def amendContractV2(id: String): ServiceCall[ContractContainer, Portfolio_v1]
  def amendProperty(id: String): ServiceCall[PortfolioPropertyFields, Done]
  def amendPropertyV2(id: String): ServiceCall[PortfolioPropertyFields, Portfolio_v1]
  def amendSettings(id: String): ServiceCall[SettingsFields, Done]
  def amendSettingsV2(id: String): ServiceCall[SettingsFields, Portfolio_v1]
  def amendMetaData(id: String): ServiceCall[PortfolioMetaData, Portfolio_v1]
  def amendSegments(id: String): ServiceCall[SegmentFields, Done]
  def amendSegmentsV2(id: String): ServiceCall[SegmentFields, Portfolio_v1]
  def createInvoiceTemplate(id: String): ServiceCall[InvoiceTemplateFields, InvoiceTemplateId]
  def createInvoiceTemplateV2(id: String): ServiceCall[InvoiceTemplateFields, Portfolio_v1]
  def getPortfolio(id: String): ServiceCall[NotUsed, Portfolio_v1]
  def openPortfolio: ServiceCall[OpenPortfolioFields, PortfolioId]
  def openPortfolioV2: ServiceCall[OpenPortfolioFields, Portfolio_v1]
  def removeInvoiceTemplate(id: String, invoice_id: String): ServiceCall[NotUsed, Done]
  def removeInvoiceTemplateV2(id: String, invoice_id: String): ServiceCall[NotUsed, Portfolio_v1]
  def getPortfolios: ServiceCall[NotUsed, Seq[PropertyPortfoliosRow]]
  def getPortfoliosByAgency(agencyId: String): ServiceCall[NotUsed, Seq[Portfolio_v1]]
  def leaseStatus: ServiceCall[Seq[UUID], Seq[PortfoliosStatusRow]]
  def portfolioSummaries: ServiceCall[NotUsed, Seq[PortfolioSummaryRow]]
  def portfolioSummariesV2: ServiceCall[NotUsed, Seq[PortfolioWithApplications]]
  def bulkInvoicesCsvTemplate: ServiceCall[NotUsed, Base64CsvResponse]
  def updateInvoiceTemplate(id: String, invoice_id: String): ServiceCall[InvoiceTemplateFields, Done]
  def updateInvoiceTemplateV2(id: String, invoice_id: String): ServiceCall[InvoiceTemplateFields, Portfolio_v1]
  def invoiceSchedules(id: String): ServiceCall[NotUsed, InvoiceSchedules]
  def addAgent(id: String): ServiceCall[AgentFields, Done]
  def removeAgent(id: String, person_id: String): ServiceCall[NotUsed, Done]
  def requestApproval(id: String): ServiceCall[ApprovalFields, Done]
  def approve(id: String): ServiceCall[ApprovalFields, Done]
  def approveV2(id: String): ServiceCall[ApprovalFields, Portfolio_v1]
  def decline(id: String): ServiceCall[DeclineFields, Done]
  def renew(id: String): ServiceCall[Renewal, Done]
  def renewV2(id: String): ServiceCall[Renewal, Portfolio_v1]
  def deleteRenew(id: String): ServiceCall[NotUsed, Done]
  def deleteRenewV2(id: String): ServiceCall[NotUsed, Portfolio_v1]
  def terminationReasons(): ServiceCall[NotUsed, Seq[TerminationFields.TerminationReasonSummary]]
  def terminateLease(id: String): ServiceCall[TerminationFields, Done]
  def terminateLeaseV2(id: String): ServiceCall[TerminationFields, Portfolio_v1]
  def deleteLease(id: String): ServiceCall[NotUsed, Done]
  def deleteLeaseV2(id: String): ServiceCall[NotUsed, Portfolio_v1]
  def getStats(): ServiceCall[NotUsed, PortfoliosStats]
  def updateOwnerParties(id: String): ServiceCall[Owners, Portfolio_v1]
  def updateTenantParties(id: String): ServiceCall[Tenants, Portfolio_v1]
  def portfoliosWithStatusByAgency(agencyId: String): ServiceCall[NotUsed, Seq[LeaseStatusRow]]
  def updateVATForAgency(agencyId: String): ServiceCall[VatUpdateFields, PortfolioList]
  def updateVatAllAgencies(): ServiceCall[VatUpdateFields, Seq[PortfolioList]]
  def revertVATForAgency(agencyId: String): ServiceCall[NotUsed, Done]
  def portfolioEvents: Topic[PortfolioEvent]

  // Properties API calls
  def createProperty: ServiceCall[PropertyCreation, Property_v1]
  def updateProperty(id: String): ServiceCall[PropertyUpdate, Property_v1]
  def deleteProperty(id: String): ServiceCall[NotUsed, PropertyDeleted]
  def getProperty(id: String): ServiceCall[NotUsed, Property_v1]
  def getGooglePlace(placeId: String): ServiceCall[NotUsed, GooglePlacesResult]

  def getLightstoneProperty(lightstoneId: String): ServiceCall[NotUsed, LightstoneResult]
  def addPropertyLandlord(id: String): ServiceCall[LandlordFields, LandlordId]
  def combinedSearchProperties(query: String): ServiceCall[NotUsed, CombinedPropertySearchResult]
  def searchProperties(query: String): ServiceCall[NotUsed, PropertySearchResult]
  def advancedSearchProperties(): ServiceCall[PropertyPostRequest, PropertySearchResult]
  def getPropertyPortfolios(id: String): ServiceCall[NotUsed, Seq[Portfolio_v1]]
  def getProperties: ServiceCall[NotUsed, Seq[PropertyResultRow]]
  def getPropertiesV2(agencyId: String): ServiceCall[NotUsed, Seq[PropertyResultRowWithIDs]]

  //Internal GO API call
  def getPropertiesFromListInternal: ServiceCall[PropertyList, Seq[Property_v1]]
  def getPortfolioInternal(id: String): ServiceCall[NotUsed, OptionalPortfolioResponse]
  def getPortfoliosByAgencyInternal(agencyId: String): ServiceCall[NotUsed, Seq[Portfolio_v1]]

  // Applications API calls
  def addApplication(portfolioId: String): ServiceCall[AddApplicationFields, ApplicationFeeResponse]
  def updateApplication(portfolioId: String, applicationId: String): ServiceCall[AddApplicationFields, ApplicationFeeResponse]
  def removeApplication(portfolioId: String, applicationId: String): ServiceCall[NotUsed, ApplicationFeeResponse]
  def getApplications(portfolioId: String): ServiceCall[NotUsed, ApplicationFeeResponse]
  def acceptApplication(portfolioId: String, applicationId: String): ServiceCall[NotUsed, Portfolio_v1]
  def getApplicationsByAgency(agencyId: String): ServiceCall[NotUsed, ApplicationResponse]
  def getApplicationsByAgencies(): ServiceCall[AgenciesList, ApplicationByAgencyIdResponse]

  // HistoricalEvents
  def getHistoricalCommandsByAgency(agencyId: String): ServiceCall[HistoricalEventRequest, Seq[LatestCommandsResponse]]

  // Topics
  def propertyEvents(): Topic[PropertyEvent]
  def applicationFeeEvents(): Topic[ApplicationFeeEvent]

  // scalastyle:off
  override final def descriptor: Descriptor = {
    import Service._
    named("portfolio")
      .withCalls(
        restCall(Method.POST, "/api/portfolios", openPortfolio),
        restCall(Method.POST, "/api/portfolios/v2", openPortfolioV2),
        restCall(Method.GET, "/api/portfolios/:id/entity", getPortfolio _),
        restCall(Method.POST, "/api/portfolios/:id/property", amendProperty _),
        restCall(Method.POST, "/api/portfolios/v2/:id/property", amendPropertyV2 _),
        restCall(Method.POST, "/api/portfolios/:id/contract", amendContract _),
        restCall(Method.POST, "/api/portfolios/v2/:id/contract", amendContractV2 _),
        restCall(Method.POST, "/api/portfolios/:id/settings", amendSettings _),
        restCall(Method.POST, "/api/portfolios/v2/:id/settings", amendSettingsV2 _),
        restCall(Method.POST, "/api/portfolios/:id/metaData", amendMetaData _),
        restCall(Method.POST, "/api/portfolios/:id/segments", amendSegments _),
        restCall(Method.POST, "/api/portfolios/v2/:id/segments", amendSegmentsV2 _),
        restCall(Method.POST, "/api/portfolios/:id/commission", amendCommission _),
        restCall(Method.POST, "/api/portfolios/v2/:id/commission", amendCommissionV2 _),
        restCall(Method.POST, "/api/portfolios/:id/lease_terms", addLeaseTerms _ ),
        restCall(Method.POST, "/api/portfolios/v2/:id/lease_terms", addLeaseTermsV2 _ ),
        /**
         * @deprecated no longer used
         */
        restCall(Method.POST, "/api/portfolios/:id/agents", addAgent _ ),
        /**
         * @deprecated no longer used
         */
        restCall(Method.DELETE, "/api/portfolios/:id/agents/:person_id", removeAgent _),
        restCall(Method.GET, "/api/portfolios", getPortfolios),
        restCall(Method.GET, "/api/portfolios/v2/summaries", portfolioSummariesV2 _),
        restCall(Method.GET, "/api/portfolios/v2/:agencyId", getPortfoliosByAgency _),
        restCall(Method.GET, "/api/portfolios/summaries", portfolioSummaries _),
        restCall(Method.GET, "/api/portfolios/bulk_invoices_csv_template", bulkInvoicesCsvTemplate),
        restCall(Method.POST, "/api/portfolios/get_status", leaseStatus),
        restCall(Method.GET, "/api/portfolios/status/agency/:agencyId", portfoliosWithStatusByAgency _),
        restCall(Method.POST, "/api/portfolios/update_vat/agency/:agencyId", updateVATForAgency _),
        restCall(Method.POST, "/api/portfolios/update_vat_all_agencies", updateVatAllAgencies _),
        restCall(Method.POST, "/api/portfolios/revert_vat_all_leases/:agencyId", revertVATForAgency _),

        // Applications
        restCall(Method.POST, "/api/portfolios/:id/applications", addApplication _),
        restCall(Method.PUT, "/api/portfolios/:id/applications/:application_id", updateApplication _),
        restCall(Method.DELETE, "/api/portfolios/:id/applications/:application_id", removeApplication _),
        restCall(Method.GET, "/api/portfolios/:id/applications", getApplications _),
        restCall(Method.POST, "/api/portfolios/:id/applications/:application_id/accept", acceptApplication _),
        restCall(Method.GET, "/api/portfolios/applications/:agencyId", getApplicationsByAgency _),
        restCall(Method.POST, "/api/portfolios/applications", getApplicationsByAgencies _),
        // Portfolio Parties
        restCall(Method.POST, "/api/portfolios/:id/owner_parties", updateOwnerParties _),
        restCall(Method.POST, "/api/portfolios/:id/tenant_parties", updateTenantParties _),
        restCall(Method.POST, "/api/portfolios/:id/invoice_templates", createInvoiceTemplate _),
        restCall(Method.POST, "/api/portfolios/v2/:id/invoice_templates", createInvoiceTemplateV2 _),
        restCall(Method.PUT, "/api/portfolios/:id/invoice_templates/:invoice_id", updateInvoiceTemplate _),
        restCall(Method.PUT, "/api/portfolios/v2/:id/invoice_templates/:invoice_id", updateInvoiceTemplateV2 _),
        restCall(Method.DELETE, "/api/portfolios/:id/invoice_templates/:invoice_id", removeInvoiceTemplate _),
        restCall(Method.DELETE, "/api/portfolios/v2/:id/invoice_templates/:invoice_id", removeInvoiceTemplateV2 _),
        // Portfolio Queries
        restCall(Method.GET, "/api/portfolios/:id/invoice_schedules", invoiceSchedules _),
        // Portfolio Actions
        restCall(Method.POST, "/api/portfolios/:id/request_approval", requestApproval _),
        restCall(Method.POST, "/api/portfolios/:id/approve", approve _),
        restCall(Method.POST, "/api/portfolios/v2/:id/approve", approveV2 _),
        restCall(Method.POST, "/api/portfolios/:id/decline", decline _),
        restCall(Method.POST, "/api/portfolios/:id/renew", renew _),
        restCall(Method.POST, "/api/portfolios/v2/:id/renew", renewV2 _),
        restCall(Method.DELETE, "/api/portfolios/:id/renew", deleteRenew _),
        restCall(Method.DELETE, "/api/portfolios/v2/:id/renew", deleteRenewV2 _),
        restCall(Method.GET, "/api/portfolios/termination_reasons", terminationReasons _),
        restCall(Method.POST, "/api/portfolios/:id/terminate", terminateLease _),
        restCall(Method.POST, "/api/portfolios/v2/:id/terminate", terminateLeaseV2 _),
        restCall(Method.DELETE, "/api/portfolios/:id", deleteLease _),
        restCall(Method.DELETE, "/api/portfolios/v2/:id", deleteLeaseV2 _),
        restCall(Method.GET, "/api/portfolios/stats", getStats _),
        // Properties
        restCall(Method.POST, "/api/properties", createProperty),
        restCall(Method.PUT, "/api/properties/:id", updateProperty _),
        restCall(Method.GET, "/api/properties/:id/entity", getProperty _),
        restCall(Method.GET, "/api/google_places/:place_id/entity", getGooglePlace _),
        restCall(Method.GET, "/api/properties/lightstone/:lightstone_id/entity", getLightstoneProperty _),
        restCall(Method.DELETE, "/api/properties/:id/entity", deleteProperty _),
        restCall(Method.GET, "/api/properties/combined_search?query", combinedSearchProperties _),
        restCall(Method.GET, "/api/properties/search?query", searchProperties _),
        restCall(Method.POST, "/api/properties/search", advancedSearchProperties _),
        restCall(Method.POST, "/api/properties/:id/landlord", addPropertyLandlord _),
        restCall(Method.GET, "/api/properties/:id/portfolios", getPropertyPortfolios _),
        restCall(Method.GET, "/api/properties", getProperties _),
        restCall(Method.GET, "/api/properties/v2/:agencyId", getPropertiesV2 _),


        // Go Internal Endpoints
        restCall(Method.POST, "/api-go/properties/", getPropertiesFromListInternal _),
        restCall(Method.GET, "/api-go/portfolio/:id/entity", getPortfolioInternal _),
        restCall(Method.GET, "/api-go/portfolios/:agencyId", getPortfoliosByAgencyInternal _),

        // HistoricalEvents
        restCall(Method.POST, "/api/portfolios/historical_events/:agencyId", getHistoricalCommandsByAgency _)
      )
      .addCalls(projectionRestCalls("portfolios"): _*)
      .withTopics(
        topic(PortfolioService.PORTFOLIO_TOPIC, portfolioEvents).addProperty(
          KafkaProperties.partitionKeyStrategy,
          PartitionKeyStrategy[PortfolioEvent](_.portfolioId.toString)
        ),
        topic(PortfolioService.PROPERTY_TOPIC, propertyEvents()).addProperty(
          KafkaProperties.partitionKeyStrategy,
          PartitionKeyStrategy[PropertyEvent](_.id.toString)
        ),
        topic(PortfolioService.APPLICATION_FEE_TOPIC, applicationFeeEvents()).addProperty(
          KafkaProperties.partitionKeyStrategy,
          PartitionKeyStrategy[ApplicationFeeEvent](_.id.uuid.toString)
        ),
      )
      .withAutoAcl(true)
  }
  // scalastyle:on
}
