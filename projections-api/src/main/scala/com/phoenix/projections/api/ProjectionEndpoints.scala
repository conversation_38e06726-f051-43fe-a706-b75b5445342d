package com.phoenix.projections.api

import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.Service.restCall
import com.lightbend.lagom.scaladsl.api.{Descriptor, ServiceCall}
import com.lightbend.lagom.scaladsl.api.transport.Method

trait ProjectionEndpoints {
  // Projections
  def getProjections: ServiceCall[NotUsed, State]
  def startProjection(name: String): ServiceCall[NotUsed, Done]
  def stopProjection(name: String): ServiceCall[NotUsed, Done]
  def startProjectionWorker(name: String, worker: String): ServiceCall[NotUsed, Done]
  def stopProjectionWorker(name: String, worker: String): ServiceCall[NotUsed, Done]

  protected def projectionRestCalls(service: String): Seq[Descriptor.Call[_, _]] = Seq(
    // Projections
    restCall(Method.GET, s"/api/$service/projections", getProjections),
    restCall(Method.GET, s"/api/$service/projections/:name/start", startProjection _),
    restCall(Method.GET, s"/api/$service/projections/:name/stop", stopProjection _),
    restCall(Method.GET, s"/api/$service/projections/:name/worker/:worker/start", startProjectionWorker _),
    restCall(Method.GET, s"/api/$service/projections/:name/worker/:worker/start", stopProjectionWorker _)
  )
}
