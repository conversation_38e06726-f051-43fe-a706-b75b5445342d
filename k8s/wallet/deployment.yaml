---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wallet
  labels:
    app: wallet
  namespace: phoenix
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: wallet
  template:
    metadata:
      labels:
        app: wallet
    spec:
      restartPolicy: Always
      containers:
        - readinessProbe:
            httpGet:
              path: /ready
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
          livenessProbe:
            httpGet:
              path: /alive
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
          name: wallet
          image: eu.gcr.io/rental-connect-kube/wallet-impl
          ports:
            - containerPort: 9000
              name: http
            - containerPort: 2552
              name: remoting
            - containerPort: 8558
              name: management
          imagePullPolicy: Always
          volumeMounts: []
          resources:
            limits:
              cpu: 1000m
              memory: 2G
            requests:
              cpu: 250m
              memory: 1800M
          env:
            - name: JAVA_OPTS
              value: "-Xms1024m -Xmx1024m -Dconfig.resource=prod-application.conf"
            - name: APP_URL
              value: "https://app.reos.co.za"
            - name: APPLICATION_SECRET
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "application_secret"
            - name: SENDGRID_TOKEN
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "sendgrid_token"
            - name: CLICKATELL_TOKEN
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "clickatell_token"
            - name: EASYPAY_PROTOCOL
              valueFrom:
                configMapKeyRef:
                  name: "phoenix-env"
                  key: "EASYPAY_PROTOCOL"
            - name: EASYPAY_HOST
              valueFrom:
                configMapKeyRef:
                  name: "phoenix-env"
                  key: "EASYPAY_HOST"
            - name: EASYPAY_PORT
              valueFrom:
                configMapKeyRef:
                  name: "phoenix-env"
                  key: "EASYPAY_PORT"
            - name: EASYPAY_TOKEN
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "easypay_token"
            - name: CARD_ACCEPTOR
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "easypay_card_acceptor"
            - name: EASYPAY_TERMINAL
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "easypay_terminal"
            - name: IROHA_ADMIN_PUB
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "iroha_admin_pub"
            - name: IROHA_ADMIN_PRI
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "iroha_admin_pri"
            - name: SENTRY_DSN
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "sentry_dsn"
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: "jwt-secret"
                  key: "jwt_secret"
            - name: PG_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "postgres-secret"
                  key: "pg_password"
            - name: PG_PHOENIX_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "phoenix-postgres-secret"
                  key: "pg_password"
            - name: AKKA_CLUSTER_BOOTSTRAP_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: "metadata.labels['app']"
            - name: REQUIRED_CONTACT_POINT_NR
              value: "1"
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: "metadata.namespace"
          envFrom:
            - configMapRef:
                name: "phoenix-env"
      priorityClassName: medium-priority
      volumes: []
