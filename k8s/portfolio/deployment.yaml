---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: portfolio
  labels:
    app: portfolio
  namespace: phoenix
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: portfolio
  template:
    metadata:
      labels:
        app: portfolio
    spec:
      restartPolicy: Always
      containers:
        - readinessProbe:
            httpGet:
              path: /ready
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
          livenessProbe:
            httpGet:
              path: /alive
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
          name: portfolio
          image: eu.gcr.io/rental-connect-kube/portfolio-impl
          ports:
            - containerPort: 9000
              name: http
            - containerPort: 2552
              name: remoting
            - containerPort: 8558
              name: management
          imagePullPolicy: Always
          volumeMounts: []
          resources:
            limits:
              cpu: 1000m
              memory: 1400M
            requests:
              cpu: 50m
              memory: 1000M
          env:
            - name: JAVA_OPTS
              value: "-Xms512m -Xmx512m -Dconfig.resource=prod-application.conf"
            - name: APP_URL
              value: "https://app.reos.co.za"
            - name: APPLICATION_SECRET
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "application_secret"
            - name: SENDGRID_TOKEN
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "sendgrid_token"
            - name: CLICKATELL_TOKEN
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "clickatell_token"
            - name: SENTRY_DSN
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "sentry_dsn"
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: "jwt-secret"
                  key: "jwt_secret"
            - name: PG_PHOENIX_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "phoenix-postgres-secret"
                  key: "pg_password"
            - name: AKKA_CLUSTER_BOOTSTRAP_SERVICE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: "metadata.labels['app']"
            - name: REQUIRED_CONTACT_POINT_NR
              value: "1"
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: "metadata.namespace"
            - name: LIGHTSTONE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: "phoenix-secrets"
                  key: "lightstone_api_key"
          envFrom:
            - configMapRef:
                name: "phoenix-env"
      priorityClassName: medium-priority
      volumes: []
