#play.crypto.secret is deprecated, use play.http.secret.key instead
play.http.secret.key = ${?APPLICATION_SECRET}
play.application.loader = com.phoenix.recon.impl.ReconLoader

recon.cassandra.keyspace = recon
jwt.issuer = recon

sentry-dsn = ${?SENTRY_DSN}

akka {
  actor {
    enable-additional-serialization-bindings = on

    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      jackson-cbor = "akka.serialization.jackson.JacksonCborSerializer"
    }

    serialization-bindings {
      "com.phoenix.recon.impl.commands.ReconCommand" = jackson-json
      "com.phoenix.recon.impl.events.ReconEvent" = jackson-json
      "com.phoenix.recon.impl.ReconState" = jackson-json
    }
  }

  serialization.jackson {
    jackson-modules += "com.fasterxml.jackson.datatype.joda.JodaModule"
  }
}

overdueInvoiceSchedulerDelay=4h

cassandra-journal.keyspace = ${recon.cassandra.keyspace}
cassandra-snapshot-store.keyspace = ${recon.cassandra.keyspace}
lagom.persistence.read-side.cassandra.keyspace = ${recon.cassandra.keyspace}

lagom.persistence.read-side.global-prepare-timeout = 120s
lagom.broker.kafka.client.consumer.batching-size = 1

cassandra-journal.write-static-column-compat = off
cassandra-query-journal {
  write-static-column-compat = off
  first-time-bucket = "20190101T00:00"
}

# Enable new sharding state store mode by overriding Lagom's default
akka.cluster.sharding.state-store-mode = ddata

# Enable serializers provided in Akka 2.5.8+ to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done"                 = akka-misc
  "akka.actor.Address"        = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

lagom.circuit-breaker {
  default {
    exception-whitelist = [
      "com.lightbend.lagom.scaladsl.api.transport.NotFound",
      "com.lightbend.lagom.scaladsl.api.transport.BadRequest"
    ]
  }
}

easypay.protocol =${?EASYPAY_PROTOCOL}
easypay.host = ${?EASYPAY_HOST}
easypay.port = ${?EASYPAY_PORT}
easypay.token = ${?EASYPAY_TOKEN}
easypay.card_acceptor = ${?CARD_ACCEPTOR}
easypay.terminal = ${?EASYPAY_TERMINAL}

com.lightbend.platform-tooling.service-discovery.external-service-addresses {
  "easypay" = ["http://test.easypay.rentalconnect.co.za/"]
 }

phoenixPG.username=${?PG_PHOENIX_USERNAME}
phoenixPG.password=${?PG_PHOENIX_PASSWORD}
phoenixPG.database=${?PG_PHOENIX_DATABASE}
phoenixPG.port=${?PG_PHOENIX_PORT}
phoenixPG.host=${?PG_PHOENIX_HOST}
phoenixPG.maximumMessageSize=${?PG_PHOENIX_MAXMSG}
