package com.phoenix.recon.impl.connectors

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.phoenix.party.api.AccountType
import com.phoenix.util.UUID
import io.getquill.{CassandraLagomAsyncContext, SnakeCase}
import com.phoenix.persistence.{AutoBind, QuillHelper}
import play.api.libs.json.{Format, Json}

import scala.concurrent.{ExecutionContext, Future}

class PartyRepository(session: CassandraSession)(implicit ec: ExecutionContext) {

  val ctx = new CassandraLagomAsyncContext(SnakeCase, session)
  import ctx._
  import QuillHelper.Implicits._
  implicit val encodeAccountType = MappedEncoding[AccountType, String](_.getObjectName)
  implicit val decodeAccountType = MappedEncoding[String, AccountType](AccountType(_))

  private val partyAccounts = quote {
    querySchema[PartyAccount]("party_accounts")
  }

  private val partyAccountsNoFunds = quote {
    querySchema[PartyAccountNoFunds]("party_accounts")
  }

  private val customers = quote {
    querySchema[CustomerRow]("customers")
  }

  val customerInvoices = quote { querySchema[CustomerInvoice]("customers_invoices") }

  def insertCustomer(id: UUID, name: String): Future[Done] = {
    val q = quote {
      customers.insert(_.id -> lift(id), _.name -> lift(name))
    }
    ctx.run(q)
  }

  def insertPartyAccount(partyAccount: PartyAccountNoFunds): Future[Done] = {
    val q = quote { partyAccountsNoFunds.insert(lift(partyAccount)) }
    ctx.run(q)
  }

  def insertCustomerInvoice(invoiceId: UUID, customerId: UUID): Future[Done] = {
    val q = quote {
      customerInvoices.insert(_.invoiceId -> lift(invoiceId), _.customerId -> lift(customerId))
    }
    ctx.run(q)
  }

  def getCustomerIdByInvoiceId(invoiceId: UUID): Future[Option[UUID]] = {
    val q = quote { customerInvoices.filter(_.invoiceId == lift(invoiceId)) }
    ctx.run(q).map(_.lastOption.map(_.customerId))
  }

  def getCustomerNameByInvoiceId(invoiceId: UUID): Future[Option[String]] = {
    getCustomerIdByInvoiceId(invoiceId).flatMap {
      case Some(customerId) => getCustomerNameById(customerId)
      case _ => Future(Option.empty[String])
    }
  }

  def getCustomerNameById(customerId: UUID): Future[Option[String]] = {
    val q = quote { customers.filter(_.id == lift(customerId))}
    ctx.run(q).map(_.lastOption.map(_.name))
  }

  def updateCustomer(id: UUID, name: String): Future[Done] = {
    val q = quote { customers.filter(_.id == lift(id)).update(_.name -> lift(name)) }
    ctx.run(q)
  }

  def setAvailableFunds(agencyId: UUID, partyId: UUID, accountId: UUID, availableFunds: Option[BigDecimal]): Future[Option[PartyAccount]] = {
    val q = quote { partyAccounts.filter(pa => pa.agencyId == lift(agencyId) &&
      pa.partyId == lift(partyId) &&
      pa.accountId == lift(accountId)).update(_.availableFunds -> lift(availableFunds)) }
    findPartyAccountId(agencyId, partyId, accountId).flatMap { acc =>
      ctx.run(q).map { _ =>
        // Copy the funds value because in the best case the row will be updated eventually
        acc.map(_.copy(availableFunds = availableFunds))
      }
    }
  }

  def updateAddress(agencyId: UUID, partyId: UUID, accountId: UUID, address: Option[String]): Future[Done] = {
    val q = quote { partyAccounts.filter(pa => pa.agencyId == lift(agencyId) &&
                    pa.partyId == lift(partyId) &&
                    pa.accountId == lift(accountId)).update(_.address -> lift(address)) }
    ctx.run(q)
  }

  def findPartyAccountTag(agencyId: UUID, partyId: UUID, tag: AccountType, portfolioId: Option[UUID]): Future[Option[PartyAccount]] = for {
    result <- ctx.run( quote { partyAccounts.filter(pa => pa.agencyId == lift(agencyId) && pa.partyId == lift(partyId))})
  } yield result.filter(pa => pa.tag == tag && pa.portfolioId == portfolioId).headOption

  def findPartyAccountId(agencyId: UUID, partyId: UUID, accountId: UUID): Future[Option[PartyAccount]] = {
    val q = quote { partyAccounts.filter(pa => pa.agencyId == lift(agencyId) && pa.partyId == lift(partyId) && pa.accountId == lift(accountId)) }
    ctx.run(q).map(_.headOption)
  }

  def findAccountIdHack(agencyId: UUID, accountId: UUID): Future[Option[PartyAccount]] = {
    val q = quote {
      partyAccounts.filter(pa => pa.agencyId == lift(agencyId) && pa.accountId == lift(accountId)).allowFiltering
    }
    ctx.run(q).map(_.headOption)
  }

  def getTenantAccounts(agencyId: UUID): Future[Seq[PartyAccount]] = for {
    result <- ctx.run( quote { partyAccounts.filter(pa => pa.agencyId == lift(agencyId)) })
  } yield result.filter(pa => pa.tag == AccountType.Tenant)

}

private case class CustomerRow(id: UUID, name: String)
case class CustomerInvoice(invoiceId: UUID, customerId: UUID)

case class PartyAccountNoFunds(agencyId: UUID, partyId: UUID, paymentReference: String, accountId: UUID, tag: AccountType, partyName: String,
                        portfolioId: Option[UUID], address: Option[String])
object PartyAccountNoFunds {
  implicit val format: Format[PartyAccountNoFunds] = Json.format
}

case class PartyAccount(agencyId: UUID, partyId: UUID, paymentReference: String, accountId: UUID, tag: AccountType, partyName: String,
                        portfolioId: Option[UUID], address: Option[String], availableFunds: Option[BigDecimal])
object PartyAccount {
  implicit val format: Format[PartyAccount] = Json.format
}
