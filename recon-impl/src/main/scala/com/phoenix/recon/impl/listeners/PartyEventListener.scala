package com.phoenix.recon.impl.listeners

import akka.Done
import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode.NotFound
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.InvalidCommandException
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.phoenix.invoice.api.InvoiceType
import com.phoenix.logging.EventLogging
import com.phoenix.party.api._
import com.phoenix.persistence.PersistentEntityRegistrySugar
import com.phoenix.portfolio.api.Applications.ReconStatus
import com.phoenix.recon.api.response.ApplicationFeeInvoiceStatusResponse
import com.phoenix.recon.api.{BeneficiaryCreditNote, CreditNote, CreditNoteReason, Invoice}
import com.phoenix.recon.impl.ReconState
import com.phoenix.recon.impl.commands.{GetInvoice, IssueCreditNote}
import com.phoenix.recon.impl.connectors._
import com.phoenix.recon.impl.entities.ReconEntity
import com.typesafe.scalalogging.LazyLogging

import java.time.LocalDateTime
import scala.concurrent.{ExecutionContext, Future}

class PartyEventListener(override val entityRegistry: PersistentEntityRegistry,
                         partyRepository: PartyRepository,
                         applicationFeeConnector: ApplicationFeeConnector,
                         pgInvoiceConnector: PgInvoiceConnector,
                         partyService: PartyService)
                        (implicit executionContext: ExecutionContext)
  extends PersistentEntityRegistrySugar
    with EventLogging
    with LazyLogging {

  partyService.partyEvents
    .subscribe
    .withGroupId("reconPartyEvent260")
    .atLeastOnce(
      Flow[PartyEvent].mapAsync(1)(logEventAsync)
        .mapAsync(1) {
          case PartyCreated_v1(partyId, taggableParty, _) =>
            taggableParty match {
              case company: Company_v1 =>
                partyRepository.insertCustomer(partyId, company.companyName.getOrElse(""))
              case person: Person_v1 =>
                partyRepository.insertCustomer(partyId, person.firstName.getOrElse("") + " " + person.lastName.getOrElse(""))
              case x => Future.successful(Done)
            }

          case PartyUpdated_v1(partyId, taggableParty, _, _) =>
            taggableParty match {
              case company: Company_v1 =>
                partyRepository.updateCustomer(partyId, company.companyName.get)
              case person: Person_v1 =>
                partyRepository.updateCustomer(partyId, person.firstName.getOrElse("") + " " + person.lastName.getOrElse(""))
              case _ => Future.successful(Done)
            }

          case PartyAccountAdded(id, agencyId, account) => for {
            customer <- partyRepository.getCustomerNameById(id)
            _ <- pgInvoiceConnector.insertPartyAccount(
              PgPartyAccount(agencyId, id, account.accountId, account.propertyAddress, None, customer.getOrElse(""),
                Some(account.paymentReference), account.portfolioId.map(_.uuid), account.tag, LocalDateTime.now(), LocalDateTime.now()))
            reply    <- partyRepository.insertPartyAccount(PartyAccountNoFunds(agencyId, id, account.paymentReference, account.accountId,
              account.tag, customer.getOrElse(""), account.portfolioId, account.propertyAddress))
          } yield reply

          case PartyAccountUpdated(id, agencyId, account) => for {
            _ <- pgInvoiceConnector.updatePartyAddress(agencyId, id, account.accountId, account.propertyAddress)
            _ <- partyRepository.updateAddress(agencyId, id, account.accountId, account.propertyAddress)
          } yield Done

          case PartyAccountMigrated(_, _, fromAccount, toAccount, _, eventRecord) => (fromAccount, toAccount) match {
            // Finds all invoices on the lease the application was migrated and credits notes the balance
            case (fromAcc, toAcc) if fromAcc.tag == AccountType.Application && toAcc.tag == AccountType.Tenant && toAcc.portfolioId.nonEmpty => for {
              applications <- applicationFeeConnector.getApplicationFeeInvoicesByPortfolioId(toAcc.portfolioId.get.uuid)


              invoices <- Future.sequence(applications.filter(isAcceptedApplication).map(getAppFeeInvoices)).map(_.flatten)

              _ <- Future.sequence(invoices
                .filter(_.amountLessCreditNotes > 0)
                .filter(_.customerId == toAccount.partyId) map { invoice =>
                  logger.info("[PartyAccountMigrated] Invoice to credit note: " + invoice.invoiceId)
                  entityRegistry.refFor[ReconEntity](invoice.invoiceId).ask(
                      IssueCreditNote(CreditNote(
                        invoice.amountLessCreditNotes,
                        CreditNoteReason.InvoiceSettled,
                        None,
                        userUnderstandsRisk = true,
                        invoice.beneficiaries.map(b => BeneficiaryCreditNote(b.beneficiary.id.get, 0)),
                        Some("Application accepted")),
                        eventRecord.createdBy.getOrElse("")))
                    .recover({
                      case ex: InvalidCommandException =>
                        logger.error(s"[PartyAccountMigrated] Cannot credit note invoice [${invoice.invoiceId}] $ex")
                        Done
                    })
                })

            } yield Done
            case (_, _) => Future.successful(Done)
          }

          case _ => Future.successful(Done)
        })
    .recover {
      case e =>
        logger.error("Error in party event listener {}", e)
        throw e
    }

  private def getAppFeeInvoices(application: ApplicationFeeInvoiceStatusResponse): Future[Option[Invoice]] = {
    entityRegistry.refFor[ReconEntity](application.invoiceId).ask(GetInvoice).map {
      case ReconState(_, _, Some(i), _) if application.applicationId == i.invoiceId &&
        (i.invoiceType == InvoiceType.ApplicationFee.name || i.invoiceType == InvoiceType.ApplicationFee.getObjectName) =>
        Some(i)
      case _ => None
    }
  }

  private def isAcceptedApplication(a: ApplicationFeeInvoiceStatusResponse): Boolean = {
    Set("InvoiceSent", "PartiallyPaid", "Pending").contains(a.status)
  }
}
