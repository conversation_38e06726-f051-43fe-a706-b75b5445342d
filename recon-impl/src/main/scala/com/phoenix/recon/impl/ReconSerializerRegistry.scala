package com.phoenix.recon.impl

import com.lightbend.lagom.scaladsl.playjson.{JsonSerializer, JsonSerializerRegistry}
import com.phoenix.recon.api.InvoiceStatus
import com.phoenix.util.JsonSerializerImpl._

import scala.collection.immutable.Seq

object ReconSerializerRegistry extends JsonSerializerRegistry {
  private def api = {
    import com.phoenix.recon.api._
    Seq(
      JsonSerializer[UpdateBeneficiariesResult],
      JsonSerializer[InvoiceResponse],
      <PERSON>sonSerializer[Invoice],
      JsonSerializer[InvoiceSummary],
      <PERSON>sonSerializer[InvoiceResult],
      JsonSerializer[ReconBeneficiaryRule],
      <PERSON>sonSerializer[PaymentRequestResult],
      JsonSerializer[Payment],
      JsonSerializer[ApprovedPayment],
      JsonSerializer[RejectedPayment],
      JsonSerializer[CreditNoteResult],
      <PERSON>sonSerializer[BeneficiaryCreditNote],
      JsonSerializer[CreditNoteReason],
      JsonSerializer[CreditNoteReasonSummary],
      JsonSerializer[CreditNote],
      JsonSerializer[AccountActivity],
      JsonSerializer[OpenInvoiceSummary],
      JsonSerializer[Activity]
    )
  }

  private def responses = {
    import com.phoenix.recon.api.response._
    Seq(
      JsonSerializer[CreditNoteReasonSummaries],
      JsonSerializer[BeneficiaryByPartyRow]
    )
  }

  private def apiEvents = {
    import com.phoenix.recon.api.events._
    Seq(
      JsonSerializer[ReconciliationEvent],
      JsonSerializer[PaymentsApproved_v1],
      JsonSerializer[CreditNoteIssued_v1],
      JsonSerializer[CreditNoteIssued_v2]
    )
  }

  private def state = {
    Seq(
      JsonSerializer[ReconState]
    )
  }

  private def domain = {
    import com.phoenix.recon.impl.domain._
    Seq(
      JsonSerializer[Distribution],
      JsonSerializer[Distribution_v2],
      JsonSerializer[ReconStatus],
      JsonSerializer[InvoiceStatus],
      JsonSerializer[ActivityType]
    )
  }

  private def events = {
    import com.phoenix.recon.impl.events._
    generateJsonSerializersFor[ReconEvent]
  }

  private def commands = {
    import com.phoenix.recon.impl.commands._
    Seq(
      //
    ) ++ generateJsonSerializersFor[ReconCommand[_]]
  }

  private def connectors = {
    import com.phoenix.recon.impl.connectors._
    Seq(
      JsonSerializer[InvoiceFromRow],
      JsonSerializer[PartyAccount],
      JsonSerializer[PartyAccountNoFunds],
      JsonSerializer[BulkImportCSVTableRow]
    )
  }

  override def serializers: Seq[JsonSerializer[_]] = api ++ apiEvents ++ responses ++ events ++ commands ++ domain ++ state ++ connectors
}
