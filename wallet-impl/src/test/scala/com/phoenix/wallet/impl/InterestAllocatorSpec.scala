package com.phoenix.wallet.impl

import akka.stream.scaladsl.Sink
import akka.stream.testkit.scaladsl.TestSink
import com.phoenix.banking.api.{InterestEarned, InterestRate, StatementEvent}
import com.phoenix.date.DateUtcUtil
import com.phoenix.util.UUID
import com.phoenix.wallet.api.{DepositInterestCreated, WalletEvent}
import com.phoenix.wallet.impl.connectors.{DepositAsset, DepositDailyBalancesRow}
import com.phoenix.wallet.impl.util.{IrohaDepositInterest, WalletAsset, WalletRef}
import org.joda.time.{DateTime, DateTimeZone, LocalDate}
import org.scalatest.time.{Seconds, Span}
import play.api.libs.json.Json

import scala.concurrent.duration._

class InterestAllocatorSpec extends BaseSpec {
  override protected val listeners: Seq[String] = Seq("agency", "party", "status", "recon", "banking", "interest")

  "Feb deposit with interest transaction" when {
    val agencyId = UUID.createV4
    val deposit1 = UUID.createV4
    val walletref1 = WalletRef.deposit(deposit1, agencyId).toIrohaString
    val janInterest = DepositDailyBalancesRow(new LocalDate(2020, 2, 1), agencyId, deposit1, DepositAsset(WalletAsset.Zar.name, 2, BigDecimal(540015.12)))
    val t1 = DepositDailyBalancesRow(new LocalDate(2020, 2, 6), agencyId, deposit1, DepositAsset(WalletAsset.Zar.name, 2, BigDecimal(539020.12)))
    val t2 = DepositDailyBalancesRow(new LocalDate(2020, 2, 7), agencyId, deposit1, DepositAsset(WalletAsset.Zar.name, 2, BigDecimal(527434.02)))
    val t3 = DepositDailyBalancesRow(new LocalDate(2020, 2, 12), agencyId, deposit1, DepositAsset(WalletAsset.Zar.name, 2, BigDecimal(526279.08)))
    val t4 = DepositDailyBalancesRow(new LocalDate(2020, 2, 14), agencyId, deposit1, DepositAsset(WalletAsset.Zar.name, 2, BigDecimal(525279.08)))
    val t5 = DepositDailyBalancesRow(new LocalDate(2020, 2, 18), agencyId, deposit1, DepositAsset(WalletAsset.Zar.name, 2, BigDecimal(525104.02)))
    val febInterest = DepositDailyBalancesRow(new LocalDate(2020, 3, 1), agencyId, deposit1, DepositAsset(WalletAsset.Zar.name, 2, BigDecimal(527561.21)))

    // {:rates=>{"2020-01-17"=>{:annual=>"5.85", :daily=>"0.00016027397260273972602739726"}}, :interest=>245719, :calculated=>{:interest=>245719, :difference=>0}}
    // admin Fee @ 5.85% ==
    //{:rates=>{"2020-01-17"=>{:annual=>"5.85", :daily=>"0.00016027397260273972602739726",
    //                                                   :annual_admin_fee=>"2.0", :daily_fee=>"0.00005479452054794521"}},
    //                         :interest=>245719, :calculated=>{:interest=>245719, :difference=>0}}

    val ie = InterestEarned(Seq(InterestRate(new DateTime("2020-01-17"), BigDecimal(5.85),
      BigDecimal("0.00016027397260273972602739726"), BigDecimal(2.0), BigDecimal("0.00005479452054794521"))),
      BigDecimal(2457.19))

    "create payments" in {
      eventually(timeout(Span(duration, Seconds))) {
        depositConnector.insert(janInterest).map(_ => assert(true))
        depositConnector.insert(t1).map(_ => assert(true))
        depositConnector.insert(t2).map(_ => assert(true))
        depositConnector.insert(t3).map(_ => assert(true))
        depositConnector.insert(t4).map(_ => assert(true))
        depositConnector.insert(t5).map(_ => assert(true))
        depositConnector.insert(febInterest).map(_ => assert(true))
        depositConnector.insertEligibleAccount(agencyId, deposit1, WalletAsset.Zar.name, WalletAsset.Zar.precision, BigDecimal(527561.21))
          .map(_ => assert(true))
      }
    }

    "should return 8 transactions ordered by remittance date for February" in {
      depositConnector.fetch(DepositDailyBalancesRow, agencyId, deposit1, WalletAsset.Zar.name)
        .filter(_.date.getMonthOfYear == 2)
        .runWith(Sink.seq)
        .map { t =>
          assert(t.length == 6)
          assert(t.last.balance == WalletAsset.Zar(BigDecimal(525104.02)))
        }
      }

    "should allocate 2370.64 given a rate of 5.85 and take 810.47 given a rate of admin fee rate of 2%" in {
      val remittanceDate = DateUtcUtil.now().withDate(2020, 3, 1)
      val event = DepositInterestCreated("3ac216648ce10f9d5d30dde19279f7f4c689e6f1", ie, WalletAsset.Zar.name, 2, remittanceDate)
      val t = interestAllocatorImpl.calculatePendingAllocations(event)
        .runWith(Sink.seq)
      whenReady(t) { transactions =>
        assert(transactions.length == 1)
        assert(transactions.head.fee.scaled == BigDecimal(840.06))
        assert(transactions.head.interest.scaled == BigDecimal(2457.19))
      }
    }
  }

  // Deposit interest json event
  "Banking Event Listener" when {
    val json =
      """
        |{
        | "type":"DepositInterestAccrued",
        | "id":"c1ff1ffc-0fb4-495e-90a2-fa31b810e640",
        | "identifier":"3ac216648ce10f9d5d30dde19279f7f4c689e6f7",
        | "accountNumber":"**********",
        | "isScraped":false,
        | "transactionProcessingDate":"2020-03-01",
        | "transactionEffectiveDate":"2020-03-01",
        | "data": {
        |    "rec_type":"2",
        |    "account_number":"**********",
        |    "statement_number":"2123",
        |    "page_number":"1",
        |    "transaction_processing_date":"********",
        |    "transaction_effective_date":"********",
        |    "cheque_number":"0",
        |    "transaction_reference_number":"0",
        |    "transaction_amount_sign":"+",
        |    "transaction_amount":"245719",
        |    "account_balance_sign":"+",
        |    "account_balance_after_transaction":"********",
        |    "transaction_description":"CREDIT INTEREST      HEADOFFICE",
        |    "dep_id":"",
        |    "transaction_code":"IN71",
        |    "cheques_function_code":"INCR",
        |    "charge_levied_amount_sign":"+",
        |    "charge_levied_amount":"0",
        |    "charge_type":"",
        |    "stamp_duty_amount_sign":"+",
        |    "stamp_duty_levied_amount":"0",
        |    "cash_deposit_fee_sign":"+",
        |    "cash_deposit_fee":"0",
        |    "charges_accrued":"",
        |    "event_number":"68798",
        |    "statement_line_sequence_number":"3",
        |    "vat_amount":"0",
        |    "cash_portion":"0",
        |    "deposit_number":"0",
        |    "transaction_time":"0",
        |    "filler_1":"",
        |    "filler_2":"",
        |    "sitename":"",
        |    "category":"2150",
        |    "transaction_type":"",
        |    "deposit_id_description":"",
        |    "pod_adjustment_amount":"***************",
        |    "pod_adjustment_reason":"",
        |    "pod_returned_cheque_reason_code":"0",
        |    "pod_returned_cheque_drawee":"",
        |    "fedi_payor":"",
        |    "fedi_number":"0",
        |    "redirect_description":"",
        |    "account_number_redirect":"0",
        |    "unpaid_cheque_reason_description":"",
        |    "filler_3":"",
        |    "generation_number":"*********",
        |    "old_reconfocus_category1":"54",
        |    "old_reconfocus_category2":"54",
        |    "filler_4":"",
        |    "transaction_number_for_day":1
        |  },
        |  "interest":
        |    { "rates":
        |      {"2020-01-17":
        |         {"annual":"5.85",
        |          "daily":"0.00016027397260273972602739726",
        |          "annual_admin_fee":"2.0",
        |          "daily_fee":"0.00005479452054794521"
        |          }
        |      },
        |      "interest":245719,
        |      "calculated":{
        |         "interest":245719,"difference":0
        |       }
        |    }
        | }
        |""".stripMargin

    "DepositInterestAccrued" should {
      "allocate R2457.19" in {
        statementEventStub.send(Json.parse(json).as[StatementEvent])

        eventually(timeout(Span(duration, Seconds))) {
          val p = walletFactory.get(IrohaDepositInterest()).getAccount(IrohaDepositInterest.walletRef)
          whenReady(p) { response =>
            assert(response.flatMap(p => p.assets.collectFirst({ case x: WalletAsset.Zar => x.scaled })) === Some(BigDecimal(2457.19)))
          }
        }
      }

      "see deposit accrued interest event" in {
        implicit lazy val mat = server.materializer
        val source = client.walletEvents.subscribe.atMostOnceSource
        val probe = source
          .collect {
            case x: DepositInterestCreated => x
          }
          .runWith(TestSink.probe[WalletEvent](server.actorSystem))

        val nextEvents = probe.within(60.seconds) {
          probe.request(1).expectNextN(1)
        }

        nextEvents.count(_.isInstanceOf[DepositInterestCreated]) should ===(1)

        nextEvents.collect { case x: DepositInterestCreated => x }
          .last.interestEarned.interest should ===(BigDecimal(2457.19))

        probe.cancel
        succeed
      }
    }
  }
}
