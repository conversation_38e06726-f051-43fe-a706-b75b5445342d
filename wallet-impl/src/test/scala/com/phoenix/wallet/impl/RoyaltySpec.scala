package com.phoenix.wallet.impl

import com.phoenix.agency.api.{AgencyAddedToGroup, AgencyGroupCreated, GroupMember}
import com.phoenix.date.DateUtcUtil
import com.phoenix.finance.Beneficiary
import com.phoenix.invoice.api.InvoiceType
import com.phoenix.party.api.{Account, AccountType, Company_v1, PartyAccountAdded, PartyCreated_v1, PartyTag, Person_v1}
import com.phoenix.persistence.EventRecord
import com.phoenix.recon.api
import com.phoenix.recon.api.events.PaymentsApproved_v1
import com.phoenix.util.UUID
import com.phoenix.wallet.api.WalletBalanceAsset
import com.phoenix.wallet.impl.actors.{ClearanceScheduler, RoyaltyPayoutScheduler}
import com.phoenix.wallet.impl.commands.DemandPayment
import com.phoenix.wallet.impl.connectors.{AgencyRoyalty, AllocationHolding}
import com.phoenix.wallet.impl.entities.WalletEntity
import com.phoenix.wallet.impl.util.{IrohaIncomingEFT, IrohaOutgoingEFT, IrohaTransactionCreator, Receipt, WalletAsset, WalletRef}
import org.joda.time.DateTime
import org.scalatest.time.{Seconds, Span}

import scala.concurrent.Future


class RoyaltySpec extends BaseSpec {
  override protected val listeners: Seq[String] = Seq("status", "party", "agencyGroup", "recon")

  protected lazy val schedulerActorRef = server.application.royaltyPayoutScheduler.init
  protected lazy val clearanceSchedulerActorRef = server.application.clearanceScheduler.init

  val tenantPartyId = UUID.createV4
  val tenantAccountId = UUID.createV4
  val reference = "**********"
  val bankingHash = "ABC-121212-ABC"
  val tenantRef = WalletRef.account(tenantAccountId, agencyId)

  "RoyaltySpec" when {
    "Setup initial state" should {
      "create agency and group wallets" in {
        partyEventStub.send(PartyCreated_v1(tenantPartyId, Person_v1(
          Some(tenantPartyId), Some(agencyId), List(PartyTag.Tenant), Some("Test"), Some("Tenant"), Some("8*********1232"),None,
          None, "<EMAIL>", Some("*********"), None, None, None, None, None, None, None, None, Seq()
        ), Some(EventRecord(None))))
        partyEventStub.send(PartyCreated_v1(agencyId, Company_v1(Some(agencyId), Some(agencyId), List(PartyTag.Agency), Some("Test agency"), None, None, None, None, "<EMAIL>",
          None, None, None, None, None, None, None, None, None, None, None, None), Some(EventRecord(None))))
        val companyDetails = Company_v1(Some(agencyGroupId), None, List.empty, Some("Test 50/50 Group"), None, None, None, None, "<EMAIL>",
          None, None, None, None, None, None, None, None, None, None, None, None)
        agencyGroupEventStub.send(AgencyGroupCreated(agencyGroupId, companyDetails, EventRecord(None)))
        agencyGroupEventStub.send(AgencyAddedToGroup(agencyGroupId, GroupMember(agencyId, BigDecimal(50)), EventRecord(None)))

        info(s"creating agency UUID $agencyId")
        for {
//          _ <- walletEntityImpl.createAgencyGroupWallet(agencyGroupId, Some(userId))
          _ <- walletEntityImpl.createAgencyWallet(agencyId, Some(userId))

          reply <- WalletExistence.executeOnceCreated(Seq(WalletRef.agencyGroup(agencyGroupId), WalletRef.agency(agencyId)), persistentEntityRegistry, walletFactory) {
            () => Future(assert(true))
          }
        } yield reply
      }
      "see agency added to group" in {
        eventually {
          val f = agencyRoyaltiesConnector.fetch(agencyId)
          f.map { r =>
            r should ===(Some(AgencyRoyalty(agencyGroupId, agencyId, royaltyPercentage = BigDecimal(50))))
          }
        }
      }
      "insert account for tenant party" in {
        partyEventStub.send(
          PartyAccountAdded(tenantPartyId, agencyId, Account(tenantAccountId, tenantPartyId, reference, AccountType.Tenant, Some(UUID.createV4), Some("123 Test Address")))
        )

        eventually(timeout(Span(duration, Seconds))) {
          val p = partyReferencesConnector.findPartyAccountsByAccountId(agencyId, tenantAccountId)
          p.map{ response =>
            assert(response.map(_.reference) === Some(reference))
          }
        }
      }
      "allocate R10,000 to tenants wallet" in {
        val asset = WalletAsset.Zar(BigDecimal(10000))

        assetAllocationImpl.addAssetWithReference(WalletAsset.Zar.name, asset.amount, Option.empty[BigDecimal], reference, bankingHash, new DateTime, Option.empty).flatMap { _ =>
          eventually(timeout(Span(duration, Seconds))) {
            val p = walletFactory.get(IrohaTransactionCreator(tenantRef, persistentEntityRegistry)).getAccount(tenantRef)
            p.map { response =>
              assert(response.flatMap(p => p.assets.collectFirst({ case x: WalletAsset.Zar => x.scaled })) === Some(asset.amount))
            }
          }
        }
      }
      "give agency group wallet balance of R 10 000 and R 123 000 dummy money" in {
        val groupRef = WalletRef.agencyGroup(agencyGroupId)
        walletFactory.get(IrohaIncomingEFT())
          .receipt("1234", "abc-1234", WalletAsset.Zar(BigDecimal(10000)), Receipt.AllocatedExisting(groupRef, "Incoming EFT", None))
        walletFactory.get(IrohaIncomingEFT())
          .receipt("1235", "abc-1234", WalletAsset.ZarDummy(BigDecimal(123000)), Receipt.AllocatedExisting(groupRef, "Incoming EFT", None))
        eventually {
          val f = walletEntityImpl.getBalance(groupRef, None)
          f.map { reply =>
            assert(reply.assets.exists(a => a.name == WalletAsset.ZarDummy.name && a.balance == BigDecimal(123000)))
            assert(reply.assets.exists(a => a.name == WalletAsset.Zar.name && a.balance == BigDecimal(10000)))
          }
        }
      }
      "see agency group in table" in {
        eventually {
          val f = agencyGroupConnector.all
          f.map {
            result => assert(result.headOption.map(_.agencyGroupId.uuid).contains(agencyGroupId.uuid))
          }
        }
      }
    }
    val rentPaymentId = UUID.createV4
    val alarmPaymentId = UUID.createV4
    "approving payments on rent invoice" should {
      "see 100 rand transferred from tenant" in {
        reconEventStub.send(PaymentsApproved_v1(
          UUID.createV4, tenantAccountId, Some(InvoiceType.RentInvoice.getObjectName), agencyId,
          Seq(api.ApprovedPayment(rentPaymentId, Beneficiary.PartyBeneficiary(
            agencyId, PartyTag.Agency.getObjectName, "Commission", Some(BigDecimal(100.0)), None, Some(UUID.createV4)
          ))), EventRecord(None)
        ))
        eventually {
          val f = walletEntityImpl.getBalance(tenantRef, None)
          f.map { result =>
            assert(result.assets.toSet == Set(
              WalletBalanceAsset(WalletAsset.Zar.name, WalletAsset.Zar.precision, BigDecimal(10000) - BigDecimal(100))
            ))
          }
        }
      }
      "see payment in outgoing" in {
        eventually {
          val f = outgoingPaymentsConnector.findOutgoingPayments()
          f.map { result =>
            assert(result.exists(_.payment == rentPaymentId))
          }
        }
      }
      "demand payment" in {
        persistentEntityRegistry.refFor[WalletEntity](tenantRef.toIrohaString).ask(DemandPayment(rentPaymentId, userId, System.currentTimeMillis()))
        eventually {
          val f = outgoingPaymentsConnector.findOutgoingPayments()
          f.map { result =>
            assert(result.find(_.payment == rentPaymentId).exists(_.isDemanded))
          }
        }
      }
      "see 50 rand reserved" in {
        clearanceSchedulerActorRef ! ClearanceScheduler.ClearanceTick
        eventually {
          val f = allocationHoldingsConnector.unprocessed(agencyId, WalletAsset.Zar.name)
          f.map { result =>
            result.headOption should ===(Some(AllocationHolding(agencyId, agencyGroupId, rentPaymentId,
              paymentAmount = BigDecimal(100), royaltyAmount = BigDecimal(50), hasTransferred = false,
              asset = WalletAsset.Zar.name, txHash = Option.empty[String])))
          }
        }
      }
      "see 100 rand in agencies wallet" in {
        eventually {
          val f = walletEntityImpl.getBalance(WalletRef.global(agencyId, agencyId), None)
          f.map { result =>
            assert(result.assets.toSet == Set(
              WalletBalanceAsset(WalletAsset.Zar.name, WalletAsset.Zar.precision, BigDecimal(100)))
            )
          }
        }
      }
    }
    "approving payments on alarm invoice" should {
      "see 200 rand transferred from tenant" in {
        reconEventStub.send(PaymentsApproved_v1(
          UUID.createV4, tenantAccountId, Some(InvoiceType.Alarm.getObjectName), agencyId,
          Seq(api.ApprovedPayment(alarmPaymentId, Beneficiary.PartyBeneficiary(
            agencyId, PartyTag.Agency.getObjectName, "Commission", Some(BigDecimal(200.0)), None, Some(UUID.createV4)
          ))), EventRecord(None)
        ))
        eventually {
          val f = walletEntityImpl.getBalance(tenantRef, None)
          f.map { result =>
            assert(result.assets.toSet == Set(
              WalletBalanceAsset(WalletAsset.Zar.name, WalletAsset.Zar.precision, BigDecimal(10000) - BigDecimal(100))
            ))
          }
        }
      }
      "see payment in outgoing" in {
        eventually {
          val f = outgoingPaymentsConnector.findOutgoingPayments()
          f.map { result =>
            assert(result.exists(_.payment == alarmPaymentId))
          }
        }
      }
      "demand payment" in {
        persistentEntityRegistry.refFor[WalletEntity](tenantRef.toIrohaString).ask(DemandPayment(alarmPaymentId, userId, System.currentTimeMillis()))
        eventually {
          val f = outgoingPaymentsConnector.findOutgoingPayments()
          f.map { result =>
            assert(result.find(_.payment == alarmPaymentId).exists(_.isDemanded))
          }
        }
      }
      "see 0 rand reserved" in {
        clearanceSchedulerActorRef ! ClearanceScheduler.ClearanceTick
        eventually {
          val f = allocationHoldingsConnector.unprocessed(agencyId, WalletAsset.Zar.name)
          whenReady(f) { result =>
            result.headOption should ===(Some(AllocationHolding(agencyId, agencyGroupId, rentPaymentId,
              paymentAmount = BigDecimal(100), royaltyAmount = BigDecimal(50), hasTransferred = false,
              asset = WalletAsset.Zar.name, txHash = Option.empty[String])))
          }
        }
      }
      "see 300 rand in agencies wallet" in {
        eventually {
          val f = walletEntityImpl.getBalance(WalletRef.global(agencyId, agencyId), None)
          f.map { result =>
            assert(result.assets.toSet == Set(
              WalletBalanceAsset(WalletAsset.Zar.name, WalletAsset.Zar.precision, BigDecimal(300)))
            )
          }
        }
      }
    }
    "Test scheduled payout" should {
      "make payout payment on scheduled date" in {
        val dateTime = DateUtcUtil.now().withDate(2021, 7, 1)
          .withTime(3, 40, 0, 0)
        schedulerActorRef ! RoyaltyPayoutScheduler.DisburseRoyaltiesAt(dateTime)
        eventually {
          val f = walletEntityImpl.getBalance(IrohaOutgoingEFT.walletRef, None)
          f.map { result =>
            assert(result.assets.toSet == Set(
              WalletBalanceAsset(WalletAsset.Zar.name, WalletAsset.Zar.precision, BigDecimal(10000)),
              WalletBalanceAsset(WalletAsset.ZarDummy.name, WalletAsset.ZarDummy.precision, BigDecimal(123000))))
          }
        }
      }
    }
  }
}
