#play.crypto.secret is deprecated, use play.http.secret.key instead
play.http.secret.key = ${?APPLICATION_SECRET}
play.application.loader = com.phoenix.wallet.impl.WalletLoader

wallet.cassandra.keyspace = wallet
jwt.issuer = wallet

sentry-dsn = ${?SENTRY_DSN}

clearanceSchedulerDelay=5m

akka {
  actor {
    enable-additional-serialization-bindings = on

    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      jackson-cbor = "akka.serialization.jackson.JacksonCborSerializer"
    }

    serialization-bindings {
      "com.phoenix.wallet.impl.actors.ClearanceScheduler$Command" = jackson-json
      "com.phoenix.wallet.impl.actors.EasyPayApiScheduler$Command" = jackson-json
      "com.phoenix.wallet.impl.actors.RoyaltyPayoutScheduler$Command" = jackson-json
      "com.phoenix.wallet.impl.commands.WalletCommand" = jackson-json
      "com.phoenix.wallet.impl.commands.BadReferenceCommand" = jackson-json
      "com.phoenix.wallet.impl.events.WalletEvent" = jackson-json
      "com.phoenix.wallet.impl.events.BadReferenceEvent" = jackson-json
      "com.phoenix.wallet.impl.WalletState" = jackson-json
      "com.phoenix.wallet.impl.BadReferenceState" = jackson-json
    }
  }

  serialization.jackson {
    jackson-modules += "com.fasterxml.jackson.datatype.joda.JodaModule"
  }
}

cassandra-journal.keyspace = ${wallet.cassandra.keyspace}
cassandra-snapshot-store.keyspace = ${wallet.cassandra.keyspace}
lagom.persistence.read-side.cassandra.keyspace = ${wallet.cassandra.keyspace}

lagom.persistence.read-side.global-prepare-timeout = 120s
lagom.broker.kafka.client.consumer.batching-size = 1

# Enable new sharding state store mode by overriding Lagom's default
akka.cluster.sharding.state-store-mode = ddata

cassandra-journal.write-static-column-compat = off
cassandra-query-journal {
  write-static-column-compat = off
  first-time-bucket = "20190101T00:00"
}

# Enable serializers provided in Akka 2.5.8+ to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done"                 = akka-misc
  "akka.actor.Address"        = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

lagom.circuit-breaker {
  default {
    exception-whitelist = [
      "com.lightbend.lagom.scaladsl.api.transport.NotFound"
    ]
    call-timeout = 10s
  }
  easypay {
    call-timeout = 30s
    reset-timeout = 10s
  }
}

easypay.protocol =${?EASYPAY_PROTOCOL}
easypay.host =${?EASYPAY_HOST}
easypay.port = ${?EASYPAY_PORT}
easypay.token = ${?EASYPAY_TOKEN}
easypay.card_acceptor = ${?CARD_ACCEPTOR}
easypay.terminal = ${?EASYPAY_TERMINAL}

iroha {
  wallet = "IrohaWallet"
  wallet = ${?IROHA_WALLET}
  admin {
    public-key = "313a07e6384776ed95447710d15e59148473ccfc052a681317a72a69f2a49910"
    public-key= ${?IROHA_ADMIN_PUB}
    private-key = "f101537e319568c765b2cc89698325604991dca57b9716b58016b253506cab70"
    private-key= ${?IROHA_ADMIN_PRI}
    name = "main"
    domain = "phoenix.coin"
  }
}

akka.grpc.client {
    iroha {
        # Host to use if service-discovery-mechanism is set to static
        host = "127.0.0.1"
        host = ${?IROHA_HOST}

        service-discovery {
            mechanism = "static"
            mechanism = ${?IROHA_MECHANISM}
            # Service name to use if a service-discovery.mechanism other than static
            service-name = "iroha"
            service-name = ${?IROHA_SVC_NAME}
            # See https://developer.lightbend.com/docs/akka-management/current/discovery/index.html for meanings for each mechanism
            # if blank then not passed to the lookup
            port-name = ""
            protocol = ""

            # timeout for service discovery resolving
            resolve-timeout = 1s
        }

        # port to use if service-discovery-mechism is static or service discovery does not return a port
        port = 50051

        deadline = infinite
        override-authority = ""
        user-agent = ""
        # Pulls default configuration from ssl-config-core's reference.conf
        # ssl-config = ${ssl-config}
        use-tls = false

        # TODO: Enforce HTTP/2 TLS restrictions: https://tools.ietf.org/html/draft-ietf-httpbis-http2-17#section-9.2
        connection-attempts = -1

        # Service discovery mechamism to use. The default is to use a static host
        # and port that will be resolved via DNS.
        # Any of the mechanisms described [here](https://developer.lightbend.com/docs/akka-management/current/discovery/index.html) can be used
        # including Kubernetes, Consul, AWS API
    }
}

readSidePostgres.username=${?PG_PHOENIX_USERNAME}
readSidePostgres.password=${?PG_PHOENIX_PASSWORD}
readSidePostgres.database=${?PG_PHOENIX_DATABASE}
readSidePostgres.port=${?PG_PHOENIX_PORT}
readSidePostgres.host=${?PG_PHOENIX_HOST}
readSidePostgres.maximumMessageSize=${?PG_PHOENIX_MAXMSG}
