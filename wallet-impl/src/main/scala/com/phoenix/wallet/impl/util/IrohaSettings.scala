package com.phoenix.wallet.impl.util

import java.security.KeyPair

import akka.actor.{ActorSystem, ExtendedActorSystem, Extension, ExtensionId, ExtensionIdProvider}
import com.typesafe.config.Config
import net.cimadai.iroha.{Account, Utils}

case class IrohaSettings(irohaAdmin: Account, irohaKeypair: KeyPair, wallet: String) {
  val adminWalletRef = WalletRef(irohaAdmin.name, irohaAdmin.domain)
}

class IrohaSettingsImpl(config: Config) extends Extension {
  private val c = config

  val irohaPublicKey = c.getString("iroha.admin.public-key")
  val irohaPrivateKey = c.getString("iroha.admin.private-key")

  val settings = IrohaSettings(
    irohaAdmin = Account(c.getString("iroha.admin.name"), c.getString("iroha.admin.domain")),
    irohaKeypair = Utils.parseHexKeypair(irohaPublicKey, irohaPrivateKey),
    wallet = c.getString("iroha.wallet")
  )
}

object IrohaSettingsExtension extends ExtensionId[IrohaSettingsImpl] with ExtensionIdProvider {

  override def lookup = IrohaSettingsExtension

  override def createExtension(system: ExtendedActorSystem) = new IrohaSettingsImpl(system.settings.config)

  /**
    * Java API: retrieve the Settings extension for the given system.
    */
  override def get(system: ActorSystem): IrohaSettingsImpl = super.get(system)
}
