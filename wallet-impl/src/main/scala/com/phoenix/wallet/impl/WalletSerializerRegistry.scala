package com.phoenix.wallet.impl

import com.lightbend.lagom.scaladsl.playjson.{JsonSerializer, JsonSerializerRegistry}
import com.phoenix.util.JsonSerializerImpl._
import com.phoenix.wallet.impl.commands.QueryBadReferenceReply

import scala.collection.immutable.Seq

object WalletSerializerRegistry extends JsonSerializerRegistry {
  private def api = {
    import com.phoenix.wallet.api._
    import com.phoenix.banking.api._
    Seq(
      JsonSerializer[CreateReceiptRequest],
      JsonSerializer[WalletTransactionResponse],
      <PERSON>sonSerializer[AssetBalance],
      <PERSON>sonSerializer[PartyAccount],
      JsonSerializer[CreateWalletRequest],
      <PERSON>sonSerializer[WalletPayoutRequest],
      JsonSerializer[CreateProofOfPaymentRequest],
      JsonSerializer[ProofOfPaymentResponse],
      JsonSerializer[PartyBalancesResponse],
      <PERSON>sonSerializer[InterestEarned],
      <PERSON>sonSerializer[DepositInterestCreated],
      JsonSerializer[WalletTransferRequest],
      JsonSerializer[WalletTransfer]
    ) ++ generateJsonSerializersFor[WalletEvent]
  }

  private def state = {
    import com.phoenix.persistence._
    import com.phoenix.wallet.impl.util._
    Seq(
      JsonSerializer[StateRecord],
      JsonSerializer[CreatedState],
      JsonSerializer[DeletedState],
      JsonSerializer[EventRecord],
      JsonSerializer[WalletTransaction],
      JsonSerializer[WalletKeypair],
      JsonSerializer[WalletState],
      JsonSerializer[WalletRef],
      JsonSerializer[WalletAsset],
      JsonSerializer[BadReferenceState],
    ) ++ generateJsonSerializersFor[TransactionStatus]
  }

  private def events = {
    import com.phoenix.wallet.impl.events._
    Seq(JsonSerializer[CheckTransactionStatus], JsonSerializer[WalletApiEvent]) ++
    generateJsonSerializersFor[WalletEvent] ++ generateJsonSerializersFor[BadReferenceEvent]
  }

  private def commands = {
    import com.phoenix.wallet.impl.commands._
    import com.phoenix.wallet.impl.entities._
    Seq(
      JsonSerializer[ApprovedPayment],
      JsonSerializer[WalletPayment],
      JsonSerializer[SignedTransaction],
      JsonSerializer[ExistingCommandsReply],
      JsonSerializer[QueryBadReferenceReply],
      JsonSerializer[BadReferenceNotFound],
      JsonSerializer[InvalidWallet],
      JsonSerializer[UnavailableWallet],
      JsonSerializer[WalletDoesNotExist],
      JsonSerializer[WalletAlreadyCreated],
      JsonSerializer[PaymentStatusExists],
    ) ++ generateJsonSerializersFor[WalletCommand[_]] ++ generateJsonSerializersFor[BadReferenceCommand[_]]
  }

  override def serializers: Seq[JsonSerializer[_]] = api ++ state ++ events ++ commands
}
