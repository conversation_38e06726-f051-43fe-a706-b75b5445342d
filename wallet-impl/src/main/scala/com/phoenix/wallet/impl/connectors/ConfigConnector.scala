package com.phoenix.wallet.impl.connectors

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import io.getquill.{CassandraLagomAsyncContext, SnakeCase}

import scala.concurrent.{ExecutionContext, Future}

class ConfigConnector(session: CassandraSession)(implicit ec: ExecutionContext) {
  private val ctx = new CassandraLagomAsyncContext(SnakeCase, session)
  import com.phoenix.persistence.QuillHelper.Implicits.{decodeUUID, encodeUUID}
  import ctx._

  private val config = quote {
    querySchema[Config]("config")
  }

  def insert(property: String, value: String): Future[Done] = {
    val q = quote { config.update(_.property -> lift(property), _.value -> lift(value)) }
    ctx.run(q)
  }

  def find(property: String): Future[Option[String]] = {
    ctx.run(quote { config.filter(_.property == lift(property)) })
      .map(_.headOption.map(_.value))
  }

  private case class Config(property: String, value: String)
}
