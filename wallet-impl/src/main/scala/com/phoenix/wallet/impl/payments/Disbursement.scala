package com.phoenix.wallet.impl.payments

import akka.actor.ActorSystem
import akka.stream.Materializer
import akka.stream.scaladsl.{Flow, Source}
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.phoenix.date.DateUtcUtil
import com.phoenix.persistence.{EmptyState, EventRecord, NonEmptyState}
import com.phoenix.util.UUID
import com.phoenix.wallet.api.PaymentKind
import com.phoenix.wallet.impl.commands.{AccountInfoReply, QueryAccountInfo}
import com.phoenix.wallet.impl.connectors.TransactionStatusConnector
import com.phoenix.wallet.impl.entities.WalletEntity
import com.phoenix.wallet.impl.listeners.ReconEventListener
import com.phoenix.wallet.impl.util.{WalletAsset, WalletFactory, WalletKeypair, WalletRef, WalletTransaction}
import com.phoenix.wallet.impl.{ApprovedPayment, AssetAllocationImpl, CreateWallet, WalletExistence}
import com.typesafe.scalalogging.LazyLogging

import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class Disbursement(walletFactory: WalletFactory,
                   persistentEntityRegistry: PersistentEntityRegistry,
                   statusConnector: TransactionStatusConnector,
                   walletImpl: Wallet, assetAllocationImpl: AssetAllocationImpl)
                  (implicit ec: ExecutionContext, ac: ActorSystem, mat: Materializer) extends LazyLogging {
  private def newWallets(payments: Seq[ApprovedPayment]) = {
    val createdableWallets = payments.collect {
      case ApprovedPayment(_, _, _, _, paymentKind: PaymentKind.PartyPayment, _, _, _, _, Some(agency)) =>
        WalletRef.global(paymentKind.toParty, agency)
    }
    lazy val wallets = createdableWallets.distinct.map { wallet =>
      persistentEntityRegistry.refFor[WalletEntity](wallet.toIrohaString).ask(QueryAccountInfo)
        .map[Try[Option[(WalletRef, WalletKeypair)]]] {
          case AccountInfoReply(_, Nil, EmptyState()) => Success(Some((wallet, WalletKeypair.create())))
          case AccountInfoReply(_, commands, EmptyState()) =>
            commands.find(_.isInstanceOf[CreateWallet]) match {
              case None => Failure(new Exception(s"$wallet Wallet does not exist despite containing queued commands"))
              case Some(command) =>
                Failure(new Exception(s"$wallet Wallet already has creation command pending [${command.transaction.txHash}]"))
            }
          case AccountInfoReply(_, _, NonEmptyState()) => Success(None)
        }
    }
    if (createdableWallets.isEmpty) Future(Seq.empty) else Future.sequence(wallets)
  }

  private def allocateDummyPayments = {
    Flow[(WalletTransaction, Seq[ApprovedPayment])].flatMapMerge(1, { case (tx, payments) =>
      val tailReturn = immutable.Seq(payments.map(p => (tx, p)): _*)
      if (payments.nonEmpty) {
        val flow = Source(immutable.Seq(payments: _*)).mapAsync(1) {
          case ApprovedPayment(payment, walletAsset, _, _, paymentKind: PaymentKind.DepositPayment, _, _, _, _, _)
            if walletAsset.name == WalletAsset.ZarDummy.name =>
            assetAllocationImpl.addDepositAssetWithReference(walletAsset.name, walletAsset.amount, paymentKind.reference, payment, DateUtcUtil.now(), Some("REOS")).map(_ => Done)
          case ApprovedPayment(payment, walletAsset, _, _, paymentKind: PaymentKind.DepositTransfer, _, _, _, _, _)
            if walletAsset.name == WalletAsset.ZarDummy.name =>
            assetAllocationImpl.addAssetWithReference(walletAsset.name, walletAsset.amount, Option.empty[BigDecimal], paymentKind.reference, payment, DateUtcUtil.now(), Some("REOS")).map(_ => Done)
          case _ => Future(Done)
        }
        flow.grouped(payments.size)
          .map(_ => tailReturn)
      } else {
        Source.single(tailReturn)
      }
    })
  }

  private def approvePayments(sourceWallet: WalletRef, agencyId: UUID, eventRecord: EventRecord) = {
    Flow[Seq[ApprovedPayment]].mapAsync(1) { approvedPayments =>
      for {
        wallets <- newWallets(approvedPayments)

        errors = wallets.collect({ case Failure(x) => x })

        createdWallets = wallets.collect({ case Success(Some(x)) => x })

        walletCreationTx <- walletImpl.createWalletTransactions(Map(createdWallets: _*), agencyId)
        _ <- walletImpl.createWalletCommands(walletCreationTx, agencyId, eventRecord)

        tx <- if (errors.nonEmpty) Future(Option.empty[WalletTransaction]) else {
          WalletExistence.executeOnceCreated(walletCreationTx.map(_._1), persistentEntityRegistry, walletFactory) { () =>
            walletImpl.processApprovePayments(sourceWallet, approvedPayments, Option.empty)
              .flatMap {
                case optTx @ Some(pendingTx) => statusConnector.waitForTransactionCommitted(pendingTx).map(_ => optTx)
                case optTx => Future(optTx)
              }
          }
        }
      } yield {
        errors.foreach(p => logger.error(p.getMessage))

        if (errors.nonEmpty) throw new Exception("Errors not empty")

        (tx.get, approvedPayments.filter(_.walletAsset.name == WalletAsset.ZarDummy.name))
      }
    }
  }

  def approvePaymentsFlow(sourceWallet: WalletRef, agencyId: UUID, eventRecord: EventRecord): Flow[Seq[ApprovedPayment], Seq[(WalletTransaction, ApprovedPayment)], NotUsed] = {
    Flow[Seq[ApprovedPayment]]
      .via(approvePayments(sourceWallet, agencyId, eventRecord))
      .via(allocateDummyPayments)
  }
}
