package com.phoenix.wallet.impl.connectors

import java.util.UUID

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.phoenix.party.api.AccountType
import com.phoenix.persistence.QuillHelper
import io.getquill.{CassandraLagomAsyncContext, SnakeCase}

import scala.concurrent.{ExecutionContext, Future}

class PartyAccountsConnector(session: CassandraSession)(implicit ec: ExecutionContext) {
  val ctx = new CassandraLagomAsyncContext(SnakeCase, session)
  import ctx._
  import QuillHelper.Implicits._

  implicit val encodeAccountType = MappedEncoding[AccountType, String](_.getObjectName)
  implicit val decodeAccountType = MappedEncoding[String, AccountType](AccountType(_))

  private val partyReferenceTable = quote {
    querySchema[PartyReference]("party_references")
  }

  private val partyAccountTable = quote {
    querySchema[PartyAccountType]("party_accounts")
  }

  private val partyAccountByAccountIdTable = quote {
    querySchema[PartyAccountType]("party_accounts_by_account_id")
  }

  def insertPartyReference(partyReference: PartyReference): Result[Done] = {
    val q = quote { partyReferenceTable.insert(lift(partyReference)) }
    run(q)
  }

  def insertPartyReferenceAddress(reference: String, address: Option[String]): Result[Done] = {
    val q = quote { partyReferenceTable.filter(_.reference == lift(reference)).update(_.propertyAddress -> lift(address)) }
    run(q)
  }

  def findPartyReference(reference: String): Result[Option[PartyReference]] = {
    val q = quote { partyReferenceTable.filter(_.reference == lift(reference)) }
    run(q).map(_.headOption)
  }

  def insertPartyAccount(partyAccount: PartyAccountType): Result[Done] = {
    val PartyAccountType(agencyId, partyId, accountId, accountType, reference, portfolioId, propertyAddress, _) = partyAccount
    val q1 = quote { partyAccountTable.insert(
      _.agencyId -> lift(agencyId), _.partyId -> lift(partyId), _.accountId -> lift(accountId), _.accountType -> lift(accountType),
      _.reference -> lift(reference), _.portfolioId -> lift(portfolioId), _.propertyAddress -> lift(propertyAddress)
    ) }
    val q2 = quote { partyAccountByAccountIdTable.insert(
      _.agencyId -> lift(agencyId), _.partyId -> lift(partyId), _.accountId -> lift(accountId), _.accountType -> lift(accountType),
      _.reference -> lift(reference), _.portfolioId -> lift(portfolioId), _.propertyAddress -> lift(propertyAddress)
    ) }
    for {
      _ <- run(q1)
      _ <- run(q2)
    } yield Done
  }

  def insertDepositReference(agencyId: UUID, partyId: UUID, accountId: UUID, depositReference: String): Future[Done] = {
    val q1 = quote { partyAccountTable.insert(
      _.agencyId -> lift(agencyId), _.partyId -> lift(partyId), _.accountId -> lift(accountId), _.depositReference -> lift(Option(depositReference))
    ) }
    val q2 = quote { partyAccountByAccountIdTable.insert(
      _.agencyId -> lift(agencyId), _.partyId -> lift(partyId), _.accountId -> lift(accountId), _.depositReference -> lift(Option(depositReference))
    ) }
    for {
      _ <- run(q1)
      _ <- run(q2)
    } yield Done
  }

  def insertAccountAddress(agencyId: UUID, partyId: UUID, accountId: UUID, address: Option[String]): Future[Done] = {
    val q1 = quote {
      partyAccountTable.insert(
        _.agencyId -> lift(agencyId), _.partyId -> lift(partyId), _.accountId -> lift(accountId), _.propertyAddress -> lift(address)

      )
    }
    val q2 = quote {
      partyAccountByAccountIdTable.insert(
        _.agencyId -> lift(agencyId), _.partyId -> lift(partyId), _.accountId -> lift(accountId), _.propertyAddress -> lift(address)
      )
    }
    for {
      _ <- run(q1)
      _ <- run(q2)
    } yield Done
  }

  def findPartyAccounts(agencyId: UUID, partyId: UUID): Result[Seq[PartyAccountType]] = {
    val q = quote { partyAccountTable.filter(p => p.agencyId == lift(agencyId) && p.partyId == lift(partyId)) }
    run(q)
  }

  def findPartyAccountsByAccountId(agencyId: UUID, accountId: UUID): Result[Option[PartyAccountType]] = {
    val q = quote { partyAccountByAccountIdTable.filter(p => p.agencyId == lift(agencyId) && p.accountId == lift(accountId)) }
    run(q).map(_.headOption)
  }
}

case class PartyReference(accountId: UUID, agencyId: UUID, partyId: UUID, accountType: AccountType, reference: String, portfolioId: Option[UUID], propertyAddress: Option[String])
case class PartyAccountType(agencyId: UUID, partyId: UUID, accountId: UUID, accountType: AccountType, reference: String, portfolioId: Option[UUID], propertyAddress: Option[String],
                            depositReference: Option[String] = Option.empty)
object PartyAccountType {
  implicit class ReferenceConverter(pat: PartyAccountType) {
    def mainToDeposit: String = pat.depositReference.getOrElse(s"TRS${pat.reference}")
  }

  def toPartyAccountType(p: PartyReference): PartyAccountType = {
    PartyAccountType(p.agencyId, p.partyId, p.accountId, p.accountType, p.reference, p.portfolioId, p.propertyAddress, None)
  }
}
