package com.phoenix.wallet.impl.entities

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import com.lightbend.lagom.scaladsl.pubsub.{PubSubRegistry, TopicId}
import com.phoenix.bytes.HexBytesUtil
import com.phoenix.date.DateUtcUtil
import com.phoenix.json.JsonFormats
import com.phoenix.persistence.{EmptyState, EventRecord, NonEmptyState, StateRecord}
import com.phoenix.wallet.impl._
import com.phoenix.wallet.impl.commands._
import com.phoenix.wallet.impl.connectors.AcceptedDetails
import com.phoenix.wallet.impl.events._
import com.phoenix.wallet.impl.util._
import jp.co.soramitsu.crypto.ed25519.Ed25519Sha3
import net.cimadai.iroha.Utils
import play.api.libs.json.{Format, Json}

import scala.concurrent.ExecutionContext
import scala.util.control.NoStackTrace


class WalletEntity()
                  (implicit ec: ExecutionContext, irohaSettings: IrohaSettings) extends PersistentEntity {
  override type Command = WalletCommand[_]
  override type Event = WalletEvent
  override type State = WalletState

  override def initialState: WalletState = WalletState(0, None, StatusCollection.empty, Nil, EmptyState())

  import PaymentsCollection._
  import StateValidation._

  private def walletRef = {
    val s = entityId.split("@")
    WalletRef(s(0), s(1))
  }

  private def adminPhx = Actions()

  private def newWallet = Actions()
    .onCommand[CreateWalletCommand, Done] {
      case (_: CreateWalletCommand, ctx, WalletState(_, _, status, _, _)) if status.commands.exists(_.isInstanceOf[CreateWallet]) =>
        val cmd = status.commands.find(_.isInstanceOf[CreateWallet]).get
        ctx.commandFailed(UnavailableWallet(s"Wallet already has initial transaction: ${cmd.transaction.txHash}"))
        ctx.done
      case (CreateWalletCommand(command), ctx, _) =>
        ctx.thenPersist(CommandSubmitted(command)) { _ =>
          ctx.reply(Done)
        }
    }
    .onCommand[AddReceiptCommand, Done] {
      case (AddReceiptCommand(AddReceipt(_, _, _, _, _, receipt: Receipt.AllocateNew, _, _)), ctx, WalletState(_, _, status, _, _))
        if status.commands.exists(_.isInstanceOf[CreateWallet]) =>
        val cmd = status.commands.find(_.isInstanceOf[CreateWallet]).get
        ctx.commandFailed(UnavailableWallet(s"Cannot allocate receipt as wallet has pending creation: ${cmd.transaction.txHash} keypair = ${receipt.walletKeypair}"))
        ctx.done
      case (AddReceiptCommand(addReceipt @ AddReceipt(transaction, _, _, _, _, receipt: Receipt.AllocateNew, _, _)), ctx, _) =>
        ctx.thenPersistAll(
          CommandSubmitted(CreateWallet(transaction, None, None, receipt.walletKeypair)),
          CommandSubmitted(addReceipt)
        ) { () =>
          ctx.reply(Done)
        }
      case (AddReceiptCommand(AddReceipt(_, _, _, _, _, receipt, _, _)), ctx, _) =>
        ctx.commandFailed(InvalidWallet(s"Cannot use receipt of [${receipt.getClass.getSimpleName}] using a new wallet"))
        ctx.done
    }
    .onReadOnlyCommand[QueryAccountInfo.type, AccountInfoReply] {
      case (_, ctx, state) => ctx.reply(AccountInfoReply(state.walletDepth, state.statuses.commands, state.stateRecord))
    }
    .onReadOnlyCommand[SignTransaction, SignedTransaction] {
      case (_: SignTransaction, ctx, WalletState(_, _, status, _, _))
        if status.commands.exists(_.isInstanceOf[CreateWallet]) =>
        val cmd = status.commands.find(_.isInstanceOf[CreateWallet]).get
        ctx.commandFailed(UnavailableWallet(s"Cannot sign as wallet has pending creation: ${cmd.transaction.txHash}"))
      case (cmd: SignTransaction, ctx, _) =>
        ctx.commandFailed(WalletDoesNotExist(s"Cannot sign transaction until wallet is created: ${cmd.walletTransaction.txHash}"))
    }
    .onEvent {
      case (AccountCreated(walletPrivateKey, transaction, agency, eventRecord), state @ WalletState(_, _, _, _, StateRecord(b))) =>
        state.copy(
          privateKey = Some(walletPrivateKey),
          stateRecord = b.update(eventRecord).stateRecord
        )
    }

  private def actionPayments = Actions()
    .onCommand[ActionPayment, Done] {
      case (ActionPayment(paymentId, isEft, actionedAt), ctx, state) =>
        state.payments.approved(paymentId) match {
          case Some(tagged)
            if tagged.tags.contains("payment_actioned") =>
            ctx.commandFailed(PaymentStatusExists(s"Actioned Payment [$paymentId] already exists in this entity"))
            ctx.done
          case Some(tagged) =>
            ctx.thenPersist(PaymentActioned(tagged, isEft, EventRecord(None, actionedAt))) { _ =>
              ctx.reply(Done)
            }
          case None =>
            ctx.invalidCommand(s"Approved Payment not found $paymentId")
            ctx.done
        }
    }
    .onEvent {
      case (event: PaymentActioned, state) =>
        state.copy(payments = state.payments.appendPaymentActioned(Seq(event.payment.approvedPayment)))
    }

  private def demandPayments = Actions()
    .onCommand[DemandPayment, Done] {
      case (DemandPayment(paymentId, userId, demandedAt), ctx, state) =>
        state.payments.approved(paymentId) match {
          case Some(tagged) if tagged.hasProcessed =>
            ctx.commandFailed(PaymentStatusExists(s"Cannot demand already processed payment [$paymentId] ${tagged.tags}"))
            ctx.done
          case Some(tagged) =>
            ctx.thenPersist(PaymentDemanded(tagged.approvedPayment, EventRecord(Some(userId), demandedAt))) { _ =>
              ctx.reply(Done)
            }
          case None =>
            ctx.invalidCommand(s"Approved Payment not found $paymentId")
            ctx.done
        }
    }
    .onEvent {
      case (event: PaymentDemanded, state) =>
        state.copy(payments = state.payments.appendPaymentDemanded(Seq(event.payment)))
    }

  private def reversePayments = Actions()
    .onCommand[ReversePayment, Done] {
      case (ReversePayment(paymentId, userId, reversedAt), ctx, state) =>
        state.payments.approved(paymentId) match {
          case Some(tagged) if tagged.hasProcessed =>
            ctx.commandFailed(PaymentStatusExists(s"Cannot reverse already processed payment [$paymentId]"))
            ctx.done
          case Some(tagged) =>
            ctx.thenPersist(PaymentReversed(tagged.approvedPayment, EventRecord(Some(userId), reversedAt))) { _ =>
              ctx.reply(Done)
            }
          case None =>
            ctx.invalidCommand(s"Approved Payment not found $paymentId")
            ctx.done
        }
    }
    .onEvent {
      case (event: PaymentReversed, state) =>
        state.copy(payments = state.payments
          .appendPaymentReversed(Seq(event.payment))
          .assignReversedAt(event.payment, event.eventRecord))
    }

  private def approvePayments = Actions()
    .onCommand[ApprovePaymentsCommand, Done] {
      case (ApprovePaymentsCommand(_, command), ctx, state)
        if command.payments.exists(p => state.payments.get(p.payment).nonEmpty) =>
        command.retryFrom match {
          case Some(value) if state.statuses.commands.map(_.transaction).contains(value) =>
            ctx.thenPersist(CommandSubmitted(command)) { _ =>
              ctx.reply(Done)
            }
          case _ =>
            ctx.commandFailed(PaymentStatusExists("Approved Payment(s) already exist in this entity"))
            ctx.done
        }
      case (ApprovePaymentsCommand(_, command), ctx, _) =>
        ctx.thenPersist(CommandSubmitted(command)) { _ =>
          ctx.reply(Done)
        }
    }

  private def acceptPayments = Actions()
    .onCommand[AcceptPayment, Done] {
      case (AcceptPayment(paymentId), ctx, state) =>
        state.payments.approved(paymentId) match {
          case Some(tagged)
            if tagged.tags.contains("payment_accepted") =>
            ctx.commandFailed(PaymentStatusExists(s"Accepted Payment [$paymentId] already exists in this entity"))
            ctx.done
          case Some(tagged) =>
            ctx.thenPersist(PaymentAccepted(tagged.approvedPayment)) { _ =>
              ctx.reply(Done)
            }
          case None =>
            ctx.invalidCommand(s"Approved Payment not found $paymentId")
            ctx.done
        }
    }
    .onEvent {
      case (event: PaymentAccepted, state) =>
        state.copy(payments = state.payments.appendPaymentAccepted(Seq(event.payment)))
    }

  private def acceptPaymentRequests = Actions()
    .onCommand[AcceptPaymentRequest, Done] {
      case (AcceptPaymentRequest(paymentId), ctx, state) =>
        state.payments.approved(paymentId) match {
          case Some(tagged)
            if tagged.tags.contains("request_accepted") =>
            ctx.commandFailed(PaymentStatusExists(s"Accepted Payment Request [$paymentId] already exists in this entity"))
            ctx.done
          case Some(tagged) =>
            ctx.thenPersist(PaymentRequestAccepted(tagged.approvedPayment, Some(EventRecord(Option.empty)))) { _ =>
              ctx.reply(Done)
            }
          case None =>
            ctx.invalidCommand(s"Approved Payment not found $paymentId")
            ctx.done
        }
    }
    .onEvent {
      case (event: PaymentRequestAccepted, state) =>
        state.copy(payments = state.payments.appendRequestAccepted(Seq(event.payment)))
    }

  private def verifyPayments = Actions()
    .onCommand[VerifyPayment, Done] {
      case (VerifyPayment(paymentId), ctx, state) =>
        state.payments.approved(paymentId) match {
          case Some(tagged)
            if tagged.tags.contains("payment_verified") =>
            ctx.commandFailed(PaymentStatusExists(s"Verified Payment [$paymentId] already exists in this entity"))
            ctx.done
          case Some(tagged) =>
            ctx.thenPersist(PaymentVerified(tagged.approvedPayment, Some(EventRecord(None)))) { _ =>
              ctx.reply(Done)
            }
          case None =>
            ctx.invalidCommand(s"Approved Payment not found $paymentId")
            ctx.done
        }
    }
    .onEvent {
      case (event: PaymentVerified, state) =>
        state.copy(
          payments = state.payments.appendVerified(Seq(event.payment))
        )
    }

  private def acceptPaymentsToParty = Actions()
    .onCommand[AcceptPaymentToParty, Done] {
      case (AcceptPaymentToParty(paymentId, toParty, partyBankDetails, paymentProofDetails), ctx, state) =>
        state.payments.approved(paymentId) match {
          case Some(tagged)
            if tagged.tags.contains("payment_to_party_accepted") =>
            ctx.commandFailed(PaymentStatusExists(s"Payment to party accepted Payment [$paymentId] already exists in this entity"))
            ctx.done
          case Some(tagged) =>
            ctx.thenPersist(PaymentToPartyAccepted(tagged.approvedPayment, toParty, partyBankDetails, paymentProofDetails, EventRecord(None))) { _ =>
              ctx.reply(Done)
            }
          case None =>
            ctx.invalidCommand(s"Approved Payment not found $paymentId")
            ctx.done
        }
    }
    .onEvent {
      case (event: PaymentToPartyAccepted, state) =>
        state.copy(
          payments = state.payments.appendToPartyAccepted(Seq(event.payment))
        )
    }

  private def requestProofOfPayment = Actions()
    .onCommand[RequestProofOfPayment, Done] {
      case (RequestProofOfPayment(requestId, batchSize, sendToEmail, subject, agency, acceptedPayment, eventRecord), ctx, state) =>
        state.payments.approved(acceptedPayment.paymentId) match {
          case Some(tagged)
            if tagged.tags.contains("payment_verified") =>
            acceptedPayment.details match {
              case accepted: AcceptedDetails.BankDetails =>
                ctx.thenPersist(ProofOfPaymentRequested(requestId, batchSize, acceptedPayment.txHash, sendToEmail, subject, agency,
                  acceptedPayment.paymentId, acceptedPayment.toParty, Option.empty, Option(accepted),
                  acceptedPayment.paymentProofDetails, acceptedPayment.verifiedAt.get, eventRecord)) { _ =>
                  ctx.reply(Done)
                }
              case accepted: AcceptedDetails.EasyPayDetails =>
                ctx.thenPersist(ProofOfPaymentRequested(requestId, batchSize, acceptedPayment.txHash, sendToEmail, subject, agency,
                  acceptedPayment.paymentId, Option.empty, Option(accepted), Option.empty,
                  acceptedPayment.paymentProofDetails, acceptedPayment.verifiedAt.get, eventRecord)) { _ =>
                  ctx.reply(Done)
                }
              case AcceptedDetails.MissingDetails =>
                ctx.invalidCommand("Accepted payment is missing details required for proof of payment")
                ctx.done
            }
          case Some(_) =>
            ctx.invalidCommand(s"Cannot request proof for unverified payment")
            ctx.done
          case None =>
            ctx.invalidCommand(s"Approved Payment not found ${acceptedPayment.paymentId}")
            ctx.done
        }
    }
    .onEvent {
      case (_: ProofOfPaymentRequested, state) => state
    }

  private def rejectPayments = Actions()
    .onCommand[RejectPaymentRequestCommand, Done] {
      case (RejectPaymentRequestCommand(command), ctx, state)
        if state.payments.approved(command.paymentId).isEmpty =>
        ctx.invalidCommand(s"Approved Payment not found ${command.paymentId}")
        ctx.done
      case (RejectPaymentRequestCommand(command), ctx, state)
        if state.payments.approved(command.paymentId).exists(_.tags.contains("request_rejected")) =>
        ctx.commandFailed(PaymentStatusExists(s"Request Rejected [${command.paymentId}] already exist in this entity"))
        ctx.done
      case (RejectPaymentRequestCommand(command), ctx, _) =>
        ctx.thenPersist(CommandSubmitted(command)) { _ =>
          ctx.reply(Done)
        }
    }
    .onCommand[RejectPaymentCommand, Done] {
      case (RejectPaymentCommand(command), ctx, state)
        if state.payments.approved(command.paymentId).isEmpty =>
        ctx.invalidCommand(s"Approved Payment not found ${command.paymentId}")
        ctx.done
      case (RejectPaymentRequestCommand(command), ctx, state)
        if state.payments.approved(command.paymentId).exists(_.tags.contains("payment_rejected")) =>
        ctx.commandFailed(PaymentStatusExists(s"Payment Rejected [${command.paymentId}] already exist in this entity"))
        ctx.done
      case (RejectPaymentCommand(command), ctx, _) =>
        ctx.thenPersist(CommandSubmitted(command)) { _ =>
          ctx.reply(Done)
        }
    }

  private def reversePaymentTransaction = Actions()
    .onCommand[ReversePaymentCommand, Done] {
    case (ReversePaymentCommand(command), ctx, state)
      if state.payments.approved(command.paymentId).isEmpty =>
      ctx.invalidCommand(s"Approved Payment not found ${command.paymentId}")
      ctx.done
    case (ReversePaymentCommand(command), ctx, state)
      if state.payments.approved(command.paymentId).exists(_.tags.contains("payment_reversed_transaction")) =>
      ctx.commandFailed(PaymentStatusExists(s"Payment Reversal [${command.paymentId}] already exist in this entity"))
      ctx.done
    case (ReversePaymentCommand(command), ctx, _) =>
      ctx.thenPersist(CommandSubmitted(command)) { _ =>
        ctx.reply(Done)
      }
  }

  private def paymentActions = Actions()
    .onReadOnlyCommand[QueryApprovedPayment, Option[ApprovedPayment]] {
      case (QueryApprovedPayment(id, tags), ctx, state) => ctx.reply(state.payments.approved(id, tags).map(_.approvedPayment))
    }
    .onReadOnlyCommand[QueryExistingCommands.type, ExistingCommandsReply] {
      case (QueryExistingCommands, ctx, state) => ctx.reply(ExistingCommandsReply(state.statuses.commands))
    }
    .orElse(approvePayments.orElse(acceptPayments.orElse(acceptPaymentRequests.orElse(verifyPayments.orElse(rejectPayments.orElse(acceptPaymentsToParty))))))
    .orElse(actionPayments)
    .orElse(demandPayments)
    .orElse(reversePayments)
    .orElse(reversePaymentTransaction)
    .orElse(easyPay)
    .orElse(requestProofOfPayment)

  private def existingWalletTransfers = Actions()
    .onCommand[TransferAssetsCommand, Done] {
      case (command: TransferAssetsCommand, ctx, WalletState(height, _, _, _, _)) if invalidHeight(command.height, height) =>
        ctx.commandFailed(UnavailableWallet("Command height invalid state has newer version"))
        ctx.done
      case (TransferAssetsCommand(_, command), ctx, _) if command.transfers.isEmpty =>
        ctx.invalidCommand("Cannot transfer nothing")
        ctx.done
      case (TransferAssetsCommand(_, command), ctx, _) =>
        ctx.thenPersist(CommandSubmitted(command)) { _ =>
          ctx.reply(Done)
        }
    }
    .onEvent {
      case (_: TransferCreated, state) => state
    }


  private def existingWallet = Actions()
    .onCommand[CreateWalletCommand, Done] {
      case (_, ctx, _) =>
        ctx.commandFailed(WalletAlreadyCreated())
        ctx.done
    }
    .onReadOnlyCommand[QueryAccountInfo.type, AccountInfoReply] {
      case (_, ctx, state) => ctx.reply(AccountInfoReply(state.walletDepth, state.statuses.commands, state.stateRecord))
    }
    .onCommand[AddReceiptCommand, Done] {
      case (AddReceiptCommand(addReceipt @ AddReceipt(_, _, _, _, _, _:Receipt.AllocatedExisting | _: Receipt.BadReference|_: Receipt.BadReferenceAgencyNotActive | _: Receipt.DepositInterestAccrued, _, _)), ctx, _) =>
        ctx.thenPersist(CommandSubmitted(addReceipt)) { _ =>
          ctx.reply(Done)
        }
      case (AddReceiptCommand(AddReceipt(_, _, _, _, _, receipt, _, _)), ctx, _) =>
        ctx.commandFailed(InvalidWallet(s"Cannot use receipt of [${receipt.getClass.getSimpleName}] using a existing wallet"))
        ctx.done
    }
    .orElse(existingWalletTransfers)

  private def easyPay = Actions()
    .onCommand[EasyPayPaymentSuccess, Done] {
      case (EasyPayPaymentSuccess(paymentId, easyPayRequestId, succeededAt), ctx, state) =>
        state.payments.approved(paymentId) match {
          case Some(tagged)
            if tagged.tags.contains("easypay_success") =>
            ctx.commandFailed(PaymentStatusExists(s"EasyPay Payment [$paymentId] already exists in this entity"))
            ctx.done
          case Some(tagged) =>
            ctx.thenPersist(EasyPayPaymentSucceeded(tagged, easyPayRequestId, EventRecord(None, succeededAt))) { _ =>
              ctx.reply(Done)
            }
          case None =>
            ctx.invalidCommand(s"Approved Payment not found $paymentId")
            ctx.done
        }
    }
    .onEvent {
      case (event: EasyPayPaymentSucceeded, state) =>
        state.copy(payments = state.payments.appendEasyPayPayment(Seq(event.payment.approvedPayment)))
    }

  // scalastyle:off
  private def transactions: Actions = Actions()
    .onCommand[UpdateTransactionStatus, Done] {
      case (command: UpdateTransactionStatus, ctx, WalletState(_, _, collection, payments, _)) =>
        val events = collection.transactionMatch(command) {
          case CreateWallet(transaction, user, agency, privateKey) if command.committed =>
            val record = EventRecord(user.map(x => implicitly[String](x)))
            Seq(AccountCreated(privateKey, transaction, agency, record))
          case AddReceipt(transaction, uid, reference, asset, cashDepositFee, receipt, hint, remittance) if command.committed =>
            Seq(AssetAddedWithReference(asset, cashDepositFee, uid, reference, transaction, Some(receipt), hint,
              EventRecord(None, remittance.map(_.getMillis()).getOrElse(DateUtcUtil.now().getMillis))))
          case ApprovePayments(transaction, pmnts, _) =>
            if (command.committed) Seq(PaymentsApproved(Some(transaction), pmnts)) else Seq(PaymentsFailed(transaction, pmnts, command.status, DateUtcUtil.now()))
          case RejectPaymentRequest(transaction, paymentId, errors) =>
            Seq(PaymentRequestRejected(transaction, payments.approved(paymentId).map(_.approvedPayment).get, errors, Some(EventRecord(Option.empty))))
          case RejectPayment(transaction, paymentId, qualifier, error) =>
            Seq(PaymentRejected(transaction, payments.approved(paymentId).map(_.approvedPayment).get, qualifier, error, Some(EventRecord(Option.empty))))
          case ReversePaymentTransaction(transaction, paymentId) =>
            val taggedPayment = payments.approved(paymentId).get
            Seq(PaymentReversedTransaction(transaction, taggedPayment.approvedPayment, taggedPayment.reversedRecord))
          case TransferAssets(transaction, fromWallet, transfers, user, agency, createdAt, _) if command.committed =>
            val record = EventRecord(user.map(implicitly[String](_)), createdAt.getOrElse(System.currentTimeMillis()))
            transfers.map(t => TransferCreated(t.asset, fromWallet, t.toWallet, transaction, agency, t.description, record))
        }
        if (events.nonEmpty) {
          ctx.thenPersistAll(events: _*)(() => ctx.reply(Done))
        } else {
          // No events to persist, reply and persist nothing
          ctx.reply(Done)
          ctx.done
        }
      }
    .onEvent {
      case (_: AssetAddedWithReference, state) => state
      case (event: TransactionStatusUpdated, state) =>
        state.copy(
          statuses = state.statuses.removeFromCollection(event)
        )
      case (CommandSubmitted(command @ ApprovePayments(_, _, Some(retryFrom))), state) =>
        val cleanedCommands = state.statuses.commands.filterNot(_.transaction == retryFrom)
        state.copy(
          statuses = state.statuses.copy(cleanedCommands :+ command),
          payments = state.payments.appendCommitted(command.payments)
        )
      case (CommandSubmitted(command: ApprovePayments), state) =>
        state.copy(
          statuses = state.statuses.copy(state.statuses.commands :+ command),
          payments = state.payments.appendCommitted(command.payments)
        )
      case (CommandSubmitted(command @ TransferAssets(_, _, _, _, _, _, Some(retryFrom))), state) =>
        val cleanedCommands = state.statuses.commands.filterNot(_.transaction == retryFrom)
        state.copy(
          statuses = state.statuses.copy(cleanedCommands :+ command)
        )
      case (event: CommandSubmitted, state) =>
        state.copy(
          statuses = state.statuses.copy(state.statuses.commands :+ event.stateCommand)
        )
      case (event: PaymentsApproved, state) =>
        state.copy(payments = state.payments.appendApproved(event.payments))
      case (event: PaymentRequestRejected, state) =>
        state.copy(payments = state.payments.appendRequestRejected(Seq(event.payment)))
      case (event: PaymentRejected, state) =>
        state.copy(payments = state.payments.appendPaymentRejected(Seq(event.payment)))
      case (event: PaymentReversedTransaction, state) =>
        state.copy(payments = state.payments.appendPaymentReversedTransaction(Seq(event.payment)))
      case (_: PaymentsFailed, state) =>
        state
    }
  // scalastyle:on

  private def catchAllActions = Actions()
    .onReadOnlyCommand[CreateWalletCommand, Done] {
      case (_, ctx, _) => ctx.invalidCommand("Cannot create wallet with tx using this entity")
    }
    .onReadOnlyCommand[AddReceiptCommand, Done] {
      case (_, ctx, _) => ctx.invalidCommand("Cannot add asset with ref using this wallet entity")
    }
    .onReadOnlyCommand[ApprovePaymentsCommand, Done] {
      case (_, ctx, _) => ctx.invalidCommand("Cannot approve payments with this wallet entity")
    }
    .onReadOnlyCommand[TransferAssetsCommand, Done] {
      case (_, ctx, _) => ctx.invalidCommand("Cannot transfer asset using this wallet entity")
    }
    .onReadOnlyCommand[SignTransaction, SignedTransaction] {
      case (_, ctx, _) => ctx.invalidCommand("Cannot sign transaction using this wallet entity")
    }
    .onReadOnlyCommand[QueryAccountInfo.type, AccountInfoReply] {
      case (_, ctx, _) => ctx.invalidCommand("Cannot query this wallet entity")
    }
    .orElse(transactions)
    .orElse(depositFee)

  private def adminTransactionSigning = Actions()
    .onReadOnlyCommand[SignTransaction, SignedTransaction] {
      case (SignTransaction(walletTransaction), ctx, _) =>
        val keypair = irohaSettings.irohaKeypair
        val rawSignature = new Ed25519Sha3().rawSign(HexBytesUtil.hex2bytes(walletTransaction.txHash), keypair)
        ctx.reply(SignedTransaction(walletTransaction, Utils.toHex(keypair.getPublic.getEncoded), Utils.toHex(rawSignature)))
    }

  private def walletSigning = Actions()
    .onReadOnlyCommand[SignTransaction, SignedTransaction] {
      case (SignTransaction(walletTransaction), ctx, WalletState(_, Some(pk), _, _, _)) =>
        val keypair = Utils.parseHexKeypair(pk.publicKeyHex, pk.privateKeyHex)
        val rawSignature = new Ed25519Sha3().rawSign(HexBytesUtil.hex2bytes(walletTransaction.txHash), keypair)
        ctx.reply(SignedTransaction(walletTransaction, Utils.toHex(keypair.getPublic.getEncoded), Utils.toHex(rawSignature)))
    }

  private def depositFee = Actions()
    .onCommand[UpdateDepositFee, Done] {
      case (command: UpdateDepositFee, ctx, _) =>
        import command._
        ctx.thenPersist(DepositFeeUpdated(transaction, receipt, receiptTimestamp, uid, reference, cashDepositFee, chargeLevied, eventRecord))(_ => ctx.reply(Done))
    }
    .onEvent {
      case (_: DepositFeeUpdated, state) => state
    }

  private def isAdminAccount = entityId == irohaSettings.irohaAdmin.toIrohaString
  private def isIncomingEft = entityId == IrohaIncomingEFT.walletRef.toIrohaString
  private def isIncomingDeposit = entityId == IrohaIncomingDeposit.walletRef.toIrohaString
  private def isDepositFloat = entityId == IrohaDepositFloat.walletRef.toIrohaString
  private def isIncomingInterest = entityId == IrohaDepositInterest.walletRef.toIrohaString
  private def isOutgoingEft = entityId == IrohaOutgoingEFT.walletRef.toIrohaString
  private def isOutgoingDeposit = entityId == IrohaOutgoingDeposit.walletRef.toIrohaString
  private def isBadReference = entityId == IrohaBadReference.walletRef.toIrohaString
  private def isOutgoingEasypay = entityId == IrohaOutgoingEasypay.walletRef.toIrohaString

  // scalastyle:off
  override def behavior: Behavior = {
    case _ if isIncomingEft => adminTransactionSigning.orElse(catchAllActions)
    case _ if isIncomingDeposit => adminTransactionSigning.orElse(catchAllActions)
    /**
      * @todo we shouldn't allow some of these wallets to do the things defined in 'existingWallet'
      */
    case _ if isOutgoingEft => adminTransactionSigning.orElse(existingWallet.orElse(catchAllActions))
    case _ if isOutgoingDeposit => adminTransactionSigning.orElse(existingWallet.orElse(catchAllActions))
    case _ if isDepositFloat => adminTransactionSigning.orElse(catchAllActions)
    case _ if isIncomingInterest => adminTransactionSigning.orElse(existingWallet.orElse(catchAllActions))
    case _ if isBadReference => adminTransactionSigning.orElse(existingWallet.orElse(catchAllActions))
    case _ if isAdminAccount => adminPhx.orElse(adminTransactionSigning.orElse(catchAllActions))
    case _ if isOutgoingEasypay => adminTransactionSigning.orElse(existingWallet.orElse(catchAllActions))
    case WalletState(_, _, _, _, EmptyState()) => newWallet.orElse(catchAllActions)
    case WalletState(_, Some(_), _, _, NonEmptyState()) => existingWallet.orElse(paymentActions.orElse(walletSigning.orElse(catchAllActions)))
    case _ => catchAllActions
  }
}

case class InvalidWallet(message: String) extends IllegalArgumentException(message) with NoStackTrace
object InvalidWallet {
  implicit val format: Format[InvalidWallet] = Json.format
}

case class UnavailableWallet(message: String) extends IllegalArgumentException(message) with NoStackTrace
object UnavailableWallet {
  implicit val format: Format[UnavailableWallet] = Json.format
}

case class WalletDoesNotExist(message: String) extends IllegalArgumentException(message) with NoStackTrace
object WalletDoesNotExist {
  implicit val format: Format[WalletDoesNotExist] = Json.format
}

case class WalletAlreadyCreated() extends IllegalArgumentException("Wallet already exists") with NoStackTrace
object WalletAlreadyCreated {
  implicit val format: Format[WalletAlreadyCreated] = JsonFormats.singletonFormat(WalletAlreadyCreated())
}

case class PaymentStatusExists(message: String) extends IllegalArgumentException(message) with NoStackTrace
object PaymentStatusExists {
  implicit val format: Format[PaymentStatusExists] = Json.format
}
