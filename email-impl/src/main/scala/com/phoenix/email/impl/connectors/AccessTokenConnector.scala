package com.phoenix.email.impl.connectors

import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.phoenix.authentication.api.{AccessTokenChecker, InvalidAccessToken, TokenStatus, ValidAccessToken}
import com.phoenix.persistence.QuillHelper
import io.getquill.{CassandraLagomAsyncContext, SnakeCase}

import scala.concurrent.{ExecutionContext, Future, Promise}

class AccessTokenConnector(session: CassandraSession)(implicit ec: ExecutionContext) extends AccessTokenChecker {

  private val ctx = new CassandraLagomAsyncContext(SnakeCase, session)
  import ctx._
  import QuillHelper.Implicits._

  private val accessTokens = quote {
    querySchema[AccessToken]("access_tokens")
  }

  def insertAccessToken(accessToken: AccessToken): Future[AccessToken] = {
    val q = quote { accessTokens.insert(lift(accessToken)) }
    ctx.run(q).map(_ => accessToken)
  }

  def fetchTokenStatus(token: String): Future[TokenStatus] = {
    val future = if (token.length == 64) findAccessTokenStatement(token) else Future(Option.empty[AccessToken])
    future.map {
      case None => InvalidAccessToken("Token not correct")
      case Some(_) => ValidAccessToken(Seq.empty)
    }
  }

  def findAccessTokenStatement(token: String): Future[Option[AccessToken]] = {
    val q = quote { accessTokens.filter(_.accesstkn == lift(token)) }
    ctx.run(q).map(_.headOption)
  }
}

case class AccessToken(accesstkn: String, owner: String)
