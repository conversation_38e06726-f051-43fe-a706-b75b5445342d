package com.phoenix.email.impl

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import com.phoenix.date.DateUtcUtil
import com.phoenix.json.JsonFormats
import com.phoenix.util.UUID
import org.joda.time.{DateTime, Hours}
import play.api.libs.json.{Format, Json}

import scala.util.control.NoStackTrace

class EmailConversationEntity extends PersistentEntity {
  override type Command = EmailConversationCommand[_]
  override type Event = EmailConversationEvent
  override type State = EmailConversation

  override def initialState: State = EmailConversation(List.empty, Option.empty[EmailMessage], Option.empty[DateTime], Seq.empty[SendgridWebhookEvent])

  private def emailConversation = Actions()
    .onReadOnlyCommand[GetConversations, EmailConversationsReply] {
      case (GetConversations(limit), ctx, EmailConversation(messages, _, _, _)) => ctx.reply(EmailConversationsReply(messages.reverse.take(limit)))
    }
    .onReadOnlyCommand[GetEmailMessage, Option[EmailMessage]] {
      case (GetEmailMessage(message), ctx, EmailConversation(messages, _, _, _)) => ctx.reply(messages.find(_.id == message))
    }

  private def submitEmails = Actions()
    .onCommand[SubmitEmail, Done] {
      case(SubmitEmail(id, messageDescriptor), ctx, EmailConversation(messages, _, _, _)) =>
        messages.find(p => p.id == id) match {
          case Some(message) => ctx.thenPersist(EmailSubmitted(id, message.agency, messageDescriptor))(_ => ctx.reply(Done))
          case None =>
            ctx.invalidCommand("Invalid message Id")
            ctx.done
        }
    }
    .onEvent {
      case (EmailSubmitted(id, _, msgDescriptor), state) =>
        state.copy(messages = state.messages.map({
          case f if f.id == id => f.copy(messageDescriptor = Some(msgDescriptor))
          case f => f
        }))
    }

  private def isNudgeCoolDown(state: State, requestedNudge: DateTime) = state.lastNudge.exists { lastNudge =>
    Hours.hours(24).isGreaterThan(Hours.hoursBetween(lastNudge, requestedNudge))
  }

  private def sendEmails = Actions()
    .onCommand[SendEmail, EmailMessage] {
      case(SendEmail(email: EmailType.TenantNudged, _), ctx, state)
        if isNudgeCoolDown(state, email.nudgedAt) =>
        ctx.commandFailed(DuplicateEmailEvent(entityId, s"Cannot nudge as cool down is in effect"))
        ctx.done
      case(SendEmail(email, _), ctx, state)
        if state.messages.exists(_.emailType == email) =>
        ctx.commandFailed(DuplicateEmailEvent(entityId, s"EmailType already exists: $email"))
        ctx.done
      case(SendEmail(email, details), ctx, _) =>
        val timestamp = DateUtcUtil.now().getMillis
        val message = EmailMessage(UUID.createV4, details.agency, Some(details.from), details.replyTo, details.template,
                                   details.data, timestamp, email, details.attachments, ccContacts = Some(details.cc))
        ctx.thenPersist(EmailCreated(message, Option.empty, Option.empty))(_ => ctx.reply(message))
    }
    .onEvent {
      case (EmailCreated(emailMessage, _, _), state @ EmailConversation(messages, _, ln, _)) =>
        val lastNudged = emailMessage.emailType match {
          case et: EmailType.TenantNudged => Some(et.nudgedAt)
          case _ => ln
        }
        state.copy(messages = messages ++ List(emailMessage), latestMessage = Some(emailMessage), lastNudge = lastNudged)
    }

  private def resendEmails = Actions()
    .onCommand[ResendEmailMessage, Done] {
      case(ResendEmailMessage(emailMessage, userId), ctx, state) =>
        val timestamp = DateUtcUtil.now().getMillis
        val modifiedMessage = emailMessage.copy(ccContacts = Option.empty, id = UUID.createV4, timestamp = timestamp)
        ctx.thenPersist(EmailCreated(modifiedMessage, Option(emailMessage.id), Option(userId)))(_ => ctx.reply(Done))
    }

  private def failedSubmitEmails = Actions()
    .onCommand[SubmitEmailFailed, Done] {
    case(SubmitEmailFailed(id, reason), ctx, EmailConversation(messages, _, _, _)) =>
      messages.find(p => p.id == id) match {
        case Some(message) => ctx.thenPersist(EmailSubmitFailed(id, message.agency, reason))(_ => ctx.reply(Done))
        case None =>
          ctx.invalidCommand("Invalid message Id")
          ctx.done
      }
  }
    .onEvent {
      case (_: EmailSubmitFailed, state) => state
    }

  private def sendgridWebhook = Actions()
    .onCommand[HandleSendgridWebhook, Done] {
      case (HandleSendgridWebhook(event, currentTime), ctx, EmailConversation(messages, _, _, events)) =>
        val isOld = Hours.hoursBetween(event.timestamp, DateUtcUtil.now().withMillis(currentTime))
          .isGreaterThan(EmailConversationEntity.MAX_MESSAGE_AGE)
        if (isOld) {
          ctx.commandFailed(EmailConversationEntity.EventExceedsMaxAge())
          ctx.done
        } else if (events.exists(_.sendgridEventId == event.sendgridEventId)) {
          ctx.commandFailed(EmailConversationEntity.DuplicateWebhookEvent())
          ctx.done
        } else {
          val message = messages.find(_.messageDescriptor
            .collect({ case x: SendgridDescriptor => x })
            .exists(_.sgMessageId == event.sendgridMessageIdSplit))
          message match {
            case Some(value) =>
              ctx.thenPersist(event.copy(agencyId = value.agency, messageId = Option(value.id)))(_ => ctx.reply(Done))
            case None =>
              ctx.reply(Done)
              ctx.done
          }
        }
    }
    .onEvent {
      case (event: SendgridWebhookEvent, state) =>
        val events = state.sendgridEvents
          .filterNot(p => Hours.hoursBetween(p.timestamp, event.timestamp).isGreaterThan(EmailConversationEntity.MAX_MESSAGE_AGE))
        state.copy(sendgridEvents = Seq(event.reduceEvent()) ++ events)
    }

  override def behavior: Behavior = emailConversation.orElse(submitEmails.orElse(sendEmails.orElse(failedSubmitEmails).orElse(sendgridWebhook))).orElse(resendEmails)
}

object EmailConversationEntity {
  val MAX_MESSAGE_AGE: Hours = Hours.hours(1440) // 2 months

  case class DuplicateWebhookEvent() extends IllegalArgumentException("Duplicate Webhook Event") with NoStackTrace
  object DuplicateWebhookEvent {
    implicit val format: Format[DuplicateWebhookEvent] = JsonFormats.singletonFormat(DuplicateWebhookEvent())
  }

  case class EventExceedsMaxAge() extends IllegalArgumentException("Event Exceeds Max Age") with NoStackTrace
  object EventExceedsMaxAge {
    implicit val format: Format[EventExceedsMaxAge] = JsonFormats.singletonFormat(EventExceedsMaxAge())
  }
}

case class DuplicateEmailEvent(entity: String, message: String) extends IllegalArgumentException(s"[$entity] $message") with NoStackTrace
object DuplicateEmailEvent {
  implicit val format: Format[DuplicateEmailEvent] = Json.format
}
