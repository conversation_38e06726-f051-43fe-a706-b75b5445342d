package com.phoenix.email.impl.listeners

import akka.Done
import akka.stream.scaladsl.Flow
import com.phoenix.accounting.api.AccountingService
import com.phoenix.authentication.jwt.{AgencyMembership, AuthenticationService, UserSession}
import com.phoenix.date.DateUtcUtil
import com.phoenix.email.api.PdfEmailAttachment
import com.phoenix.email.impl.connectors._
import com.phoenix.email.impl.util.{ContactResolver, EmailContact, Mailer}
import com.phoenix.email.impl.views._
import com.phoenix.email.impl.{DuplicateEmailEvent, EmailType, InvoiceBatchImpl}
import com.phoenix.invoice.api.InvoiceType
import com.phoenix.logging.EventLogging
import com.phoenix.party.api.{AccountType, PartyTag}
import com.phoenix.util.UUID
import com.phoenix.util.UUID.toUUID
import com.phoenix.wallet.api._
import com.typesafe.scalalogging.LazyLogging
import io.lemonlabs.uri.RelativeUrl
import org.joda.time.DateTime
import play.api.libs.json._

import scala.concurrent.{ExecutionContext, Future}
import scala.language.implicitConversions
import java.util.{UUID => JavaUUID}

class WalletEventListener(paymentConnector: PaymentConnector,
                          partyConnector: PartyConnector,
                          invoiceConnector: InvoiceConnector,
                          userConnector: UserConnector,
                          accountingService: AccountingService,
                          batchImpl: InvoiceBatchImpl,
                          auth: AuthenticationService,
                          partyAccountConnector: PartyAccountConnector,
                          propertyConnector: PropertyConnector,
                          walletService: WalletService,
                          portfolioConnector: PortfolioConnector,
                          mailer: Mailer,
                          accountingBatchConnector: AccountingBatchConnector)
                         (implicit executionContext: ExecutionContext)
  extends EventLogging
    with LazyLogging {

  import Mailer.Implicits._
  import WalletEventListener._

  private def invoiceUri(id: String) = RelativeUrl.parse("/invoices/open").addPathPart(id)

  private def generateJwt(agencyId: UUID) = {
    auth.generateToken(UserSession("", Some(AgencyMembership.ReadOnlyMember(agencyId)), "", "", Some("email"), "", None, Some(true))).authToken
  }

  private val testAgencies = Seq(
    "558ac286-7838-41a8-94ea-8bf41268bb87",
    "481e9492-20fb-43dd-a248-fbafdde139ef",
    "4f23aad2-7775-4af5-8fef-62f4372faa5f",
    "c8c21026-9c89-4e7b-9475-65a45f96a9ef",
    "7a36a92c-0254-494b-baa5-d93418672a1c",
    "56dfbd51-14b5-4c8b-84ca-88058fbbe662"
  )

  walletService.walletEvents
    .subscribe
    .withGroupId("emailWalletListener0.53")
    .atLeastOnce(
      Flow[WalletEvent].mapAsync(1)(logEventAsync)
        .mapAsync(1) {
          case PaymentRejected(payment, Some(agency), toParty, fromParty, transaction, qualifier, error, reference, invoice, _, _, amount, Some(approvedBy), approvedAt) =>
            paymentConnector.find(payment).flatMap {
              case paymentRow if !paymentRow.flatMap(_.email).contains("rejection") =>
                for {
                  //                  agent <- partyConnector.find(agency, approvedBy).map {
                  //                    case None => throw new Exception(s"Agent not found $approvedBy")
                  //                    case Some(x) => x
                  //                  }
                  // could be an approved payment by a support user
                  agent <- userConnector.getUserById(approvedBy).map {
                    case None => throw new Exception(s"User not found $approvedBy")
                    case Some(x) => Party(agency, approvedBy, false, None, x.firstName, x.lastName, x.email, None, None, None, None, None, None, None, None, None, None, None)
                  }

                  sendTo = if (devopsQualifiers.contains(qualifier.toUpperCase)) {
                    EmailContact(devopsEmail, None)
                  } else {
                    EmailContact(agent.email, Some(agent.contactFirstName))
                  }

                  invoice <- invoice.map(id => invoiceConnector.findInvoice(id.uuid)).getOrElse(Future(Option.empty[OpenedInvoice]))

                  property <- invoice.flatMap(_.portfolioId).map(portfolioId => propertyConnector.find(agency, portfolioId)).getOrElse(Future(Seq.empty[PropertyAddress]))

                  replyContact <- ContactResolver.replyToContact(partyConnector, agency, Some(agent))

                  data = Json.toJson(PaymentRejectionView(
                    TransactionView.build(DateUtcUtil.now().withMillis(approvedAt), amount, transaction, reference, Some(s"$qualifier: $error")),
                    PartyView.build(agent),
                    property.headOption.map(p => PropertyView(p.address))
                  )).as[JsObject]

                  _ <- mailer.send(EmailType.SystemPaymentRejection(transaction), "system.payment-rejection", agency = None)
                    .to(sendTo)
                    .replyTo(replyContact)
                    .data(data)
                    .recover({
                      case ex: DuplicateEmailEvent =>
                        logger.warn(s"[WEL] DuplicateEmailEvent ${ex.message}")
                        Done
                    })

                  _ <- paymentConnector.updateRejection(payment)
                } yield Done
              case _ => Future(Done)
            }
          case event: AssetTransferred if sourceWallets.contains(event.fromAccount) && event.toParty.flatMap(_.party).isDefined =>
            val agencyId = event.toParty.flatMap(_.agency).get
            val partyId = event.toParty.flatMap(_.party).get
            val accountId = event.toParty.flatMap(_.account).get
            val timestamp = DateUtcUtil.now().withMillis(event.timestamp)
            val assetBalance = event.toBalances.flatMap(p => p.find(_.name == "zar#phoenix.coin") orElse p.find(_.name == "zar_dummy#phoenix.coin"))
            for {
              tenant <- partyConnector.find(agencyId, partyId).map(_.get)

              recentInvoices <- invoiceConnector.findRecent(partyId, accountId, timestamp.minusMonths(1).getYear, timestamp.minusMonths(1).getMonthOfYear)

              lastInvoice = recentInvoices.headOption

              portfolio <- lastInvoice.flatMap(_.portfolioId).map(pid => portfolioConnector.find(agencyId, pid)) getOrElse Future(Option.empty[Portfolio])

              agentId = portfolio.flatMap(_.primaryAgentId) getOrElse agencyId.uuid

              agent <- partyConnector.find(agencyId, agentId) // .map(_.get)

              primary <- agent match {
                case None => partyConnector.find(agencyId, agencyId).map(_.get)
                case Some(a) => Future(a)
              }

              replyContact <- ContactResolver.replyToContact(partyConnector, agencyId, Some(primary))

              sendToTenant = EmailContact(tenant.email, Some(tenant.contactFirstName))
              sendToAgent = EmailContact(primary.email, Some(primary.contactFirstName))

              tenantView = JsObject(Seq(
                "property" -> JsObject(Seq(
                  "address" -> (portfolio.map(_.address).map(JsString) getOrElse JsNull))),
                "tenant" -> Json.toJson(PartyView.build(tenant)),
                "agent" -> Json.toJson(PartyView.build(primary)),
                "transaction" -> Json.toJson(TransactionView.build(timestamp, event.amount, event.transaction, Some(event.description))),
                "account" -> assetBalance.map(asset => Json.toJson(AccountView.build(asset))).getOrElse(JsNull),
                "invoice" -> lastInvoice.map(_.amount)
                  .map(amount => Json.toJson(MonthlyInvoiceView.Invoice.build(amount)))
                  .getOrElse(JsNull)
              ))

              recentInvoiceItems = recentInvoices.map { invoice =>
                Json.toJson(MonthlyInvoiceView.InvoiceItem.build(invoice.amount, invoice.number, InvoiceType(invoice.invoiceType).name,
                  mailer.link(invoiceUri(invoice.invoiceId.toString))))
              }

              agentView = JsObject(Seq(
                "tenant" -> JsObject(Seq("fullName" -> JsString(tenant.fullName))),
                "property" -> JsObject(Seq(
                  "address" -> portfolio.map(_.address).map(JsString).getOrElse(JsNull))),
                "agent" -> Json.toJson(PartyView.build(primary)),
                "invoice" -> JsObject(Seq("invoices" -> JsArray(recentInvoiceItems)))
              ))

              // _ <- if (recentInvoices.nonEmpty) {
              //   mailer.send(EmailType.AgentUnreconciledPayment(event.transaction), "agent.unreconciled-payment-notification", Some(agencyId))
              //     .to(sendToAgent)
              //     .replyTo(ContactResolver.noReply)
              //     .data(agentView)
              //     .recover({
              //       case ex: DuplicateEmailEvent =>
              //         logger.warn(s"[WEL] DuplicateEmailEvent ${ex.message}")
              //         Done
              //     })
              // } else {
              //   Future(Done)
              // }

              _ <- if (tenant.tagSplit.contains(PartyTag.Tenant.getObjectName)) {
                mailer.send(EmailType.TenantPaymentReceived(event.transaction), "tenant.payment-received", Some(agencyId))
                  .to(sendToTenant)
                  .replyTo(replyContact)
                  .data(tenantView)
                  .recover({
                    case ex: DuplicateEmailEvent =>
                      logger.warn(s"[WEL] DuplicateEmailEvent ${ex.message}")
                      Done
                  })
              } else {
                Future(Done)
              }
            } yield Done
          case PaymentAccepted(_, Some(fromPartyAccount),
          AcceptedPayment(payment, _, Some(PartyAccount(_, Some(toParty), _, _, _, _, _)), asset, amount, precision, paymentKind: PaymentKind.PartyPayment, _, approvedAt, _, agency), transaction) =>
          logger.debug("PaymentAccepted in emailWalletListener0.53: {}", payment)
            if (PartyTag(paymentKind.partyTag) == PartyTag.Owner && agency.isDefined && testAgencies.contains(agency.get.uuid.toString)) {
              logger.debug("PaymentAccepted in emailWalletListener0.53 for test agency")
              val approvedAtTimestamp = new DateTime(approvedAt)
              val pe = PaymentEmail(payment, agency.map(_.uuid), Some(toParty), Some(transaction), Some(paymentKind.reference),
              Some(amount), Some(precision), Some(asset), Option.empty[String], Some(approvedAtTimestamp), fromPartyAccount.party.map(_.uuid))
              for {
                _ <- paymentConnector.insert(pe)
                _ = logger.debug("Payment Inserted: {}", pe)
                tenantId = fromPartyAccount.party.map(_.uuid).getOrElse(throw new Exception("fromPartyAccount partyId not found"))
                leaseId = fromPartyAccount.leaseId.getOrElse(throw new Exception("LeaseId not found"))
                _ = logger.debug("LeaseId: {}", leaseId)
                _ = logger.debug("TenantId: {}", tenantId)
                _ <- sendPaymentProcessing(leaseId, agency.get, toParty, transaction, Some(paymentKind.reference), amount, tenantId, approvedAtTimestamp, fromPartyAccount)
              } yield Done
            }
            Future.successful(Done)
          case _ => Future.successful(Done)
        }).recover({ case x =>
      logger.error("WalletEventListener failure: {}", x)
      throw x
    })

  //scalastyle:off
  private def sendPaymentProcessing(portfolioId: String,
                                    agencyId: JavaUUID,
                                    toPartyId: JavaUUID,
                                    tx: String,
                                    ref: Option[String],
                                    amount: BigDecimal,
                                    tenantId: JavaUUID,
                                    timestamp: DateTime,
                                    fromPartyAccount: PartyAccount): Future[Done] = {
    logger.debug("sendPaymentProcessing in emailWalletListener0.53")
    for {
      recipient <- partyConnector.find(agencyId, toPartyId).map(_.getOrElse(throw new Exception(s"Recipient not found $toPartyId")))
      agent <- partyConnector.find(agencyId, agencyId)
      tenant <- partyConnector.find(agencyId, tenantId)
      sendTo = EmailContact(recipient.email, Some(recipient.contactFirstName))
      now = DateUtcUtil.now()

      recentInvoices: Seq[RecentInvoice] <- (tenantId, fromPartyAccount, now) match {
        case (t, PartyAccount(Some(accountId), _, _, _, _, _, _), d) =>
          invoiceConnector.findRecent(t, accountId, d.minusMonths(1).getYear, d.minusMonths(1).getMonthOfYear)
        case _ =>
          Future(Seq.empty)
      }
      _ = logger.debug("Recent Invoices: " + recentInvoices)
      miniStatement <- accountingService.getLeaseStatementJson(portfolioId, Some(timestamp.minusMonths(1).toString), Some(timestamp.toString))
        .handleRequestHeader(auth.authenticatedWith(generateJwt(agencyId)))
        .invoke()

      attachment = PdfEmailAttachment(s"mini_statement.pdf", "attachments.invoice", Some(miniStatement.context))
      _  = logger.debug("Mini statement: " + miniStatement)

      portfolio <- portfolioConnector.find(agencyId, UUID.fromString(portfolioId))
      view = createView(timestamp, tx, ref, amount, agent, tenant, recentInvoices, portfolio)
      replyContact <- ContactResolver.replyToContact(partyConnector, agencyId, agent)
      _ = logger.debug("Portfolio: " + portfolio)
      _ = logger.debug("View: " + view)
      _ = logger.debug("Reply Contact: " + replyContact)
      _ <- if (recipient.tagSplit.contains(PartyTag.Owner.getObjectName)) {
        mailer.send(EmailType.OwnerPaymentConfirm(tx), "owner.payment-processing", Some(agencyId))
          .to(sendTo)
          .replyTo(replyContact)
          .data(view)
          .attach(attachment)
          .recover({
            case ex: DuplicateEmailEvent =>
              logger.warn(s"[WEL] DuplicateEmailEvent ${ex.message}")
              Done
          })
      } else {
        logger.error("Tenant Payment Processing SKIPPED")
        Future(Done)
      }
    } yield Done
}
  private def createView(timestamp: DateTime, tx: String, ref: Option[String], amount: BigDecimal, agent: Option[Party], tenant: Option[Party], recentInvoices: Seq[RecentInvoice], portfolio: Option[Portfolio]) = {
    JsObject(Seq(
      "tenant" -> JsObject(Seq(
        "name" -> tenant.map(_.contactFirstName).map(JsString).getOrElse(JsNull),
        "fullName" -> tenant.map(_.fullName).map(JsString).getOrElse(JsNull))),
      "invoice" -> JsObject(Seq(
        "number" -> recentInvoices.headOption.flatMap(_.number).map(_.toString).map(JsString).getOrElse(JsNull))),
      "property" -> JsObject(Seq(
        "address" -> portfolio.map(_.address).map(JsString).getOrElse(JsNull))),
      "agent" -> agent.map(party => Json.toJson(PartyView.build(party))).getOrElse(JsNull),
      "transaction" -> Json.toJson(TransactionView.build(timestamp, amount, tx, ref))
    ))
  }

  walletService.proofOfPaymentEvents
    .subscribe
    .withGroupId("emailPOPEventsListener0.53")
    .atLeastOnce(
      Flow[ProofOfPaymentEvent].mapAsync(1)(logEventAsync)
        .mapAsync(1) {
          case ProofOfPaymentsRequested(requestId, sendToEmail, subject, agency, batch) =>
          for {
            attachments <- Future.sequence(buildAttachments(batch, agency))

            sendTo = EmailContact(sendToEmail, None)
            mainView = JsObject(Seq(
              "poprequest" -> JsObject(Seq("subject" -> JsString(subject.map(s => s"Payment Notification: $s").getOrElse("Payment Notification"))))
            ))

            _ <- mailer.send(EmailType.ProofOfPayment(requestId), "system.notification-proof-of-payment", Some(agency))
            .to(sendTo)
            .replyTo(ContactResolver.noReply)
            .data(mainView)
            .attach(attachments: _*)
            .recover({
              case ex: DuplicateEmailEvent =>
                logger.warn(s"[WEL] DuplicateEmailEvent ${ex.message}")
                Done
            })

          } yield Done

          case _ => Future(Done)
        })

  // scalastyle:off
  private def buildAttachments(batch: Seq[ProofOfPayment], agency: UUID) = {
      batch.zipWithIndex.map{ case (pop, ix) =>
        for {
          party <- (pop.agencyId, pop.partyId) match {
            case (Some(agencyId), Some(partyId)) => partyConnector.find(agencyId, partyId)
            case _ => Future(Option.empty[Party])
          }

          partyAddress <- (pop.partyId, pop.accountId) match {
            case (Some(partyId), Some(accountId)) => partyAccountConnector.findByAccountId(partyId, accountId)
            case _ => Future(Option.empty[PartyPaymentReference])
          }

          paymentView = pop.bankPaymentProof match {
            case Some(details) =>
              Json.toJson(
                PaymentView.build(
                  details.toBank,
                  details.accountType.getOrElse(""),
                  details.toAccount,
                  details.accountName))
            case None => JsNull
          }

          view = JsObject(Seq(
            "party" -> Json.toJson(buildPartyJSValue(party, partyAddress)),
            "payment" -> paymentView,
            "transaction" -> Json.toJson(TransactionView.build(pop.paymentAcceptedAt, pop.amount, pop.txHash, Some(pop.paymentReference)))
          ))

          template = if (pop.easyPayPaymentProof.isDefined) "system.easypay-proof-of-payment" else "system.generic-proof-of-payment"
        } yield PdfEmailAttachment(s"payment_notification_${ix + 1}.pdf", template, Some(view))
      }
  }
  // scalastyle:on

  def buildPartyJSValue(party: Option[Party], partyAddress: Option[PartyPaymentReference]): JsValue = {
    if (party.isDefined) {
      JsObject(Seq(
        "name" -> JsString(party.get.fullName),
        "firstName" -> JsString(party.get.contactFirstName),
        "lastName" -> JsString(party.get.contactLastName),
        "address" -> partyAddress.flatMap(_.propertyAddress).map(JsString).getOrElse(JsNull),
      ))
    } else {
      JsNull
    }
  }

}

object WalletEventListener {
  val devopsQualifiers = Seq(
    "INSUFFICIENT FUNDS",
    "INSUFFICIENT FUNDS - AMOUNTS NOT CLEARED",
    "MAXIMUM NUMBER OF CREDITS PER DAY REACHED",
    "MAXIMUM NUMBER OF CREDITS PER MONTH REACHED",
    "MAXIMUM NUMBER OF DEPOSITS PER DAY REACHED",
    "MAXIMUM NUMBER OF DEPOSITS PER MONTH REACHED",
    "MAXIMUM NUMBER OF DEBIT TRANSACTIONS PER DAY REACHED",
    "MAXIMUM NUMBER OF DEBIT TRANSACTIONS PER MONTH REACHED",
    "MAXIMUM NUMBER OF WITHDRAWALS PER DAY REACHED",
    "MAXIMUM NUMBER OF WITHDRAWALS PER MONTH REACHED",
    "MAXIMUM WITHDRAWAL AMOUNT PER DAY REACHED",
    "MAXIMUM WITHDRAW AL AMOUNT PER MONTH REACHED",
    "PARAMETER SYSTEM VALIDATION ERROR",
    "ZERO TRANSACTION AMOUNT NOT VALID"
  )

  val sourceWallets = Seq("<EMAIL>", "<EMAIL>")

  val devopsEmail = "<EMAIL>"
}
