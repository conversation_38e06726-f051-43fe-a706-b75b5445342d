package com.phoenix.email.impl

import com.phoenix.date.DateTimeJsonFormatter
import com.phoenix.email.api.EmailAttachment
import com.phoenix.email.impl.util.EmailContact
import com.phoenix.email.impl.views.MonthlyInvoiceView.Invoice
import com.phoenix.util.UUID
import julienrf.json.derived
import org.joda.time.{DateTime, LocalDate}
import play.api.libs.json.{Format, JsValue, Json, __}

case class EmailMessage(id: UUID,
                        agency: Option[UUID],
                        fromContact: Option[EmailContact],
                        replyContact: Option[EmailContact],
                        template: String,
                        data: Option[JsValue],
                        timestamp: Long,
                        emailType: EmailType,
                        attachments: Option[Seq[EmailAttachment]],
                        messageDescriptor: Option[EmailMessageDescriptor] = None,
                        ccContacts: Option[Seq[EmailContact]])
object EmailMessage {
  implicit val format: Format[EmailMessage] = Json.format
}

case class EmailConversation(messages: List[EmailMessage], latestMessage: Option[EmailMessage], lastNudge: Option[DateTime],
                             sendgridEvents: Seq[SendgridWebhookEvent])
object EmailConversation {
  implicit val dateTimeFormat: Format[DateTime] = DateTimeJsonFormatter.format
  implicit val format: Format[EmailConversation] = Json.format
}

sealed trait EmailMessageDescriptor
object EmailMessageDescriptor {
  implicit val format: Format[EmailMessageDescriptor] = Json.format

  def unapply(emailMessageDescriptor: EmailMessageDescriptor): Option[(String, JsValue)] = {
    val (prod: Product, sub) = emailMessageDescriptor match {
      case b: TestDescriptor => (b, Json.toJson(b)(TestDescriptor.format))
      case b: SendgridDescriptor => (b, Json.toJson(b)(SendgridDescriptor.format))
    }
    Some(prod.productPrefix -> sub)
  }

  def apply(`class`: String, data: JsValue): EmailMessageDescriptor = {
    (`class` match {
      case "TestDescriptor" => Json.fromJson[TestDescriptor](data)(TestDescriptor.format)
      case "SendgridDescriptor" => Json.fromJson[SendgridDescriptor](data)(SendgridDescriptor.format)
    }).get
  }
}

case class TestDescriptor(faked: Boolean) extends EmailMessageDescriptor
object TestDescriptor {
  implicit val format: Format[TestDescriptor] = Json.format
}

case class SendgridDescriptor(sgMessageId: String) extends EmailMessageDescriptor
object SendgridDescriptor {
  implicit val format: Format[SendgridDescriptor] = Json.format
}

sealed trait EmailType

object EmailType {
  case class AgentLeaseActivation(portfolioId: UUID) extends EmailType
  object AgentLeaseActivation {
    implicit val format: Format[AgentLeaseActivation] = Json.format
  }

  case class AgentLeaseTermination(portfolioId: UUID) extends EmailType
  object AgentLeaseTermination {
    implicit val format: Format[AgentLeaseTermination] = Json.format
  }

  case class AgentUnreconciledPayment(txHash: String) extends EmailType
  object AgentUnreconciledPayment {
    implicit val format: Format[AgentUnreconciledPayment] = Json.format
  }

//  case class SystemWelcomeEmail(userId: UUID) extends EmailType
//  object SystemWelcomeEmail {
//    implicit val format: Format[SystemWelcomeEmail] = Json.format
//  }

  case class SystemResetPasswordConfirmation(resetPasswordLink: String) extends EmailType
  object SystemResetPasswordConfirmation {
    implicit val format: Format[SystemResetPasswordConfirmation] = Json.format
  }

  case class SystemVerifyEmail(actionurl: String, userId: UUID) extends EmailType
  object SystemVerifyEmail {
    implicit val format: Format[SystemVerifyEmail] = Json.format
  }

  case class InvitationEmail(actionurl: String, userId: UUID) extends EmailType
  object InvitationEmail {
    implicit val format: Format[InvitationEmail] = Json.format
  }

  case class BankDetailsChangedEmail(partyId: UUID, createdAt: Long, createdBy: UUID) extends EmailType
  object BankDetailsChangedEmail {
    implicit val format: Format[BankDetailsChangedEmail] = Json.format
  }

  case class SystemPaymentRejection(txHash: String) extends EmailType
  object SystemPaymentRejection {
    implicit val format: Format[SystemPaymentRejection] = Json.format
  }

  case class TenantLeaseActivation(portfolioId: UUID, partyId: UUID) extends EmailType
  object TenantLeaseActivation {
    implicit val format: Format[TenantLeaseActivation] = Json.format
  }

  case class TenantLeaseTermination(portfolioId: UUID, partyId: UUID) extends EmailType
  object TenantLeaseTermination {
    implicit val format: Format[TenantLeaseTermination] = Json.format
  }

  /**
    * @deprecated use TenantBatchInvoice instead
    */
  case class TenantMonthlyInvoice(portfolioId: UUID, invoices: Seq[UUID]) extends EmailType
  object TenantMonthlyInvoice {
    implicit val format: Format[TenantMonthlyInvoice] = Json.format
  }

  case class TenantBatchInvoice(batchId: UUID) extends EmailType
  object TenantBatchInvoice {
    implicit val format: Format[TenantBatchInvoice] = Json.format
  }

  case class FullStatementRequest(requestId: UUID) extends EmailType
  object FullStatementRequest {
    implicit val format: Format[FullStatementRequest] = Json.format
  }

  case class LeaseStatementBatch(batchId: UUID) extends EmailType
  object LeaseStatementBatch {
    implicit val format: Format[LeaseStatementBatch] = Json.format
  }

  case class TenantPaymentReceived(txHash: String) extends EmailType
  object TenantPaymentReceived {
    implicit val format: Format[TenantPaymentReceived] = Json.format
  }

  case class TenantNudged(invoiceId: UUID, partyId: UUID, accountId: Option[UUID], nudgedAt: DateTime) extends EmailType
  object TenantNudged {
    implicit val format: Format[TenantNudged] = Json.format
  }

  case class OwnerPaymentConfirm(txHash: String) extends EmailType
  object OwnerPaymentConfirm {
    implicit val format: Format[OwnerPaymentConfirm] = Json.format
  }

  case class OwnerLeaseTermination(portfolioId: UUID, partyId: UUID) extends EmailType
  object OwnerLeaseTermination {
    implicit val format: Format[OwnerLeaseTermination] = Json.format
  }

  case class ProofOfPayment(request: UUID) extends EmailType
  object ProofOfPayment {
    implicit val format: Format[ProofOfPayment] = Json.format
  }

  case class LeaseExpiryDigest(agency: UUID, stateDate: LocalDate, endDate: LocalDate) extends EmailType
  object LeaseExpiryDigest {
    implicit val format: Format[LeaseExpiryDigest] = Json.format
  }

  case class LeaseExpiredDigest(agency: UUID, date: LocalDate) extends EmailType
  object LeaseExpiredDigest {
    implicit val format: Format[LeaseExpiredDigest] = Json.format
  }

  case class LeaseRenewedDigest(agency: UUID, date: LocalDate) extends EmailType
  object LeaseRenewedDigest {
    implicit val format: Format[LeaseRenewedDigest] = Json.format
  }

  implicit val formatDate: Format[LocalDate] = DateTimeJsonFormatter.localDateFormat
  implicit val dateTimeFormat: Format[DateTime] = DateTimeJsonFormatter.format
  implicit val format: Format[EmailType] = derived.flat.oformat((__ \ "type").format[String])
}
