package com.phoenix.email.impl.entities

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import com.phoenix.date.DateUtcUtil
import com.phoenix.json.JsonFormats
import play.api.libs.json.Format

import scala.util.control.NoStackTrace

class AgencyDigestEntity extends PersistentEntity {
  override type Command = AgencyDigestCommand[_]
  override type Event = AgencyDigestEvent
  override type State = AgencyDigestState

  override def initialState: State = AgencyDigestState(Seq.empty)

  override def behavior: Behavior = Actions()
    .onCommand[SendLeaseExpiryDigest, Done] {
      case (SendLeaseExpiryDigest(toEmail, messageId, eventRecord), ctx, state) =>
        val date = DateUtcUtil.now().withMillis(eventRecord.createdAt).toLocalDate
        state.leaseExpiryChecks.find(_ == date) match {
          case Some(_) =>
            ctx.thenPersist(LeaseExpiryDigestSent(toEmail, messageId, eventRecord))(_ => ctx.reply(Done))
          case None =>
            ctx.commandFailed(DigestNotFound())
            ctx.done
        }
    }
    .onCommand[CreateLeaseExpiryDigest, LeaseExpiryReply] {
      case (CreateLeaseExpiryDigest(leaseExpiryCheck, eventRecord), ctx, state) =>
        val date = DateUtcUtil.now().withMillis(eventRecord.createdAt).toLocalDate
        state.leaseExpiryChecks.find(_ == date) match {
          case Some(_) =>
            ctx.reply(LeaseExpiryReply(canSend = false, leaseExpiryCheck))
            ctx.done
          case None =>
            ctx.thenPersist(LeaseExpiryDigestCreated(leaseExpiryCheck, eventRecord))(_ =>
              ctx.reply(LeaseExpiryReply(canSend = true, leaseExpiryCheck)))
        }
    }
    .onEvent {
      case (_: LeaseExpiryDigestSent, state) => state
      case (event: LeaseExpiryDigestCreated, state) =>
        state.copy(leaseExpiryChecks = state.leaseExpiryChecks :+ event.leaseExpiryCheck.startDate)
    }
}

case class DigestNotFound() extends IllegalArgumentException("Digest not found") with NoStackTrace
object DigestNotFound {
  implicit val format: Format[DigestNotFound] = JsonFormats.singletonFormat(DigestNotFound())
}
