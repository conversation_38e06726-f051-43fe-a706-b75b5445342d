include "application"

jwt.secret = ${JWT_SECRET}

play {
  server {
    pidfile.path = "/dev/null"
  }
}

akka {
  discovery {
    kubernetes-api.pod-namespace = ${POD_NAMESPACE}
    method = aggregate
    aggregate {
      discovery-methods = ["config", "akka-dns"]
    }
    config {
      services {
        sendgrid {
          endpoints = [
            {
              host = "api.sendgrid.com"
              port = 443
            }
          ]
        }
        rentalconnect {
          endpoints = [
            {
              host = "secure.rentalconnect.co.za"
              port = 443
            }
          ]
        }
        pdf-api {
          endpoints = [
            {
              host = "pdf-api.phoenix.svc.cluster.local"
              port = 9000
            }
          ]
        }
      }
    }
  }
}

lagom.akka.discovery {
  service-name-mappings {
    sendgrid {
      lookup = sendgrid
      scheme = https
    }
    rentalconnect {
      lookup = rentalconnect
      scheme = https
    }
    pdf-api {
      lookup = pdf-api
      scheme = http
    }
  }
}

akka.management {
  cluster.bootstrap {
    contact-point-discovery {
      discovery-method = kubernetes-api
      service-name = ${AKKA_CLUSTER_BOOTSTRAP_SERVICE_NAME}
      required-contact-point-nr = ${REQUIRED_CONTACT_POINT_NR}
    }
  }
}

# after 60s of unsuccessful attempts to form a cluster,
# the actor system will shut down
akka.cluster.shutdown-after-unsuccessful-join-seed-nodes = 60s

# exit jvm on actor system termination
# this will allow Kubernetes to restart the pod
lagom.cluster.exit-jvm-when-system-terminated = on

cassandra-query-journal.eventual-consistency-delay = 100ms
cassandra-query-journal.delayed-event-timeout = 10s
