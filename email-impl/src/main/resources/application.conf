#play.crypto.secret is deprecated, use play.http.secret.key instead
play.http.secret.key = ${?APPLICATION_SECRET}
play.application.loader = com.phoenix.email.impl.EmailLoader

email.cassandra.keyspace = email
jwt.issuer = email
invoiceBatchSchedulerDelay=5m

sentry-dsn = ${?SENTRY_DSN}

akka {
  actor {
    enable-additional-serialization-bindings = on

    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      jackson-cbor = "akka.serialization.jackson.JacksonCborSerializer"
    }

    serialization-bindings {
      # Commands
      "com.phoenix.email.impl.actors.AgencyDigestScheduler$Command" = jackson-json
      "com.phoenix.email.impl.EmailConversationCommand" = jackson-json
      "com.phoenix.email.impl.entities.AgencyDigestCommand" = jackson-json
      
      # Events
      "com.phoenix.email.impl.EmailConversationEvent" = jackson-json
      "com.phoenix.email.impl.entities.AgencyDigestEvent" = jackson-json
      
      # States
      "com.phoenix.email.impl.EmailConversation" = jackson-json
      "com.phoenix.email.impl.entities.AgencyDigestState" = jackson-json
    }
  }

  serialization.jackson {
    jackson-modules += "com.fasterxml.jackson.datatype.joda.JodaModule"
  }
}

cassandra-journal.keyspace = ${email.cassandra.keyspace}
cassandra-snapshot-store.keyspace = ${email.cassandra.keyspace}
lagom.persistence.read-side.cassandra.keyspace = ${email.cassandra.keyspace}

jwt.issuer = "email"

app.url = ${?APP_URL}

sendgrid = {
    token = ${?SENDGRID_TOKEN}
    rentalconnect-token = ${?RENTALCONNECT_TOKEN}
    public-key = ${?SENDGRID_PUBLIC_KEY}
    private-key = ""
}

email.from = {
  name = "reOS"
  address = "<EMAIL>"
}

lagom.persistence.read-side.global-prepare-timeout = 120s
lagom.broker.kafka.client.consumer.batching-size = 1

cassandra-journal.write-static-column-compat = off
cassandra-query-journal {
  write-static-column-compat = off
  first-time-bucket = "20190101T00:00"
}

com.lightbend.platform-tooling.service-discovery.external-service-addresses {
  "sendgrid" = ["https://api.sendgrid.com"]
  "rentalconnect" = ["https://secure.rentalconnect.co.za/"]
}

# Enable new sharding state store mode by overriding Lagom's default
akka.cluster.sharding.state-store-mode = ddata

# Enable serializers provided in Akka 2.5.8+ to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done"                 = akka-misc
  "akka.actor.Address"        = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

lagom.circuit-breaker {
  default {
    exception-whitelist = [
      "com.lightbend.lagom.scaladsl.api.transport.NotFound"
    ]
  }
  pdf-api {
    call-timeout = 57s
    reset-timeout = 10s
  }
}

akka.actor.allow-java-serialization=off

lagom.persistence.ask-timeout=30s

api-pdf.username=${?PDF_API_USER}
api-pdf.password=${?PDF_API_PASSWORD}
