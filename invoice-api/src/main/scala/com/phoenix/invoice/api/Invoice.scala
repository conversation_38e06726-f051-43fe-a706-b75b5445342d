package com.phoenix.invoice.api

import com.phoenix.finance.Beneficiary
import com.phoenix.party.api.AccountType
import com.phoenix.portfolio.api.Portfolio_v1
import com.phoenix.portfolio.api.response.PortfolioSummaryRow
import com.phoenix.util.UUID
import org.joda.time.DateTime
import play.api.libs.json._

import scala.util.Try

case class Invoice(
                    id: UUID,
                    tags: Map[String, String],
                    number: Option[Int] = None,
                    customer: Option[Customer] = None,
                    portfolioId: Option[UUID] = None,
                    property: Option[String] = None,
                    reoccurring: String,
                    invoiceType: String,
                    invoiceTypeName: String,
                    dueDate: Option[String] = None,
                    vat: Option[Boolean] = None,
                    amount: Option[BigDecimal] = None,
                    beneficiaries: Seq[InvoiceBeneficiaryRule] = Seq.empty,
                    description: Option[String] = None
                  ) {

  def toApiInvoiceV2(p: Option[Portfolio_v1]): InvoiceV2 = {
    InvoiceV2(id, tags, number, customer, portfolioId, property, reoccurring, invoiceType, invoiceTypeName, dueDate, vat, amount, beneficiaries, description)
  }

  def toApiResponseV2(s: Option[PortfolioSummaryRow] = None): InvoiceV2 ={
    InvoiceV2(id, tags, number, customer, portfolioId, property, reoccurring, invoiceType, invoiceTypeName, dueDate, vat, amount, beneficiaries, description,
      mainText = s.map(_.mainText),
      secondaryText = s.map(_.secondaryText),
      metaData = s.flatMap(_.metaData))
  }
}

case class Customer(partyId: UUID, customerTag: AccountType, name: Option[String] = None)

object Customer {
  implicit val format: Format[Customer] = Json.format
}

case class Property(propertyId: UUID, address: Option[String] = None)

object Property {
  implicit val format: Format[Property] = Json.format
}

final case class InvoiceTypeSummary(name: String, value: String, incomeType: String, vat: VatSummary)

object InvoiceTypeSummary {
  implicit val format: Format[InvoiceTypeSummary] = Json.format
}

final case class VatSummary(onCharge: Boolean, vatable: Boolean)

object VatSummary {
  implicit val format: Format[VatSummary] = Json.format
}

case class InvoiceBeneficiaryRule(beneficiary: Beneficiary)

object InvoiceBeneficiaryRule {
  implicit val format: Format[InvoiceBeneficiaryRule] = new Format[InvoiceBeneficiaryRule] {
    override def reads(json: JsValue): JsResult[InvoiceBeneficiaryRule] = {
      if ((json \ "beneficiary").isEmpty) {
        val jsObj = json.as[JsObject]
        Json.format[InvoiceBeneficiaryRule].reads(jsObj ++ JsObject(Seq("beneficiary" -> (jsObj - "type"))))
      } else {
        Json.format[InvoiceBeneficiaryRule].reads(json)
      }
    }

    override def writes(o: InvoiceBeneficiaryRule): JsValue = Json.format[InvoiceBeneficiaryRule].writes(o)
  }
}

// is this being used?
//case class BeneficiaryCategory(label: String, partyTags: Seq[String])
//
//object BeneficiaryCategory {
//  implicit val format: Format[BeneficiaryCategory] = Json.format
//}

object Invoice {
  def byTags(maybeTags: Option[Seq[String]]): Invoice => Boolean = {
    maybeTags.map(
      _.map(tag => {
        val split = tag.split(":")
        (split(0), Try(split(1)).toOption)
      })
    ).map(p => Map(p: _*)) match {
      case Some(tokenized) => p: Invoice => !p.tags.exists(
        tag => tokenized.get(tag._1) match {
          case None => false // Skip entry if tag is missing
          case Some(tagValue) => tagValue match {
            case Some(value) => value == tag._1
            case None => true // Use entry as long as it has the tag
          }
        }
      )
      case None => _: Invoice => true
    }
  }

  /**
    * @todo This is a different attempt a searching, it will try match whole words in order
    *       the only exception is the last word - it will fuzzy match it
    */
  def byQuery(maybeQuery: Option[String]): Invoice => Boolean = {
    def checkToken(string: String, token: Either[String, String]) = token match {
      case Left(t) => t == string
      case Right(t) => string.contains(t)
    }
    def checkString(string: String, tokens: Seq[Either[String, String]]) = {
      val stringTokens = string.toLowerCase.split(" ")
      val index = stringTokens.indexWhere(s => tokens.exists(p => checkToken(s, p)))
      val splice = stringTokens.slice(index, tokens.length)
      if (splice.length == tokens.length) {
        splice.zip(tokens).forall(p => (checkToken _).tupled(p))
      } else {
        false
      }
    }
    maybeQuery.map({
      query =>
        val tokens = query.toLowerCase.split(" ")
        val starting = tokens.take(tokens.length - 1)
        val current = tokens.lastOption.map(Seq(_)).getOrElse(Seq())
        starting.map(Left.apply) ++ current.map(Right.apply)
    }) match {
      case None => _: Invoice => true
      case Some(tokens) => summary: Invoice =>
        summary.customer.flatMap(_.name).exists(p => checkString(p, tokens)) ||
          summary.property.exists(p => checkString(p, tokens))
    }
  }

  implicit val format: Format[Invoice] = Json.format
}
case class InvoiceV2(
                    id: UUID,
                    tags: Map[String, String],
                    number: Option[Int] = None,
                    customer: Option[Customer] = None,
                    portfolioId: Option[UUID] = None,
                    property: Option[String] = None,
                    reoccurring: String,
                    invoiceType: String,
                    invoiceTypeName: String,
                    dueDate: Option[String] = None,
                    vat: Option[Boolean] = None,
                    amount: Option[BigDecimal] = None,
                    beneficiaries: Seq[InvoiceBeneficiaryRule] = Seq.empty,
                    description: Option[String] = None,
                    mainText: Option[String] = None,
                    secondaryText: Option[String] = None,
                    metaData: Option[String] = None,
                  )
object InvoiceV2 {
  implicit val format: Format[InvoiceV2] = Json.format
}
