#play.crypto.secret is deprecated, use play.http.secret.key instead
play.http.secret.key = ${?APPLICATION_SECRET}
play.application.loader = com.phoenix.invoice.impl.InvoiceLoader

invoice.cassandra.keyspace = invoice
jwt.issuer = invoice

sentry-dsn = ${?SENTRY_DSN}

akka {
  actor {
    enable-additional-serialization-bindings = on

    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      jackson-cbor = "akka.serialization.jackson.JacksonCborSerializer"
    }

    serialization-bindings {
      "com.phoenix.invoice.impl.actors.MonthlyInvoiceScheduler$CommandSerializable" = jackson-json
      "com.phoenix.invoice.impl.actors.SimpleBatchInvoiceScheduler$CommandSerializable" = jackson-json
      "com.phoenix.invoice.impl.actors.InvoiceTemplateGenerator$Command" = jackson-json
      "com.phoenix.invoice.impl.actors.InvoiceTemplateGenerator$GeneratorDone" = jackson-json
      "com.phoenix.invoice.impl.commands.InvoiceCommand" = jackson-json
      "com.phoenix.invoice.impl.commands.BatchCommand" = jackson-json
      "com.phoenix.invoice.impl.events.InvoiceEvent" = jackson-json
      "com.phoenix.invoice.impl.events.BatchEvent" = jackson-json
      "com.phoenix.invoice.impl.InvoiceState" = jackson-json
      "com.phoenix.invoice.impl.BatchState" = jackson-json
    }
  }

  serialization.jackson {
    jackson-modules += "com.fasterxml.jackson.datatype.joda.JodaModule"
  }
}

cassandra-journal.keyspace = ${invoice.cassandra.keyspace}
cassandra-snapshot-store.keyspace = ${invoice.cassandra.keyspace}
lagom.persistence.read-side.cassandra.keyspace = ${invoice.cassandra.keyspace}

lagom.persistence.read-side.global-prepare-timeout = 120s
lagom.broker.kafka.client.consumer.batching-size = 1

cassandra-journal.write-static-column-compat = off
cassandra-query-journal {
  write-static-column-compat = off
  first-time-bucket = "20190101T00:00"
}

schedulers.invoiceSchedulerDelay=4h
schedulers.openInvoiceSchedulerDelay=20m
schedulers.invoiceGenerationDay=10
schedulers.invoiceGenerationDay=${?INVOICE_GENERATION_DAY}

# Enable new sharding state store mode by overriding Lagom's default
akka.cluster.sharding.state-store-mode = ddata

# Enable serializers provided in Akka 2.5.8+ to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done"                 = akka-misc
  "akka.actor.Address"        = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

akka.actor.allow-java-serialization=off

lagom.circuit-breaker {
  default {
    exception-whitelist = [
      "com.lightbend.lagom.scaladsl.api.transport.NotFound"
    ]
  }
}
