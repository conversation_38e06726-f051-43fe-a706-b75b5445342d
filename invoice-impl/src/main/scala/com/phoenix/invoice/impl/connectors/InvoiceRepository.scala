package com.phoenix.invoice.impl.connectors

import akka.Done
import com.datastax.driver.core.{BoundStatement, PreparedStatement, Row}
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.phoenix.invoice.api.request.CreateInvoiceRequest
import com.phoenix.invoice.api.{Customer, Invoice, InvoiceType}
import com.phoenix.invoice.impl._
import com.phoenix.invoice.impl.events.InvoiceComponents
import com.phoenix.invoice.impl.util.{AutoBind, InvoiceMeta}
import com.phoenix.party.api.AccountType
import com.phoenix.persistence.QuillHelper
import com.phoenix.portfolio.api.Recurrence
import com.phoenix.util.UUID
import io.getquill.{CassandraLagomAsyncContext, SnakeCase}
import org.joda.time.DateTime
import play.api.libs.json.{JsS<PERSON>, Json}

import scala.concurrent.{ExecutionContext, Future, Promise}

class InvoiceRepository(session: CassandraSession, partyRepository: PartyRepository, propertyConnector: PropertyConnector, portfolioConnector: PortfolioConnector)(implicit ec: ExecutionContext) {

  val ctx = new CassandraLagomAsyncContext(SnakeCase, session)
  import QuillHelper.Implicits._
  import ctx._

  implicit val encodeInvoice = MappedEncoding[InvoiceState.Invoice, String](i => Json.toJson(i).toString)
  implicit val decodeInvoice = MappedEncoding[String, InvoiceState.Invoice](str => Json.parse(str).as[InvoiceState.Invoice])

  implicit val encodeRecurrence = MappedEncoding[Recurrence, String](r => r.getObjectName)
  implicit val decodeRecurrence = MappedEncoding[String, Recurrence](s => JsString(s).as[Recurrence]) // @todo test

  implicit val encodeStatus = MappedEncoding[InvoiceStatus, String](i => i.getObjectName)
  implicit val decodeStatus = MappedEncoding[String, InvoiceStatus](s => JsString(s).as[InvoiceStatus])

  implicit val encodeInvoiceType = MappedEncoding[InvoiceType, String](i => i.getObjectName)
  implicit val decodeInvoiceType = MappedEncoding[String, InvoiceType](s => JsString(s).as[InvoiceType])

  /**
   * @todo check if we need encoder/decoder for both CreateInvoiceRequest and Seq[CreateInvoiceRequest]
   */
  implicit val encodeBulkInvoices = MappedEncoding[Seq[CreateInvoiceRequest], String](invoice => Json.toJson(invoice).toString)
  implicit val decodeBulkInvoices = MappedEncoding[String, Seq[CreateInvoiceRequest]](str => Json.parse(str).as[Seq[CreateInvoiceRequest]])

  implicit val encodeBulkInvoice = MappedEncoding[CreateInvoiceRequest, String](invoice => Json.toJson(invoice).toString)
  implicit val decodeBulkInvoice = MappedEncoding[String, CreateInvoiceRequest](str => Json.parse(str).as[CreateInvoiceRequest])

  private val invoicesByPortfolio = quote {
    querySchema[PortfolioInvoice]("invoices_by_portfolio")
  }

  private val draftInvoices = quote {
    querySchema[InvoiceOwnedByTable]("draft_invoices", _.invoiceStatus -> "status")
  }

  private val generatedInvoiceTemplateTable = quote {
    querySchema[GeneratedInvoiceTemplate]("generated_invoice_templates")
  }

  private val bulkImportedInvoices = quote {
    querySchema[BulkImportedInvoice]("bulk_imported_invoices")
  }

  def getInvoicesByPortfolio(portfolioId: UUID): Future[Seq[PortfolioInvoice]] = {
    val q = quote { invoicesByPortfolio.filter(_.portfolioId == lift(portfolioId))}
    ctx.run(q)
  }

  def getInvoicesByOwner(agencyId: UUID): Future[Seq[InvoiceOwnedBy]] = {
    val q = quote { draftInvoices.filter(i => i.agencyId == lift(agencyId)) }
    for {
      i <- ctx.run(q)
      r  = i.map(i => implicitly[InvoiceOwnedBy](i))
    } yield r
  }

  def removeDraft(agencyId: UUID, invoiceId: UUID): Future[Done] = {
    ctx.run(quote { draftInvoices.filter(i => i.agencyId == lift(agencyId) &&
                                             i.invoiceId == lift(invoiceId)).delete } )
  }

  def setDraftInvoiceStatus(agencyId: UUID, invoiceId: UUID, status: InvoiceStatus): Future[Done] = {
    val q = quote { draftInvoices.filter(i => i.agencyId == lift(agencyId) &&
      i.invoiceId == lift(invoiceId)).update(_.invoiceStatus -> lift(status))
    }
    ctx.run(q)
  }

  def drafts(agencyId: UUID): Future[Seq[InvoiceOwnedBy]] = for {
    i <- ctx.run(quote { draftInvoices.filter(_.agencyId == lift(agencyId)) })
    r  = i.map(i => implicitly[InvoiceOwnedBy](i))
  } yield r

  def findDraft(agencyId: UUID, invoiceId: UUID): Future[Option[InvoiceOwnedBy]] = for {
    i <- ctx.run(quote { draftInvoices.filter(p => p.agencyId == lift(agencyId) && p.invoiceId == lift(invoiceId)) })
    r  = i.map(i => implicitly[InvoiceOwnedBy](i))
  } yield r.lastOption

  def findGeneratedTemplatesByDate(invoiceTemplateId: UUID, year: Int, month: Int): Future[Seq[GeneratedInvoiceTemplate]] = {
    val q = quote { generatedInvoiceTemplateTable.filter(it => it.invoiceTemplateId == lift(invoiceTemplateId) &&
                                                               it.year == lift(year) &&
                                                               it.month == lift(month)) }
    ctx.run(q)
  }

  def findGeneratedTemplates(invoiceTemplateId: UUID): Future[Seq[GeneratedInvoiceTemplate]] = {
    val q = quote { generatedInvoiceTemplateTable.filter(it => it.invoiceTemplateId == lift(invoiceTemplateId)) }
    ctx.run(q)
  }

  def getInvoiceSummariesByOwner(agencyId: String): Future[Seq[Invoice]] =
    getInvoicesByOwner(agencyId).map(_.map(row => implicitly[Invoice](row)))

  def insertPortfolioOwner(components: InvoiceComponents, invoiceId: UUID): Future[Done] =
    components.invoice.portfolioId match {
      case Some(portfolioId) =>
        insertPortfolioOwner(portfolioId, invoiceId, components.invoice.invoiceType)
      case None => Future(Done)
    }

  def insertPortfolioOwner(portfolioId: UUID, invoiceId: UUID, invoiceType: InvoiceType): Future[Done] = {
    val q = quote { invoicesByPortfolio.insert(_.portfolioId -> lift(portfolioId), _.invoiceId -> lift(invoiceId), _.invoiceType -> lift(invoiceType)) }
    ctx.run(q)
  }

  def buildInvoice(invoiceId: UUID, userId: UUID, agencyId: UUID, invoiceComponents: InvoiceComponents): Future[InvoiceOwnedByTable] = {
    val customerId = implicitly[UUID](invoiceComponents.invoice.customerId)
    val customerTag = invoiceComponents.invoice.customerTag.getObjectName
    val portfolioId = invoiceComponents.invoice.portfolioId.map(p => implicitly[UUID](p))
    for {
      customerName <- partyRepository.getCustomerById(customerId).map(_.flatMap(_.name))
      leaseAddress <- portfolioId match {
        case Some(leaseId) => propertyConnector.getAddressByLease(leaseId)
        case _ => Future.successful(None)
      }
      invoice       = InvoiceOwnedByTable(invoiceId, userId, agencyId, portfolioId, Some(customerId), Some(customerTag), customerName, leaseAddress.map(_.address),
        invoiceComponents.status, invoiceComponents.invoice, invoiceComponents.recurrence, invoiceComponents.invoice.dateDetails.dueDate,
        None, invoiceComponents.status == InvoiceStatus.History, invoiceComponents.invoice.description)
    } yield invoice
  }

  def insertDraftInvoice(invoiceId: UUID, userId: UUID, agencyId: UUID, invoiceComponents: InvoiceComponents): Future[Done] = {
    for {
      invoice <- buildInvoice(invoiceId, userId, agencyId, invoiceComponents: InvoiceComponents)
      result <- ctx.run(quote { draftInvoices.insert(lift(invoice)) })
    } yield result
  }

  def insertGeneratedInvoiceTemplate(invoiceTemplateId: UUID, year: Int, month: Int, recurrence: Recurrence, invoiceId: UUID): Future[Done]  = {
    val q = quote {
      generatedInvoiceTemplateTable.insert(
        _.invoiceTemplateId -> lift(invoiceTemplateId), _.year -> lift(year), _.month -> lift(month),
        _.recurrence -> lift(recurrence), _.invoiceId -> lift(invoiceId)
      )
    }
    ctx.run(q)
  }

  private val updateCounterPromise = Promise[PreparedStatement]
  def updateCounter(agencyCounter: AgencyCounter): Future[BoundStatement] = {
    updateCounterPromise.future.map {
      p =>
        val previousCounter = agencyCounter.counter - 1
        AutoBind(p)
          .setUUID("agency", agencyCounter.agencyId)
          .setInt("newCount", agencyCounter.counter)
          .setInt("count", if (previousCounter > 0) Some(previousCounter) else None )
    }
  }

  def prepareUpdateCounter(): Future[Done] = {
    val prepared = session.prepare("UPDATE invoice_numbers SET count = :newCount WHERE agency_id = :agency IF count = :count")
    updateCounterPromise.completeWith(prepared)
    prepared.map(_ => Done)
  }


  def getAgencyCounter(agencyId: UUID): Future[AgencyCounter] = {
    lazy val default = AgencyCounter(agencyId, 0)
    session.selectOne("SELECT * FROM invoice_numbers WHERE agency_id = ?", agencyId.uuid)
      .map(_.map(row => implicitly[AgencyCounter](row)).getOrElse(default))
  }

  def updateInvoiceNumber(agencyCounter: AgencyCounter): Future[Option[AgencyCounter]] = {
    val newCounter = agencyCounter.copy(counter = agencyCounter.counter + 1)
    val statement = updateCounter(newCounter)
    statement.flatMap(session.selectOne(_))
      .map(_.exists(_.getBool("[applied]")))
      .map {
        case true => Some(newCounter)
        case false => None
      }
  }

  def updateInvoiceNumberRecursively(agency: UUID, levels: Int = 0): Future[AgencyCounter] = {
    assert(levels < 2, "Update invoice recursively too deep")
    getAgencyCounter(agency).flatMap { ac =>
      updateInvoiceNumber(ac).flatMap {
        case None => updateInvoiceNumberRecursively(agency, levels + 1)
        case Some(newAc) => Future(newAc)
      }
    }
  }

  def updateInvoiceAddress(agencyId: UUID, invoiceId: UUID, address: String): Future[Done] = {
    val q = quote {
      draftInvoices.filter(p => p.agencyId == lift(agencyId) && p.invoiceId == lift(invoiceId))
        .update(_.property -> lift(Option(address)))
    }
    ctx.run(q)
  }

  def getBulkImportedInvoices(agencyId: UUID): Future[Seq[BulkImportedInvoice]] = {
    val q = quote {
      bulkImportedInvoices.filter(inv => inv.agencyId == lift(agencyId))
    }
    ctx.run(q)
  }

  def insertBulkImportedInvoice(agencyId: UUID, invoiceId: UUID, invoice: CreateInvoiceRequest): Future[Done] = {
    val q = quote {
      bulkImportedInvoices.insert(_.agencyId -> lift(agencyId), _.invoiceId -> lift(invoiceId), _.invoice -> lift(invoice))
    }
    ctx.run(q)
  }

  def deleteBulkImportedInvoice(agencyId: UUID, invoiceId: UUID): Future[Done] = {
    val q = quote {
      bulkImportedInvoices.filter(inv => inv.agencyId == lift(agencyId) && inv.invoiceId == lift(invoiceId)).delete
    }
    ctx.run(q)
  }

  def allPrepared: Boolean =
    Seq(
      updateCounterPromise.isCompleted
    )
    .forall(p => p)

  def prepareAll(): Future[Done] = for {
    _ <- prepareUpdateCounter()
  } yield Done

}

case class AgencyCounter(agencyId: UUID, counter: Int)
object AgencyCounter {
  implicit def fromRow(row: Row): AgencyCounter = {
    // Seems its stored as an int but u need to use getLong.
    AgencyCounter(
      row.getUUID("agency_id"),
      row.getInt("count")
    )
  }
}

case class GeneratedInvoiceTemplate(invoiceTemplateId: UUID, year: Int, month: Int, recurrence: Recurrence, invoiceId: UUID)

case class PortfolioInvoice(portfolioId: UUID, invoiceId: UUID, invoiceType: InvoiceType)
case class InvoiceOwnedByTable(
                           invoiceId: UUID,
                           userId: UUID,
                           agencyId: UUID,
                           portfolioId: Option[UUID],
                           customerId: Option[UUID],
                           customerTag: Option[String],
                           customer: Option[String],
                           property: Option[String],
                           invoiceStatus: InvoiceStatus,
                           invoice: InvoiceState.Invoice,
                           recurrence: Recurrence,
                           dueDate: Option[DateTime],
                           invoiceNumber: Option[Int],
                           isHistory: Boolean,
                           description: Option[String]
                         )
case class InvoiceOwnedBy(
                           invoiceId: UUID,
                           userId: UUID,
                           agencyId: UUID,
                           portfolioId: Option[UUID],
                           customerId: Option[UUID],
                           customerTag: Option[String],
                           customer: Option[String],
                           property: Option[String],
                           invoiceStatus: InvoiceStatus,
                           invoice: InvoiceState.Invoice,
                           recurrence: Recurrence,
                           dueDate: Option[DateTime],
                           invoiceNumber: Option[Int]
                         )

object InvoiceOwnedBy {
  private case class MetaInvoice(invoiceStatus: InvoiceStatus, invoice: InvoiceState.Invoice)
    extends InvoiceMeta.HasStatus with InvoiceMeta.HasInvoice

  implicit def createMetaInvoice(invoiceOwnedBy: InvoiceOwnedBy): InvoiceMeta.HasBoth =
    MetaInvoice(invoiceOwnedBy.invoiceStatus, invoiceOwnedBy.invoice)

  implicit def invoice(invoiceOwnedBy: InvoiceOwnedBy): Invoice = Invoice(
    invoiceOwnedBy.invoiceId,
    InvoiceMeta.tagsFor(implicitly[InvoiceMeta.HasBoth](invoiceOwnedBy)),
    invoiceOwnedBy.invoiceNumber, // @todo invoice number
    Option((invoiceOwnedBy.customerId, invoiceOwnedBy.customerTag))
      .collect { case (Some(customerId), Some(customerTag)) => Customer(customerId, Json.toJson(customerTag).as[AccountType], invoiceOwnedBy.customer) },
    invoiceOwnedBy.portfolioId,
    invoiceOwnedBy.property,
    invoiceOwnedBy.recurrence.getObjectName,
    invoiceOwnedBy.invoice.invoiceType.getObjectName,
    invoiceOwnedBy.invoice.invoiceType.name,
    invoiceOwnedBy.dueDate.map(_.toString("yyyy-MM-dd'T'HH:mm:ss.SSZZ")),
    invoiceOwnedBy.invoice.amount.map(_.hasVat),
    invoiceOwnedBy.invoice.amount.map(_.gross.toDouble),
    invoiceOwnedBy.invoice.beneficiaries,
    invoiceOwnedBy.invoice.description
  )

  implicit def fromTable(t: InvoiceOwnedByTable): InvoiceOwnedBy = {
    InvoiceOwnedBy(
      t.invoiceId,
      t.userId,
      t.agencyId,
      t.portfolioId,
      t.customerId,
      t.customerTag,
      t.customer,
      t.property,
      t.invoiceStatus,
      t.invoice,
      t.recurrence,
      t.dueDate,
      t.invoiceNumber
    )
  }
}

case class BulkImportedInvoice(agencyId: UUID, invoiceId: UUID, invoice: CreateInvoiceRequest)