package com.phoenix.invoice.impl.connectors

import java.util.UUID

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import io.getquill.{CassandraLagomAsyncContext, SnakeCase}

import scala.concurrent.{ExecutionContext, Future}

class PropertyConnector(session: CassandraSession)(implicit ec: ExecutionContext) {
  val ctx = new CassandraLagomAsyncContext(SnakeCase, session)
  import ctx._

  private val leaseAddresses = quote {
    querySchema[LeaseAddress]("lease_addresses")
  }

  def getAddressByLease(leaseId: UUID): Future[Option[LeaseAddress]] = {
    val q = quote { leaseAddresses.filter(_.leaseId == lift(leaseId)) }
    ctx.run(q).map(_.lastOption)
  }

  def insertAddress(leaseAddress: LeaseAddress): Future[Done] = {
    val q = quote { leaseAddresses.insert(lift(leaseAddress)) }
    ctx.run(q)
  }
}

case class LeaseAddress(leaseId: UUID, propertyId: UUID, agencyId: UUID, address: String)
