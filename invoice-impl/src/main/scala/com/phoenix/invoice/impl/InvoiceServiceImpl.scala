package com.phoenix.invoice.impl

import akka.actor.ActorSystem
import akka.actor.typed.ActorRef
import akka.actor.typed.scaladsl.adapter._
import akka.stream.Materializer
import akka.stream.scaladsl._
import akka.util.Timeout
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.{BadRequest, Forbidden, NotFound}
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.InvalidCommandException
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.projection.Projections
import com.lightbend.lagom.scaladsl.pubsub.PubSubRegistry
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import com.phoenix.authentication.jwt.{AgencyMembership, AuthenticationService, Authorization, UserSession}
import com.phoenix.date.DateUtcUtil
import com.phoenix.finance.Beneficiary
import com.phoenix.invoice.api
import com.phoenix.invoice.api.Accounting.{NoVatGroup, VatGroup15}
import com.phoenix.invoice.api._
import com.phoenix.invoice.api.request._
import com.phoenix.invoice.api.response._
import com.phoenix.invoice.impl.actors.MonthlyInvoiceScheduler
import com.phoenix.invoice.impl.commands._
import com.phoenix.invoice.impl.connectors._
import com.phoenix.invoice.impl.entities.InvoiceEntity
import com.phoenix.invoice.impl.util.InvoiceMeta
import com.phoenix.logging.EventStreamLogging
import com.phoenix.party.api.{AccountType, PartyTag}
import com.phoenix.portfolio.api.{PortfolioService, Recurrence}
import com.phoenix.projections.lib.ProjectionEndpointsImpl
import com.phoenix.util.UUID
import com.phoenix.validation.ValidationUtil._
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import play.api.libs.json.JsString

import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class InvoiceServiceImpl(
                         persistentEntityRegistry: PersistentEntityRegistry, system: ActorSystem,
                         invoiceRepository: InvoiceRepository,
                         invoiceSegmentImpl: InvoiceSegmentFilterImpl,
                         partyRepository: PartyRepository,
                         propertyConnector: PropertyConnector,
                         portfolioService: PortfolioService,
                         val auth: AuthenticationService,
                         val projectionsImpl: Projections,
                         actorMaterializer: Materializer
                       )
                       (implicit val executionContext: ExecutionContext, scheduler: ActorRef[MonthlyInvoiceScheduler.Command]) extends InvoiceService
  with EventStreamLogging
  with ProjectionEndpointsImpl
  with LazyLogging {

  private val vatGroup = VatGroup15()

  private def buildInvoiceSummary(id: UUID, recurrence: Recurrence): Future[Invoice] = for {
    invoiceState    <- persistentEntityRegistry.refFor[InvoiceEntity](id)
                        .ask(GetInvoice)
    invoice          = invoiceState.invoice.get
    customerQuery   <- partyRepository.getCustomerById(invoice.customerId)
    propertyQuery   <- invoice.portfolioId match {
      case Some(leaseId) => propertyConnector.getAddressByLease(leaseId)
      case None => Future(Option.empty[LeaseAddress])
    }
  } yield Invoice(
    id,
    InvoiceMeta.tagsFor(implicitly[InvoiceMeta.HasBoth](invoiceState)),
    invoiceState.invoiceNumber,
    Some(Customer(invoice.customerId, invoice.customerTag, customerQuery.flatMap(_.name))),
    invoice.portfolioId,
    propertyQuery.map(_.address),
    recurrence.name,
    invoice.invoiceType.getObjectName,
    invoice.invoiceType.name,
    invoice.dateDetails.dueDate.map(_.toString("yyyy-MM-dd")),
    invoice.amount.map(_.hasVat),
    invoice.amount.map(_.gross.toDouble),
    invoice.beneficiaries,
    invoice.description
  )

  private def resolveCustomerTag(customerString: String) = {
    // Try result party tag if account type results in error
    val attempt = Try(AccountType(customerString)).recover {
      case _: Error => PartyTag(customerString).accountType
    }
    attempt.get
  }

  implicit val am: Materializer = actorMaterializer

  override def getInvoice(id: String): ServiceCall[NotUsed, Invoice] = auth(Authorization.agency) {
    authorized => ServerServiceCall {
      _ =>
        val agency = authorized.agencyMembership.map(_.agencyId).get
        persistentEntityRegistry.refFor[InvoiceEntity](id)
          .ask(GetInvoice)
          .recover {
            case ex: InvalidCommandException if ex.message.startsWith("Failure invoice with ID") =>
              throw NotFound(ex.message)
          }
          .flatMap {
            case state if !state.agencyId.contains(UUID.fromString(agency)) =>
              throw Forbidden("Agency does not have access")
            case _ =>
              buildInvoiceSummary(id, Recurrence.OnceOff)

          }
    }
  }

  override def notifications: ServiceCall[NotUsed, Seq[InvoiceNotification]] = auth(Authorization.agency) {
    authorized => ServerServiceCall { _ =>
      val agencyId = UUID.fromString(authorized.agencyMembership.get.agencyId).uuid
      val segments = authorized.agencyMembership.get.segment.map(_.currentSegments)
      for {
        draftInvoices <- invoiceSegmentImpl.fetchDrafts(agencyId, segments)
        invoices = draftInvoices.filter(_.tags("status").equals(InvoiceStatus.Draft.name))
        unset     = invoices.filter(invoice => {
          invoice.invoice.amount match {
            case Some(x) if x.gross == BigDecimal(0.0) => true
            case None                                  => true
            case _                                     => false
          }
        })
        ready     = invoices.length - unset.length
      } yield {
        Seq(InvoiceNotification("Not Set", unset.length), InvoiceNotification("Ready", ready))
      }
    }
  }

  override def getInvoices(tags: Option[Seq[String]], query: Option[String]): ServiceCall[NotUsed, InvoiceResult] =
    auth(Authorization.agency) { authorized => ServerServiceCall { _ =>
      val agencyId = UUID.fromString(authorized.agencyMembership.get.agencyId).uuid
      val segments = authorized.agencyMembership.get.segment.map(_.currentSegments)
      import api.Invoice.{byQuery, byTags}
      invoiceSegmentImpl.fetchSummaries(agencyId, segments).map(
        p => InvoiceResult(p.filter(byTags(tags)).filter(byQuery(query)))
      )
    }
  }

  private def generateJwt(agencyId: UUID) = {
    auth.generateToken(UserSession("", Some(AgencyMembership.ReadOnlyMember(agencyId)), "", "", Some("invoice"), "", None, Some(true))).authToken
  }

  override def getInvoicesV2(tags: Option[Seq[String]], query: Option[String]): ServiceCall[NotUsed, InvoiceResultV2] = auth(Authorization.agency) { authorized =>
    ServerServiceCall { _ =>
      val agencyId = UUID.fromString(authorized.agencyMembership.get.agencyId).uuid
      val segments = authorized.agencyMembership.get.segment.map(_.currentSegments)
      import api.Invoice.{byQuery, byTags}
      for {
        invoices <- invoiceSegmentImpl.fetchSummaries(agencyId, segments).map(p => InvoiceResult(p.filter(byTags(tags)).filter(byQuery(query))))

        portfolioSummaries <- portfolioService.portfolioSummaries
          .handleRequestHeader(auth.authenticatedWith(generateJwt(agencyId)))
          .invoke()
          .recover {
            case e: Exception =>
              logger.error(s"Error fetching portfolio summaries for agency ${agencyId}: ${e.getMessage}")
              Seq.empty
          }

        summariesMap = portfolioSummaries.map(x => x.portfolioId -> x).toMap
        results <- Future.traverse(invoices.invoices){ invoice =>
          invoice.portfolioId match {
            case Some(id) =>
              summariesMap.get(id) match {
                case Some(portfolio) => Future.successful(invoice.toApiResponseV2(Some(portfolio)))
                case None => Future.successful(invoice.toApiResponseV2(None))
              }
            case None => Future.successful(invoice.toApiResponseV2(None))
          }
        }
      } yield InvoiceResultV2(results)
    }
  }

  override def updateInvoice(id: String): ServiceCall[CreateInvoiceRequest, Invoice] = auth(Authorization.agency) {
    authorized => ServerServiceCall {
      invoice =>
        validate(invoice)

        val invoiceId = UUID.fromString(id)
        val user = authorized.userId
        val agency = authorized.agencyMembership.map(_.agencyId).get
        val dueDate = invoice.dueDate match {
          case Some(date) if date.trim.isEmpty => None
          case Some(date) => Some(date).map(d => DateTime.parse(d.split('T').head, DateTimeFormat.forPattern("yyyy-MM-dd").withZoneUTC()))
          case None => None
        }
        // For now assume we're working with a draft invoice only
        val expenses = invoice.beneficiaries.map {
          case b: Beneficiary.PartyBeneficiary =>
            InvoiceBeneficiaryRule(b.copy(id = Some(b.id.getOrElse(UUID.createV4.uuid))))
          case b: Beneficiary.EasyPayBeneficiary =>
            InvoiceBeneficiaryRule(b.copy(id = Some(b.id.getOrElse(UUID.createV4.uuid))))
          case b: Beneficiary.DepositBeneficiary =>
            InvoiceBeneficiaryRule(b.copy(id = Some(b.id.getOrElse(UUID.createV4.uuid))))
        }

        val invoiceEntityRef = persistentEntityRegistry.refFor[InvoiceEntity](invoiceId)

        for {
          existingInvoice <- invoiceEntityRef.ask(GetInvoice)
          res <- invoiceEntityRef
            .ask(commands.UpdateInvoice(
              JsString(invoice.invoiceType).as[InvoiceType],
              existingInvoice.invoice.get.customerId, // override to existing customerId
              existingInvoice.invoice.get.customerTag, // override to existing customerTag
              Recurrence.OnceOff, //aren't all invoices once off at this point in time
              expenses,
              invoice.amount.map(
                p => Accounting.GrossIncome(p, if (invoice.vat.getOrElse(false)) vatGroup else NoVatGroup())
              ),
              invoice.portfolioId,
              agency,
              Some(UUID.fromString(user)),
              dueDate,
              invoice.description
            ))
            .flatMap(_ => buildInvoiceSummary(invoiceId, Recurrence.OnceOff))
            .recover {
              case InvalidCommandException(ex) => throw BadRequest(ex)
            }
        } yield res
    }
  }

  override def createOnceOffInvoice(): ServiceCall[CreateInvoiceRequest, Invoice] = auth(Authorization.agency) {
    authorized => ServerServiceCall {
      invoice =>
        validate(invoice)

        val invoiceId = invoice.id.getOrElse(UUID.createV4)
        val user = authorized.userId
        val agency = authorized.agencyMembership.map(_.agencyId).get
        val expenses = invoice.beneficiaries.map {
          case b: Beneficiary.PartyBeneficiary =>
            InvoiceBeneficiaryRule(b.copy(id = Some(UUID.createV4.uuid)))
          case b: Beneficiary.EasyPayBeneficiary =>
            InvoiceBeneficiaryRule(b.copy(id = Some(UUID.createV4.uuid)))
          case b: Beneficiary.DepositBeneficiary =>
            InvoiceBeneficiaryRule(b.copy(id = Some(UUID.createV4.uuid)))
        }
        val dueDate = invoice.dueDate match {
          case Some(date) if date.trim.isEmpty => None
          case Some(date) => Some(date).map(d => DateTime.parse(d, DateTimeFormat.forPattern("yyyy-MM-dd").withZoneUTC()))
          case None => None
        }

        val amount = invoice.amount.map(p =>
          Accounting.GrossIncome(p, if (invoice.vat.getOrElse(false)) vatGroup else NoVatGroup())
        )

        persistentEntityRegistry.refFor[InvoiceEntity](invoiceId)
          .ask(commands.CreateInvoice(
            invoiceType = JsString(invoice.invoiceType).as[InvoiceType],
            customerId = invoice.customer,
            customerTag = resolveCustomerTag(invoice.customerTag),
            recurrence = Recurrence.OnceOff,
            incomeExpenses = expenses,
            amount = amount,
            portfolioId = invoice.portfolioId,
            agencyId = agency,
            userId = user,
            invoiceTemplateId = None,
            dueDate = dueDate,
            description = invoice.description
          ))
          .flatMap(_ => buildInvoiceSummary(invoiceId, Recurrence.OnceOff))
          .recover {
            case InvalidCommandException(ex) => throw BadRequest(ex)
          }
    }
  }

  override def getInvoiceTypes: ServiceCall[NotUsed, InvoiceTypeSummaries] = auth(Authorization.agency) {
    _ => ServerServiceCall {
      _ => Future.successful(InvoiceTypeSummaries(InvoiceType.all.map(_.getSummary)))
    }
  }

  /**
   * @todo Should this ignore lease segments like it currently does?
   */
  override def getDraftInvoicesForPortfolio(portfolioId: String): ServiceCall[NotUsed, InvoicesByPortfolio] = auth(Authorization.agency) {
    authorized => ServerServiceCall {
      _ =>
        val agency = authorized.agencyMembership.map(_.agencyId).get
        for {
          allDrafts          <- invoiceRepository.drafts(agency)
          portfolioInvoices   = allDrafts
                                .filter(_.portfolioId.contains(UUID.fromString(portfolioId)))
                                .filter(_.invoiceStatus.getObjectName.equals(InvoiceStatus.Draft.getObjectName))
        } yield InvoicesByPortfolio(portfolioInvoices.map(_.invoiceId))
    }
  }

  // scalastyle:off
  override def sendAllInvoices(): ServiceCall[SendInvoicesRequest, InvoiceSendResponse] = auth(Authorization.agency) {
    authorized => ServerServiceCall {
      request =>
        val user = authorized.userId
        val agency = authorized.agencyMembership.map(_.agencyId).get
        val refs = request.invoiceIds
          .map(id => persistentEntityRegistry.refFor[InvoiceEntity](id))

        for {
          refCounters <- Source(immutable.Seq.from(refs))
            .mapAsync(1)(ref => invoiceRepository.updateInvoiceNumberRecursively(agency).map(ac => (ref, ac)))
            .runWith(Sink.seq)

          canOpenInvoices <- Future.sequence(refCounters.map {
            case (ref, ac) =>
              ref.ask(CheckCanOpenInvoice).map(check => (ref, ac, check))
          })

          _ <- Future.sequence(canOpenInvoices.map {
            case (ref, ac, check) if check.canSend =>
              ref.ask(OpenInvoice(Some(user), agency, ac.counter))
            case _ => Future(Done)
          })
        } yield {
          val invoiceResults = canOpenInvoices.map(_._3).map(canOpen => InvoiceSendResult(canOpen.id, canOpen.canSend, canOpen.result))
          InvoiceSendResponse(invoiceResults)
        }
    }
  }
  // scalastyle:on

  override def deleteInvoices(): ServiceCall[DeleteInvoicesRequest, InvoicesDeletedResponse] = auth(Authorization.agency) {
    authorized =>
      ServerServiceCall {
        request =>
          val user = authorized.userId
          val agency = authorized.agencyMembership.map(_.agencyId).get
          for {
            deletedInvoices <- Future.sequence(
              request.invoiceIds.map(id =>
                persistentEntityRegistry.refFor[InvoiceEntity](id)
                .ask(DeleteInvoice(id, Some(user), agency))
                  .map(_ => id)
              )
            )
          } yield InvoicesDeletedResponse(deletedInvoices.map(id => InvoiceDeleteResult(id)))
      }
  }

  override def restoreInvoices(): ServiceCall[RestoreInvoicesRequest, InvoicesRestoredResponse] = auth(Authorization.agency) {
    authorized =>
      ServerServiceCall {
        request =>
          val user = authorized.userId
          val agency = authorized.agencyMembership.map(_.agencyId).get
          for {
            restoredInvoices <- Future.sequence(
              request.invoiceIds.map(id =>
                persistentEntityRegistry.refFor[InvoiceEntity](id)
                  .ask(RestoreInvoice(id, user, agency))
                  .map(_ => id)
              )
            )
          } yield InvoicesRestoredResponse(restoredInvoices.map(id => InvoiceRestoredResult(id)))
      }
  }

  // scalastyle:off
  override def invoiceEvents: Topic[InvoicingEvent] = TopicProducer.singleStreamWithOffset[InvoicingEvent] { offset =>
    persistentEntityRegistry.eventStream(events.InvoiceEvent.Tag, offset)
      .mapAsync(1)(logEventStreamElementAsync)
      .flatMapConcat {
        case EventStreamElement(invoiceId, events.InvoiceUpdated(components, agencyId, eventRecord), _offset) =>
          val invoice = components.invoice
          val amount = invoice.amount
          val gross = amount.map(_.gross).getOrElse(BigDecimal(0))
          val vat = amount.map(_.vat).getOrElse(BigDecimal(0))
          val net = amount.map(_.net).getOrElse(BigDecimal(0))
          Source.single((api.InvoiceUpdated(invoiceId, agencyId, invoice.invoiceType.getObjectName, invoice.customerId, invoice.customerTag,
            invoice.dateDetails.dueDate, gross, vat, net, invoice.portfolioId,
            invoice.beneficiaries, DateUtcUtil.now().withMillis(eventRecord.createdAt)), _offset))
        case EventStreamElement(invoiceId, events.InvoiceDeleted(id, agencyId, eventRecord), _offset) =>
          Source.single((api.InvoiceDeleted(id, agencyId, DateUtcUtil.now().withMillis(eventRecord.createdAt)), _offset))
        case EventStreamElement(invoiceId, events.InvoiceCreated(components, agencyId, eventRecord), _offset) =>
          val invoice = components.invoice
          val amount = invoice.amount
          val gross = amount.map(_.gross).getOrElse(BigDecimal(0))
          val vat = amount.map(_.vat).getOrElse(BigDecimal(0))
          val net = amount.map(_.net).getOrElse(BigDecimal(0))
          Source.single((api.InvoiceCreated(invoiceId, agencyId, invoice.invoiceType.getObjectName, invoice.customerId, invoice.customerTag,
            invoice.dateDetails.dueDate, gross, vat, net, invoice.portfolioId,
            invoice.beneficiaries, DateUtcUtil.now().withMillis(eventRecord.createdAt)), _offset))
        case EventStreamElement(invoiceId, events.InvoiceOpened(agencyId, invoiceNumber, eventRecord), _offset) =>
          Source.fromFuture(persistentEntityRegistry.refFor[InvoiceEntity](invoiceId).ask(GetInvoice))
            .map {
              case InvoiceState(_, _, Some(invoice), _, _, _) =>
                val amount = invoice.amount
                val gross = amount.map(_.gross).getOrElse(BigDecimal(0))
                val vat = amount.map(_.vat).getOrElse(BigDecimal(0))
                val net = amount.map(_.net).getOrElse(BigDecimal(0))
                (api.InvoiceOpened(invoiceId, agencyId, invoice.invoiceType.getObjectName, invoice.customerId, invoice.customerTag,
                  invoice.dateDetails.dueDate, gross, vat, net, invoice.portfolioId,
                  invoice.beneficiaries, invoiceNumber, DateUtcUtil.now().withMillis(eventRecord.createdAt), invoice.description, invoice.applicationId), _offset)
              case _ => throw new Exception(s"Invoice $invoiceId does not exist")
            }
        case _ => Source.empty
      }
  }

  override def batchEvents: Topic[BatchEvent] = {
    TopicProducer.taggedStreamWithOffset[BatchEvent, events.BatchEvent](events.BatchEvent.Tag) {
      case (tag, offset) =>
        persistentEntityRegistry.eventStream(tag, offset)
          .collect {
            case EventStreamElement(_, events.BatchCreated(batchId, invoices, agencyId, eventRecord), _offset) =>
              val groupingTuple = invoices.headOption.map(inv => (inv.customerId, inv.customerTag, inv.portfolioId).toString)
              logger.info(s"[Shard $tag] Batching offset ${_offset} for $groupingTuple")
              (api.SendBatchInvoices(batchId, agencyId, groupingTuple.getOrElse(batchId.toString), invoices, eventRecord), _offset)
          }
    }
  }

  def getActorStatus: ServerServiceCall[NotUsed, Done] = ServerServiceCall { _ =>
    import akka.actor.typed.scaladsl.AskPattern._

    import scala.concurrent.duration._
    val timeout: Timeout = 3.seconds
    scheduler.ask(MonthlyInvoiceScheduler.GetScheduleStatus)(timeout, system.toTyped.scheduler).map {
      case false => throw BadRequest("Actor not scheduled!")
      case true => Done
    }
  }

  override def saveBulkInvoices(): ServiceCall[SaveBulkInvoicesRequest, Done] = auth(Authorization.agency) {
    authorized =>
      ServerServiceCall {
        request =>
          val agency = authorized.agencyMembership.map(_.agencyId).get
          for {
            _ <- Source(scala.collection.immutable.Seq(request.invoices: _*))
                .mapAsync(1)(inv => invoiceRepository.insertBulkImportedInvoice(UUID.fromString(agency), inv.id.get, inv))
                .runWith(Sink.ignore)
          } yield Done
      }
  }

  override def getBulkInvoices: ServiceCall[NotUsed, GetBulkInvoicesResponse] = auth(Authorization.agency) {
    authorized =>
      ServerServiceCall {
        _ =>
          val agencyId = authorized.agencyMembership.map(_.agencyId).get
          for {
            invoices <- invoiceRepository.getBulkImportedInvoices(agencyId)
          } yield GetBulkInvoicesResponse(invoices.map(_.invoice))
      }
  }

  override def deleteBulkInvoices(): ServiceCall[NotUsed, Done] = auth(Authorization.agency) {
    authorized =>
      ServerServiceCall {
        _ =>
          val agencyId = authorized.agencyMembership.map(_.agencyId).get
          for {
            invoices <- invoiceRepository.getBulkImportedInvoices(agencyId)
            _ <- Source(scala.collection.immutable.Seq(invoices: _*))
              .mapAsync(1)(inv => invoiceRepository.deleteBulkImportedInvoice(UUID.fromString(agencyId), inv.invoiceId))
              .runWith(Sink.ignore)
          } yield Done
      }
  }
}
