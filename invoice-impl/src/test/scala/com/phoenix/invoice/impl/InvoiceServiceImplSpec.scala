package com.phoenix.invoice.impl

import com.phoenix.authentication.jwt.{AgencyMembership, UserSession}
import com.phoenix.invoice.api.InvoiceType
import com.phoenix.invoice.api.request.{CreateInvoiceRequest, DeleteInvoicesRequest, RestoreInvoicesRequest, SendInvoicesRequest}
import com.phoenix.invoice.api.response.InvoiceSendResult
import com.phoenix.party.api.AccountType
import com.phoenix.util.UUID
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.tagobjects.Slow
import org.scalatest.time.{Seconds, Span}

import scala.concurrent.Future

class InvoiceServiceImplSpec extends BaseSpec {
  override protected val listeners: Seq[String] = Seq.empty

  "InvoiceServiceImpl" should {
    "all statements prepared" taggedAs Slow in {
      eventually(timeout(Span(15, Seconds))) {
        assert(server.application.invoiceRepository.allPrepared)
      }
    }
    "fetch all invoice types" taggedAs Slow in {
        client.getInvoiceTypes
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke()
          .map(response => response.invoiceTypeSummaries.nonEmpty should ===(true))
    }
    "Fetch an invoice" taggedAs Slow in {
      for {
        invoiceCreated <- client.createOnceOffInvoice()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(CreateInvoiceRequest(
            None,
            UUID.createV4,
            "Tenant",
            None,
            Some(UUID.createV4),
            None,
            InvoiceType.RentInvoice.getObjectName,
            Some("2019-01-01"),
            Some(false),
            Some(BigDecimal(1000.0)),
            Seq.empty,
            Some("")
          ))
        assert <- eventually {
          val resp = client.getInvoice(UUID.fromString(invoiceCreated.id))
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke()
          whenReady(resp) { response =>
            response.id should ===(invoiceCreated.id)
          }
        }
      } yield assert
    }
    "list of invoices fetched" taggedAs Slow in {
      val tokenContent: UserSession =
        UserSession(UUID.createV4, Some(AgencyMembership.Owner(UUID.createV4)), "Test", "<EMAIL>", Some("user"), totpSecret, None, None)

      val customerId = UUID.createV4

      for {
        _ <- partyRepository.upsertCustomer(customerId, Some("Bilbo Baggins"))

        invoiceCreated <- client.createOnceOffInvoice()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(CreateInvoiceRequest(
            None,
            customerId,
            "Tenant",
            None,
            Some(UUID.createV4),
            None,
            InvoiceType.RentInvoice.getObjectName,
            Some("2019-01-01"),
            vat = Some(false),
            Some(BigDecimal(1000.0)),
            Seq()
          ))
        assert <- eventually {
          val resp = client.getInvoices(None, None)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke()
          resp.map { response =>
            val assertedTuple = (Some("Bilbo Baggins"), invoiceCreated.id, invoiceCreated.amount, invoiceCreated.portfolioId,
              Map("lightStatus" -> "Green", "status" -> "Draft", "subStatus" -> "ready to go"))
            assert(response.invoices.nonEmpty)
            response.invoices.map(i => (i.customer.flatMap(_.name), i.id, i.amount, i.portfolioId, i.tags)) should contain (assertedTuple)
          }
        }
      } yield assert
    }
    "bulk send invoices should remove them from the listing" taggedAs Slow in {
      for {
        invoices <- Future.sequence(1 to 5 map(_ => client.createOnceOffInvoice()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(CreateInvoiceRequest(
            None,
            UUID.createV4,
            "Tenant",
            None,
            Some(UUID.createV4),
            None,
            InvoiceType.RentInvoice.getObjectName,
            Some("2019-01-01"),
            vat = Some(false),
            Some(BigDecimal(1000.0)),
            Seq()
          ))))
        sendAllRequest = SendInvoicesRequest(invoices.map(_.id))
        sent <- client.sendAllInvoices()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(sendAllRequest)
        assert <- eventually {
          val resp = client.getInvoices(None, None)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke()
          resp.map { response =>
            response.invoices.length should===(1) // 2 created from first 2 tests but 1 with this agencyId
          }
        }
      } yield assert
    }
    "bulk delete draft invoices" taggedAs Slow in {
      for {
        invoice1 <- client.createOnceOffInvoice()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(CreateInvoiceRequest(
            None,
            UUID.createV4,
            "Tenant",
            None,
            Some(UUID.createV4),
            None,
            InvoiceType.RentInvoice.getObjectName,
            Some("2019-01-01"),
            vat = Some(false),
            Some(BigDecimal(1000.0)),
            Seq()
          ))
        invoice2 <- client.createOnceOffInvoice()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(CreateInvoiceRequest(
            None,
            UUID.createV4,
            "Tenant",
            None,
            Some(UUID.createV4),
            None,
            InvoiceType.AccountingFees.getObjectName,
            Some("2019-01-01"),
            vat = Some(false),
            Some(BigDecimal(500.0)),
            Seq()
          ))
        newlyCreatedInvoices <- client.getInvoices(None, None)
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke()
        deleteRequest = DeleteInvoicesRequest(Seq(newlyCreatedInvoices.invoices.head.id))
        _ <- client.deleteInvoices()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(deleteRequest)

        assert <- eventually {
          val resp = client.getInvoices(None, None)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke()

          resp.map { response =>
            invoice1.tags("status") should ===(InvoiceStatus.Draft.getObjectName)
            invoice2.tags("status") should ===(InvoiceStatus.Draft.getObjectName)
            response.invoices.length should ===(3)
            response.invoices.find(i => i.id == UUID.fromString(deleteRequest.invoiceIds.head)).get.tags("status") should ===(InvoiceStatus.RemovedDraft.name)
          }
        }
      } yield assert
    }
    "bulk send invoices with and without errors" taggedAs Slow in {
      for {
        invalidInvoices <- Future.sequence(1 to 2 map(_ => client.createOnceOffInvoice()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(CreateInvoiceRequest(
            None,
            UUID.createV4,
            "Tenant",
            None,
            Some(UUID.createV4),
            None,
            InvoiceType.RentInvoice.getObjectName,
            Some("2019-01-01"),
            vat = None,
            None,
            Seq()
          ))))
        validInvoices <- Future.sequence(1 to 5 map(_ => client.createOnceOffInvoice()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(CreateInvoiceRequest(
            None,
            UUID.createV4,
            "Tenant",
            None,
            Some(UUID.createV4),
            None,
            InvoiceType.RentInvoice.getObjectName,
            Some("2019-01-01"),
            vat = Some(false),
            Some(BigDecimal(1000.0)),
            Seq()
          ))))
        sendTwoValidInvoices <- client.sendAllInvoices()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(SendInvoicesRequest(validInvoices.take(2).map(_.id)))
        sendAllRequest = SendInvoicesRequest((invalidInvoices ++ validInvoices).map(_.id))
        assert <- eventually {
          val resp = client.sendAllInvoices()
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(sendAllRequest)
          resp.map { response =>
            response.invoiceResults.length should === (7)
            response.invoiceResults.head.result should === (s"Invoice ID [${UUID.toString(response.invoiceResults.head.id)}] amount is unset")
            response.invoiceResults(3).result should === (s"Invoice ID [${UUID.toString(response.invoiceResults(3).id)}] has already been sent")
            response.invoiceResults.last.result should === ("Done")
          }
        }
      } yield assert
    }
    // depends on "bulk delete draft invoices" test
    "bulk restore invoices" taggedAs Slow in {
      for {
        invoices <- client.getInvoices(None, None)
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke()
        deletedInvoice = invoices.invoices.find(_.tags("status").equals(InvoiceStatus.RemovedDraft.name)).get
        restoredInvoice <- client.restoreInvoices()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(RestoreInvoicesRequest(Seq(deletedInvoice.id)))
        assert <- eventually {
          val res = client.getInvoices(None, None)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke()
          res.map { response =>
            response.invoices.count(_.tags("status").equals(InvoiceStatus.RemovedDraft.name)) should ===(0)
          }
        }
      } yield assert
    }
    "bulk send unset invoices should return errors" taggedAs Slow in {
      for {
        invoice <- client.createOnceOffInvoice()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(CreateInvoiceRequest(
            None,
            UUID.createV4,
            "Tenant",
            None,
            Some(UUID.createV4),
            None,
            InvoiceType.RentInvoice.getObjectName,
            Some("2019-01-01"),
            vat = None,
            None,
            Seq()
          ))
        sendAllRequest = SendInvoicesRequest(Seq(invoice.id))
        attempt <- client.sendAllInvoices()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(sendAllRequest)
        invoiceId = implicitly[String](invoice.id)
      } yield attempt.invoiceResults should ===(Seq(InvoiceSendResult(invoice.id, sent = false, s"Invoice ID [$invoiceId] amount is unset")))
    }
    "create backdated once off invoice" taggedAs Slow in {
      for {
        invoice <- client.createOnceOffInvoice()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(CreateInvoiceRequest(
            None,
            UUID.createV4,
            "Tenant",
            None,
            Some(UUID.createV4),
            None,
            InvoiceType.RentInvoice.getObjectName,
            Some("2017-03-31"),
            vat = None,
            None,
            Seq()
          ))
        assert <- eventually {
          val resp = client.getInvoices(Some(Seq("status:Open")), None)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke()
          resp.map { response =>
            val backdatedInvoice = response.invoices.collectFirst({ case inv if inv.id == invoice.id => inv })
            backdatedInvoice.get.dueDate should ===(Some("2017-03-31T00:00:00.00+00:00"))
          }
        }
      } yield assert
    }

    "throw a validation error when creating an invoice with a negative amount" taggedAs Slow in {
      val f = client.createOnceOffInvoice()
        .handleRequestHeader(auth.authenticatedHandler(tokenContent))
        .invoke(CreateInvoiceRequest(
          None,
          UUID.createV4,
          "Tenant",
          None,
          Some(UUID.createV4),
          None,
          InvoiceType.RentInvoice.getObjectName,
          Some("2017-03-31"),
          vat = None,
          Some(BigDecimal(-1000)),
          Seq(),
          None
        ))

      ScalaFutures.whenReady(f.failed) { e =>
        e shouldBe a [com.lightbend.lagom.scaladsl.api.transport.TransportException]
        e.getMessage should ===("{\"errors\":" +
          "[{\"key\":\"amount\",\"message\":\"is invalid\"}]}")
      }
    }

    "update an invoice" taggedAs Slow in {
      for {
        invoice <- client.createOnceOffInvoice()
          .handleRequestHeader(auth.authenticatedHandler(tokenContent))
          .invoke(CreateInvoiceRequest(
            None,
            UUID.createV4,
            "Tenant",
            None,
            Some(UUID.createV4),
            None,
            InvoiceType.RentInvoice.getObjectName,
            Some("2017-03-31"),
            vat = None,
            None,
            Seq()
          ))
        _ <- client.updateInvoice(invoice.id)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke(CreateInvoiceRequest(
              None,
              UUID.createV4,
              "Tenant",
              None,
              Some(UUID.createV4),
              None,
              InvoiceType.RentInvoice.getObjectName,
              Some("2017-03-31"),
              None,
              Some(BigDecimal(10000.00)),
              Seq()))
        assert <- eventually {
          val resp = client.getInvoices(Some(Seq("status:Open")), None)
            .handleRequestHeader(auth.authenticatedHandler(tokenContent))
            .invoke()
          resp.map { response =>
            val onceOffInvoice = response.invoices.collectFirst({ case inv if inv.id == invoice.id => inv })
            onceOffInvoice.get.dueDate should ===(Some("2017-03-31T00:00:00.00+00:00"))
            onceOffInvoice.map(_.amount.get) should ===(Some(BigDecimal(10000.00)))
          }
        }
      } yield assert
    }
  }
}
