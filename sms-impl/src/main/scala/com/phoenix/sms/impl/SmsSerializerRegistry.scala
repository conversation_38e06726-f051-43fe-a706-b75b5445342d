package com.phoenix.sms.impl

import com.lightbend.lagom.scaladsl.playjson.{JsonSerializer, JsonSerializerRegistry}
import scala.collection.immutable.Seq

object SmsSerializerRegistry extends JsonSerializerRegistry {
  private def api = {
    import com.phoenix.sms.api._
    Seq(
      JsonSerializer[Otp_v1],
    )
  }

  private def state = {
    import com.phoenix.persistence._
    Seq(
      JsonSerializer[StateRecord],
      JsonSerializer[CreatedState],
      JsonSerializer[DeletedState],
      JsonSerializer[EventRecord],
      JsonSerializer[OtpState],
      JsonSerializer[SmsState],
    )
  }

  private def requests = {
    import com.phoenix.sms.api.request._
    Seq(
      JsonSerializer[GetOtp],
      JsonSerializer[OtpPin],
      JsonSerializer[SendSmsRequest],
    )
  }

  private def responses = {
    import com.phoenix.sms.api.response._
    Seq(
      JsonSerializer[GetOtp],
      JsonSerializer[OtpValidation],
      JsonSerializer[Message],
      JsonSerializer[SendSmsResponse],
    )
  }

  private def events = {
    Seq(
      JsonSerializer[OtpCreated],
      JsonSerializer[SmsSent],
    )
  }

  private def commands = {
    Seq(
      JsonSerializer[CreateOtp],
      JsonSerializer[SendSmsCommand],
    )
  }

  override def serializers: Seq[JsonSerializer[_]] = api ++ state ++ requests ++ responses ++ events ++ commands
}
