package com.phoenix.sms.impl

import akka.Done
import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.lightbend.lagom.scaladsl.pubsub.PubSubRegistry
import com.phoenix.events
import com.phoenix.logging.EventLogging
import com.phoenix.persistence.PersistentEntityRegistrySugar
import com.phoenix.sms.api.SmsService
import com.typesafe.scalalogging.LazyLogging

import java.util.UUID
import scala.concurrent.ExecutionContext
import scala.concurrent.Future

class SmsServiceSubscriber(override val entityRegistry: PersistentEntityRegistry,
                           smsService: SmsService,
                           pubSubRegistry: PubSubRegistry)
                          (implicit executionContext: ExecutionContext)
extends PersistentEntityRegistrySugar
  with EventLogging
  with LazyLogging {

  smsService
    .otpEvents()
    .subscribe
    .withGroupId("sms-service")
    .atLeastOnce(
      Flow[events.OtpEvent].mapAsync(1)(logEventAsync).mapAsync(1) {
        case events.OtpCreated(otpId, cellNumber, pin) =>
          val content = s"OTP - $pin"
          val to = Seq(cellNumber)
          val smsId = UUID.randomUUID()
          entityRef[SmsEntity](smsId).ask(SendSmsCommand(smsId, content, to))
        case _ => Future.successful(Done)
      }
    )
}
