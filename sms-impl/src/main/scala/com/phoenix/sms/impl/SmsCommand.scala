package com.phoenix.sms.impl

import java.util.UUID

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.phoenix.sms.api.response.SendSmsResponse
import play.api.libs.json.{Format, Json}

sealed trait SmsCommand

case class SendSmsCommand(smsId: UUID, content: String, to: Seq[String]) extends SmsCommand with ReplyType[Done]
object SendSmsCommand {
  implicit val format: Format[SendSmsCommand] = Json.format
}