package com.phoenix.sms.impl

import java.util.UUID

import akka.NotUsed
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import com.phoenix.authentication.Encryption
import com.phoenix.sms.api._
import com.typesafe.config.ConfigFactory
import com.phoenix.authentication.jwt.AuthenticationService
import com.phoenix.events
import com.phoenix.sms.api.request.{OtpPin, SendSmsRequest}
import com.phoenix.sms.api.response.{GetOtp, OtpValidation}
import play.api.libs.json.{JsO<PERSON>, Json}
import com.phoenix.logging.EventStreamLogging
import com.phoenix.persistence.PersistentEntityRegistrySugar
import com.phoenix.totp.{Authenticator, TOTPSecret}
import com.typesafe.scalalogging.LazyLogging

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Random

class SmsServiceImpl(clickatellService: ClickatellService,
                     entityRegistry: PersistentEntityRegistry,
                     persistentEntityRegistry: PersistentEntityRegistry,
                     auth: AuthenticationService)
                    (implicit val executionContext: ExecutionContext)
  extends SmsService
  with EventStreamLogging
  with LazyLogging {

  val clickatellToken: String = ConfigFactory.load().getString("clickatell.token")
  private val secretKey = ConfigFactory.load().getString("play.http.secret.key")

  private def refForOtp(id: UUID) = persistentEntityRegistry.refFor[OtpEntity](id.toString)

  private def refForSms(id: UUID) = persistentEntityRegistry.refFor[SmsEntity](id.toString)

  private def getRandomElement(list: Seq[String], random: Random): String = list(random.nextInt(list.length))

  override def sendSms(): ServiceCall[SendSmsRequest, JsObject] = auth.authenticated { _ =>
    ServerServiceCall {
      request =>
          val smsId = UUID.randomUUID()
          refForSms(smsId).ask(SendSmsCommand(smsId, request.content, request.to)).map { _ =>
            // @todo change to case object response
            Json.obj("smsId"->smsId)
          }
    }
  }

  override def getOtp(cellNumber: String): ServiceCall[NotUsed, GetOtp] = auth.authenticated { authorization =>
    ServerServiceCall { _ =>
      val secret = Encryption.decrypt(secretKey, authorization.totpSecret)
      val totpSecret = TOTPSecret(secret)
      val otpId = UUID.randomUUID()
      val validationHash = "temporaryHardCodedString" // @todo generate hash from pin
      val random = new Random()
      val possibleOTPs: Seq[String] = Authenticator.totpSeq(totpSecret, System.currentTimeMillis() / 60000)
      val pin = getRandomElement(possibleOTPs, random)

      refForSms(otpId).ask(SendSmsCommand(otpId, s"reOS OTP: $pin", Array(cellNumber))).map(_ => GetOtp(otpId, cellNumber))
    }
  }

  override def otpEvents(): Topic[events.OtpEvent] = TopicProducer.singleStreamWithOffset { offset =>
    persistentEntityRegistry.eventStream(OtpEvent.Tag, offset)
      .filter(_.event.isInstanceOf[OtpEvent]).mapAsync(1)(logEventStreamElementAsync).mapAsync(1) {
      case EventStreamElement(otpId, OtpCreated(otpState), _offset) =>
        Future.successful(events.OtpCreated(otpId, otpState.cellNumber, otpState.pin) -> _offset)
    }
  }
}