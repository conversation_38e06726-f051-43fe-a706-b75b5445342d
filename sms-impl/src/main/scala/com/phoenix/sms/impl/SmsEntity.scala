package com.phoenix.sms.impl

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import com.lightbend.lagom.scaladsl.pubsub.PubSubRegistry
import com.lightbend.lagom.scaladsl.pubsub.TopicId
import com.phoenix.events
import com.phoenix.sms.api.ClickatellService
import com.phoenix.sms.api.request.SendSmsRequest
import com.typesafe.config.ConfigFactory
import play.api.libs.json.Json

import scala.concurrent.ExecutionContext

class SmsEntity(pubSubRegistry: PubSubRegistry,
                clickatellService: ClickatellService)
               (implicit val executionContext: ExecutionContext) extends PersistentEntity {
  override type Command = SmsCommand
  override type Event = SmsEvent
  override type State = Option[SmsState]

  val clickatellToken: String = ConfigFactory.load().getString("clickatell.token")

  override def initialState: Option[SmsState] = None

  override def behavior: Behavior = {
    Actions()
      .onCommand[SendSmsCommand, Done] {
      case(SendSmsCommand(smsId, content, to), ctx, _) =>
        val topic = pubSubRegistry.refFor(TopicId[events.SmsEvent](to.toString()))
        val sms = SmsState(smsId, to, content)

        val result = clickatellService.sendSms()
          .handleRequestHeader(_.addHeader("Authorization", clickatellToken))
          .invoke(SendSmsRequest(content, to.toArray))

        result.map(response => {
          val res = Json.obj(
            "messages"->response.messages,
            "errorCode"->response.errorCode,
            "error"->response.error,
            "errorDescription"->response.errorDescription
          )
          val meta = Map("response" -> res)
          topic.publish(events.SmsSent(smsId.toString(), to.toArray, meta))
        })

        ctx.thenPersist(SmsSent(sms))(_=> {
//            ctx.reply(SendSmsResponse(messages))
            ctx.reply(Done)
        })
    }
      .onEvent {
        case (SmsSent(sms), _) =>
          Some(sms)
      }
  }
}
