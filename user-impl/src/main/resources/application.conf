#play.crypto.secret is deprecated, use play.http.secret.key instead
play.http.secret.key = ${?APPLICATION_SECRET}
play.application.loader = com.phoenix.user.impl.UserLoader

user.cassandra.keyspace = user
jwt.issuer = user

sentry-dsn = ${?SENTRY_DSN}

akka {
  actor {
    enable-additional-serialization-bindings = on

    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      jackson-cbor = "akka.serialization.jackson.JacksonCborSerializer"
    }

    serialization-bindings {
      "com.phoenix.user.impl.UserCommand" = jackson-json
      "com.phoenix.user.impl.UserEvent" = jackson-json
      "com.phoenix.user.impl.UserState" = jackson-json
    }
  }

  serialization.jackson {
    jackson-modules += "com.fasterxml.jackson.datatype.joda.JodaModule"
  }
}

cassandra-journal.keyspace = ${user.cassandra.keyspace}
cassandra-snapshot-store.keyspace = ${user.cassandra.keyspace}
lagom.persistence.read-side.cassandra.keyspace = ${user.cassandra.keyspace}

lagom.persistence.read-side.global-prepare-timeout = 120s

lagom.circuit-breaker {
  default {
    exception-whitelist = [
      "com.lightbend.lagom.scaladsl.api.transport.NotFound"
      "com.lightbend.lagom.scaladsl.api.transport.TransportException"
    ]
  }
}

# Enable new sharding state store mode by overriding Lagom's default
akka.cluster.sharding.state-store-mode = ddata

cassandra-journal.write-static-column-compat = off
cassandra-query-journal {
  write-static-column-compat = off
  first-time-bucket = "20190101T00:00"
}

# Enable serializers provided in Akka 2.5.8+ to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done"                 = akka-misc
  "akka.actor.Address"        = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

akka.actor.allow-java-serialization=off
