package com.phoenix.user.impl

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import com.phoenix.authentication.Encryption
import com.phoenix.authentication.jwt.AgencyMembership
import com.phoenix.authentication.jwt.Segment
import com.phoenix.authentication.jwt.UserSession
import com.phoenix.date.DateUtcUtil
import com.phoenix.persistence.EventRecord
import com.phoenix.totp.Authenticator
import com.phoenix.totp.TOTPSecret
import com.phoenix.user.impl.util.LuhnToolkit
import com.phoenix.user.impl.util.SecurePasswordHashing
import com.phoenix.util.UUID
import com.typesafe.config.ConfigFactory
import play.api.libs.json.Format
import play.api.libs.json.JsSuccess
import play.api.libs.json.JsValue
import play.api.libs.json.Json
import play.api.libs.json.Reads
import play.api.libs.json.Writes

import scala.util.Random
import scala.util.Try
import scala.util.control.NoStackTrace

class UserEntity() extends PersistentEntity {
  import com.phoenix.user.api.OnboardProgress

  override type Command = UserCommand
  override type Event = UserEvent
  override type State = UserState

  override def initialState: State = UserState()

  import UserState.Mutations

  private def randomString(length: Int = 64) = Random.alphanumeric.take(length).mkString("")

  private val secretKey = ConfigFactory.load().getString("play.http.secret.key")

  private def lastValidTotpTime(state: UserState) = Try(state.cellphoneNumbers.collect({
    case UserCellphoneNumber(_, Some(dateTime)) => dateTime.getMillis
  }).max).toOption

  private def loginUserWithAgency(agencyId: Option[String], state: UserState) = {
    val agency = agencyId.flatMap { id => state.agencyMemberships.find(f => f.agencyId == id) }
    val firstName = state.userPersonalDetails.map(_.firstName).getOrElse("")
    val email = state.loginDetails.map(_.email).getOrElse("")
    val totpSecret = Encryption.encrypt(secretKey, state.totpSecret.getOrElse(""))
    val totpValid = lastValidTotpTime(state)
    val onboard = OnboardProgress(state.onboardingProgress.emailVerified, state.onboardingProgress.cellVerified, None)

    UserLoggedIn(UserSession(entityId, agency, firstName, email, Some("user"), totpSecret, totpValid,
      state.validSupportStaff), state.miscellaneous, onboard)
  }

  private def loginAsAuditor(agencyId: String, agencyName: String, state: UserState, segmentIds: Seq[UUID]): UserLoggedIn = {
    // @todo - this should be a readOnly thing to be addressed. AgencyMembership needs to be Auditor
    val agency = AgencyMembership.Owner(agencyId)

    val segment = Segment(segmentIds.map(_.uuid), segmentIds.map(_.uuid))
    val agencyWithSegments = agency.copy(segment = Some(segment))

    val firstName = state.userPersonalDetails.map(_.firstName).getOrElse("")
    val email = state.loginDetails.map(_.email).getOrElse("")
    val totpSecret = Encryption.encrypt(secretKey, state.totpSecret.getOrElse(""))
    val totpValid = lastValidTotpTime(state)
    val onboard = OnboardProgress(state.onboardingProgress.emailVerified, state.onboardingProgress.cellVerified, None)

    UserLoggedIn(UserSession(entityId, Some(agencyWithSegments), firstName, email, Some("user"), totpSecret, totpValid,
      state.validSupportStaff), state.miscellaneous,  onboard)
  }

  private def selectLastAgency(state: UserState, agency: Option[String]): Option[AgencyMembership] = agency match {
    case None => state.agencyMemberships.lastOption
    case Some(id) => state.agencyMemberships.find(_.agencyId == id)
  }

  private val translateAgencyMembership: PartialFunction[(UUID, String, Option[Segment]), Either[String, AgencyMembership]] = {
    case (agencyId, "Owner", segment)          => Right(AgencyMembership.Owner(agencyId.uuid.toString, segment))
    case (agencyId, "TeamMember", segment)     => Right(AgencyMembership.TeamMember(agencyId.uuid.toString, segment))
    case (agencyId, "ReadOnlyMember", segment) => Right(AgencyMembership.ReadOnlyMember(agencyId.uuid.toString, segment))
    case (_, role, _) => Left(s"Unknown role type: $role")
  }

  private def agencyInvite = Actions()
    .onCommand[AddAgencyUserFromInvite, Done] {
      case (_, ctx, state) if state.agencyMemberships.nonEmpty =>
        ctx.commandFailed(AlreadyJoinedAgency())
        ctx.done
      case(AddAgencyUserFromInvite(invitation, password), ctx, state) =>
        val role = translateAgencyMembership(invitation.agencyId, invitation.role.toString, Option.empty)
        role match {
          case Left(message) =>
            ctx.invalidCommand(message)
            ctx.done
          case Right(membership) =>
            val eventRecord = EventRecord(Some(invitation.invitorId.toString))
            val inviteEvent = UserAcceptedInvitation(
              invitation.agencyId, invitation.partyId, invitation.firstName, invitation.lastName,
              invitation.email, invitation.cellNumber, invitation.role.toString, eventRecord)
            val agencyEvent = UserAddedToAgency(membership, EventRecord(Some(invitation.invitorId.toString)))

            val valid = SecurePasswordHashing
              .validatePassword(password, state.loginDetails.map(_.password).get)
            if (valid) {
              ctx.thenPersistAll(inviteEvent, agencyEvent)(() => ctx.reply(Done))
            } else {
              val hashedPassword = SecurePasswordHashing.hashPassword(password)
              ctx.thenPersistAll(
                inviteEvent, agencyEvent, UserPasswordReset(UserLoginDetails(invitation.email, hashedPassword))
              )(() => ctx.reply(Done))
            }
        }
    }

  private def agency = Actions()
    .onCommand[AddAgencyUser, Done] {
      case(AddAgencyUser(userId, agencyId, role, eventRecord), ctx, _) =>
        translateAgencyMembership(agencyId, role, Option.empty) match {
          case Right(agencyMembership) =>
            ctx.thenPersist(UserAddedToAgency(agencyMembership, eventRecord)) { _ =>
              ctx.reply(Done)
            }
          case Left(error) =>
            ctx.invalidCommand(error)
            ctx.done
        }
    }
    .onCommand[RemoveAgencyMembership, Done] {
      case(RemoveAgencyMembership(userId, agencyId), ctx, state) if !state.agencyMemberships.map(_.agencyId).contains(agencyId) =>
        ctx.commandFailed(AlreadyLeftAgency())
        ctx.done
      case(RemoveAgencyMembership(userId, agencyId), ctx, _) =>
        ctx.thenPersist(AgencyRemovedFromUser(agencyId, EventRecord(Some(userId))))(_ => ctx.reply(Done))
    }
    .onCommand[CreateAgency, Done] {
      case(CreateAgency(agencyId, agency), ctx, state) =>
        ctx.thenPersistAll(
          UserCreatedAgency(agencyId, agency, EventRecord(Some(entityId))),
          UserAddedToAgency(AgencyMembership.Owner(agencyId), EventRecord(Some(entityId)))
        ) { () => ctx.reply(Done) }
    }
    .onReadOnlyCommand[GetUserAgencies.type, Seq[AgencyMembership]] {
      case (GetUserAgencies, ctx, state) => ctx.reply(state.agencyMemberships)
    }
    .onEvent {
      case (_: UserCreatedAgency, currentState) => currentState
      case (UserAddedToAgency(agencyMembership, _), currentState) =>
        val agencyId = UUID.fromString(agencyMembership.agencyId).uuid
        currentState.addAgencyMembership(agencyMembership)
          // Add the agency id as the default segment id
          .updateSegments(agencyId, Seq(agencyId))
      case (AgencyRemovedFromUser(agencyId, _), currentState) =>
        currentState.removeAgencyMembership(agencyId)
    }

  private def support = Actions()
    .onCommand[AddSupportAbility, Done] {
      case(AddSupportAbility(assignedBy), ctx, state) =>
        ctx.thenPersist(SupportAbilityAdded(state.loginDetails.get.email, EventRecord(Some(assignedBy))))(_ => ctx.reply(Done))
    }
    .onCommand[EnableUser, Done] {
      case (_: EnableUser, ctx, state) if state.userEnabled =>
        ctx.commandFailed(UserAlreadyEnabled())
        ctx.done
      case (EnableUser(enabledBy, email, reason, isTester, clickupRef), ctx, state) =>
        val firstName = state.userPersonalDetails.map(_.firstName).getOrElse("")
        ctx.thenPersistAll(
          UserEnabled(email, reason, isTester, clickupRef, EventRecord(Some(enabledBy))),
          EmailVerificationGenerated(randomString(), firstName, email)
        ){ () => ctx.reply(Done) }

    }
    .onCommand[DisableUser, Done] {
      case (_: DisableUser, ctx, state) if !state.userEnabled =>
        ctx.commandFailed(UserAlreadyDisabled())
        ctx.done
      case (DisableUser(disabledBy, email, reason, clickupRef), ctx, state) =>
        val allEmails = Option(email).filter(_ != "*").map(s => Seq(s))
          .getOrElse(state.reservedEmails.map(_.email))
          .map(e => UserDisabled(e, reason, clickupRef, EventRecord(Some(disabledBy))))
        ctx.thenPersistAll(allEmails: _*)(() => ctx.reply(Done))
    }
    .onEvent {
      case (e: SupportAbilityAdded, currentState) =>
        currentState.copy(validSupportStaff = Some(true))
      case (_: UserEnabled, currentState) => currentState.copy(userEnabled = true)
      case (_: UserDisabled, currentState) => currentState.copy(userEnabled = false)
    }

  private def verification = Actions()
    .onCommand[VerifyEmail, UserLoggedIn] {
      case(VerifyEmail(verificationId), ctx, state) =>
        val unverifiedEmail = state.reservedEmails.find { m =>
          m.verifyToken.contains(verificationId) // && m.verifiedDate.isEmpty
        }

        unverifiedEmail match {
          case Some(unverified) if unverified.verifiedDate.isEmpty =>
            val agencyId = selectLastAgency(state, None).map(_.agencyId)
            ctx.thenPersist(EmailValidated(unverified.email, DateUtcUtil.now()))(_ => ctx.reply(loginUserWithAgency(agencyId, state)))
          case Some(_) =>
            ctx.invalidCommand("E-mail has already been verified with this verification token")
            ctx.done
          case None =>
            ctx.invalidCommand("This is an invalid verification token")
            ctx.done
        }
    }
    .onCommand[SendVerificationEmail.type, Done] {
      case(SendVerificationEmail, ctx, state) =>
        val token = randomString()
        val firstName = state.userPersonalDetails.map(_.firstName).get
        val email = state.loginDetails.map(_.email).get
        ctx.thenPersist(EmailVerificationGenerated(token, firstName, email))(_ => ctx.reply(Done))
    }
    .onEvent {
      case (EmailVerificationGenerated(token, _, email), currentState) =>
        currentState.setEmailValidateToken(token, email)
      case (EmailValidated(email, validAt), currentState) =>
        currentState.validateEmail(email, validAt)
    }

  private def totp = Actions()
    .onCommand[AuthorizeOtpPrivileges, Done] {
      case(AuthorizeOtpPrivileges(_, otp), ctx, state) =>
        /**
          * @todo somehow map the cell number to this totp
          */
        state.cellphoneNumbers.lastOption match {
          case None =>
            ctx.invalidCommand("No cellnumber to validate")
            ctx.done
          case Some(cellphone) =>
            val valid = Authenticator.pinMatchesSecret(otp, TOTPSecret(state.totpSecret.get), System.currentTimeMillis() / 60000)
            if (valid) {
              ctx.thenPersist(CellNumberValidated(cellphone.number, DateUtcUtil.now())) { _ =>
                ctx.reply(Done)
              }
            } else {
              ctx.commandFailed(UserOtpInvalid())
              ctx.done
            }
        }
    }
    .onEvent {
      case (CellNumberValidated(cellNumber, validAt), currentState) =>
        currentState.validateCellphone(cellNumber, validAt)
    }

  private def password = Actions()
    .onCommand[ResetUsersForgottenPassword, Done] {
      case(ResetUsersForgottenPassword(newPassword, id), ctx, state) =>
        if (state.forgotPasswordId.contains(id)) {
          val details = state.loginDetails.map {
            _.copy(password = SecurePasswordHashing.hashPassword(newPassword))
          }
          ctx.thenPersist(UserPasswordReset(details.get))(_ => ctx.reply(Done))
        } else {
          ctx.invalidCommand("Token is incorrect")
          ctx.done
        }
    }
    .onCommand[GenerateForgotUserPasswordId, Done] {
      case (_: GenerateForgotUserPasswordId, ctx, state) if !state.userEnabled =>
        ctx.commandFailed(UserDisabledException())
        ctx.done
      case(GenerateForgotUserPasswordId(randomId, forgotPasswordToken), ctx, state) =>
        val email = state.loginDetails.map(_.email).get
        ctx.thenPersist(ForgotPasswordTokenGenerated(forgotPasswordToken, randomId, email))(_ => ctx.reply(Done))
    }
    .onEvent {
      case (UserPasswordReset(userLoginDetails), currentState) =>
        currentState.resetPassword(userLoginDetails)
      case (ForgotPasswordTokenGenerated(_, forgotPasswordId, _), currentState) =>
        currentState.setForgottenPassword(Some(forgotPasswordId))
    }

  private def miscellaneous = Actions()
    .onCommand[UpdateMiscellaneous, JsValue] {
      case(UpdateMiscellaneous(details), ctx, state) =>
        ctx.thenPersist(MiscellaneousUpdated(details))(_ => ctx.reply(details))
    }
    .onReadOnlyCommand[GetMiscellaneous.type, JsValue] {
      case(GetMiscellaneous, ctx, state) => ctx.reply(state.miscellaneous.get)
    }
    .onReadOnlyCommand[GetOnboardingProgress.type, OnboardProgress] {
      case(GetOnboardingProgress, ctx, state) => ctx.reply(state.onboardingProgress)
    }
    .onEvent {
      case (MiscellaneousUpdated(details), currentState) => currentState.updateMiscellaneous(details)
    }

  // scalastyle:off
  private def registered = Actions()
    .onCommand[UpdatePersonalDetails, Done] {
      case(UpdatePersonalDetails(userId, firstName, lastName, idNumber, dob, passport, cellNumber), ctx, _) =>
        val event = PersonalDetailsUpdated(firstName, lastName, cellNumber, idNumber, dob, passport, Some(EventRecord(Some(userId))))
        ctx.thenPersist(event)(_ => ctx.reply(Done))
    }
    .onCommand[UpdateLoginEmail, Done] {
      case (UpdateLoginEmail(email, updatedBy), ctx, state) =>
        val event = UserEmailUpdated(state.loginDetails.get.email, email, EventRecord(Some(updatedBy)))
        ctx.thenPersist(event)(_ => ctx.reply(Done))
    }
    .onCommand[UpdateUserCellnumber, Done] {
      case (UpdateUserCellnumber(newCellnumber, updatedBy), ctx, state) =>
        val cellNumber = state.cellphoneNumbers.find(_.verifiedDate.isDefined) match {
          case verified @ Some(_) => verified
          case None => state.cellphoneNumbers.lastOption
        }
        val event = UserCellnumberUpdated(cellNumber.map(_.number).getOrElse(""), newCellnumber, EventRecord(Some(updatedBy)))
        ctx.thenPersist(event)(_ => ctx.reply(Done))
    }
    .onCommand[UpdateUserPermissions, Done] {
      case (UpdateUserPermissions(newRole, agency, updatedBy), ctx, state) =>
        val agencyId = agency.uuid.toString()
        val membership = state.agencyMemberships.find(_.agencyId == agencyId).get

        translateAgencyMembership(agencyId, newRole, membership.segment) match {
          case Right(agencyMembership) =>
            val eventRecord = EventRecord(Some(updatedBy))
            ctx.thenPersist(UserMembershipUpdated(agencyMembership, eventRecord))(_ => ctx.reply(Done))
          case Left(error) =>
            ctx.invalidCommand(error)
            ctx.done
        }
    }
    .onCommand[LoginToAgency, UserLoggedIn] {
      case (LoginToAgency(agencyId, agencyName, segments), ctx, state) =>
        ctx.thenPersist(SupportStaffLoggedIn(agencyId, agencyName, EventRecord(None)))(_ =>
          ctx.reply(loginAsAuditor(agencyId, agencyName, state, segments))
        )
    }
    .onReadOnlyCommand[GetPersonalDetails.type, PersonalDetails] {
      case(GetPersonalDetails, ctx, state) =>
        val loginEmail = state.loginDetails.map(_.email).get
        val cellNumber = state.cellphoneNumbers.find(_.verifiedDate.isDefined) match {
          case verified @ Some(_) => verified
          case None => state.cellphoneNumbers.lastOption
        }
        ctx.reply(PersonalDetails(
          state.userPersonalDetails.get,
          Some(loginEmail),
          cellNumber.map(_.number),
          cellNumber.flatMap(_.verifiedDate).isDefined,
          state.reservedEmails.find(_.email == loginEmail).exists(_.verifiedDate.isDefined)
        ))
    }
    .onReadOnlyCommand[AuthenticateWithPassword, UserLoggedIn] {
      case (_: AuthenticateWithPassword, ctx, state) if !state.userEnabled =>
        ctx.commandFailed(UserDisabledException())
      case (AuthenticateWithPassword(pass), ctx, state) =>
        val valid = SecurePasswordHashing
          .validatePassword(pass, state.loginDetails.map(_.password).get)
        if (valid) {
          val agencyId = selectLastAgency(state, None).map(_.agencyId)
          ctx.reply(loginUserWithAgency(agencyId, state))
        } else {
          ctx.invalidCommand("Could not authenticate with that password")
        }
    }
    .onReadOnlyCommand[LoginUser, UserLoggedIn] {
      case (_: LoginUser, ctx, state) if !state.userEnabled =>
        ctx.commandFailed(UserDisabledException())
      case (LoginUser(agencyId, segments), ctx, state)
        if state.validSupportStaff.contains(true)
          && agencyId.isDefined
          && !state.agencyMemberships.map(_.agencyId).contains(agencyId.get) => {
        // if we are a support user and don't have this agency we are refreshing a token here.
        ctx.reply(loginAsAuditor(agencyId.get, "", state, segments.getOrElse(Seq(UUID.fromString(agencyId.get)))))
      }
      case (LoginUser(agencyId, _), ctx, state) =>
        ctx.reply(loginUserWithAgency(selectLastAgency(state, agencyId).map(_.agencyId), state))
    }
    .onEvent {
      case (PersonalDetailsUpdated(firstName, lastName, cellNumber, idNumber, dateOfBirth, passport, eventRecord), currentState) =>
        // Hotfix: the cell number and id number were assigned incorrectly and must be swapped for old events
        val cellNr = if (eventRecord.isEmpty) idNumber.getOrElse("") else cellNumber
        val idNr = if (eventRecord.isEmpty) Some(cellNumber) else idNumber
        val dob = LuhnToolkit.dobFromIDNumber(idNr) match {
          case None => dateOfBirth
          case Some(id) => Some(id)
        }
        currentState.updatePersonalDetails(UserPersonalDetails(firstName, Some(lastName), idNr, dob, passport))
          .addCellphoneNumber(cellNr)
      case (UserCellnumberUpdated(oldCellnumber, newCellnumber, eventRecord), currentState) =>
        currentState.updatedCellphoneNumber(newCellnumber)
      case (_: SupportStaffLoggedIn, state) => state
      case (event: UserEmailUpdated, state) =>
        state.updateLoginEmail(event.newEmail)
      case (event: UserAcceptedInvitation, state) =>
        val s1 = event.cellNumber match {
          case Some(cellNr) => state.addCellphoneNumber(cellNr)
          case None => state
        }
        s1.updatePersonalDetails(UserPersonalDetails(event.firstName, Some(event.lastName), None, None, None))
      case (UserMembershipUpdated(agencyMembership, _), currentState) =>
        currentState.addAgencyMembership(agencyMembership) // add does the same thing as update
    }
  // scalastyle:on

  private def initial = Actions()
    .onCommand[CreateUser, Done] {
      case(CreateUser(firstName, email, password), ctx, state) =>
        val hashedPassword = SecurePasswordHashing.hashPassword(password)
        val totpSecret = OtpAuthDetails.generateSecret().base32Secret
        ctx.thenPersist(UserCreated(UserCreateDetails(firstName, email, hashedPassword, totpSecret)))(_ => ctx.reply(Done))
    }
    .onCommand[CreateUserFromInvitation, Done] {
      case(CreateUserFromInvitation(invitation, password), ctx, state) =>
        val hashedPassword = SecurePasswordHashing.hashPassword(password)
        val totpSecret = OtpAuthDetails.generateSecret().base32Secret

        val role = translateAgencyMembership(invitation.agencyId, invitation.role.toString, Option.empty)

        role match {
          case Left(message) =>
            ctx.invalidCommand(message)
            ctx.done
          case Right(membership) =>
            val eventRecord = EventRecord(Some(invitation.invitorId.toString))
            ctx.thenPersistAll(
              UserCreated(UserCreateDetails(invitation.firstName, invitation.email, hashedPassword, totpSecret)),
              UserEnabled(invitation.email, "User invited", isTester = false, Option.empty, eventRecord),
              EmailValidated(invitation.email, DateUtcUtil.now()),
              UserAcceptedInvitation(invitation.agencyId, invitation.partyId, invitation.firstName, invitation.lastName,
                invitation.email, invitation.cellNumber, invitation.role.toString, eventRecord),
              UserAddedToAgency(membership, EventRecord(Some(invitation.invitorId.toString)))
            )(() => ctx.reply(Done))
        }
    }
    .onEvent {
      case (UserCreated(UserCreateDetails(firstName, email, password, totpSecret)), state) =>
        state.updateLoginDetails(UserLoginDetails(email, password), totpSecret)
          .updatePersonalDetails(UserPersonalDetails(firstName))
          .addEmailAddress(email)
          .updateMiscellaneous(Json.obj("registered" -> true))
      case (EmailValidated(email, validAt), currentState) =>
        currentState.validateEmail(email, validAt)
    }

  private def segmentsActions = Actions()
    .onCommand[AddSegment, Done] {
      case (AddSegment(agencyId, segmentId, userId), ctx, state) =>
        val membershipSegments = state.agencyMemberships.find(_.agencyId == agencyId.uuid.toString).map(_.segment.get)
        membershipSegments match {
          case None =>
            ctx.invalidCommand("Invalid agency Id")
            ctx.done
          case Some(segments) if segments.segmentMemberships.contains(segmentId.uuid) =>
            ctx.reply(Done)
            ctx.done
          case Some(segments) =>
            val segmentSeq = segments.segmentMemberships :+ segmentId.uuid
            ctx.thenPersist(SegmentsUpdated(agencyId, segmentSeq, segments.segmentMemberships, EventRecord(userId)))(_ => ctx.reply(Done))
        }
    }
    .onCommand[RemoveSegment, Done] {
      case (RemoveSegment(agencyId, segmentId, userId), ctx, state) =>
        val membershipSegments = state.agencyMemberships.find(_.agencyId == agencyId.uuid.toString).map(_.segment.get)
        membershipSegments match {
          case None =>
            ctx.invalidCommand("Invalid agency Id")
            ctx.done
          case Some(segments) if !segments.segmentMemberships.contains(segmentId.uuid) =>
            ctx.reply(Done)
            ctx.done
          case Some(segments) =>
            val segmentSeq = segments.segmentMemberships.filterNot(_ == segmentId.uuid)
            ctx.thenPersist(SegmentsUpdated(agencyId, segmentSeq, segments.segmentMemberships, EventRecord(userId)))(_ => ctx.reply(Done))
        }
    }
    .onEvent {
      case (SegmentsUpdated(agencyId, segments, _, _), state) => state.updateSegments(agencyId, segments)
    }

  override def behavior: Behavior = {
    case user if user.loginDetails.isEmpty => initial
    case user if user.loginDetails.isDefined => registered
      .orElse(agency)
      .orElse(agencyInvite)
      .orElse(verification)
      .orElse(password)
      .orElse(support)
      .orElse(miscellaneous)
      .orElse(totp)
      .orElse(segmentsActions)
  }
}

case class UserAlreadyEnabled() extends IllegalArgumentException("This user has already been enabled") with NoStackTrace
object UserAlreadyEnabled {
  implicit val format: Format[UserAlreadyEnabled] = Format(
    Reads(_ => JsSuccess(UserAlreadyEnabled())),
    Writes(_ => Json.obj("type" -> "UserAlreadyEnabled"))
  )
}

case class UserAlreadyDisabled() extends IllegalArgumentException("This user has already been disabled") with NoStackTrace
object UserAlreadyDisabled {
  implicit val format: Format[UserAlreadyDisabled] = Format(
    Reads(_ => JsSuccess(UserAlreadyDisabled())),
    Writes(_ => Json.obj("type" -> "UserAlreadyDisabled"))
  )
}

case class UserDisabledException() extends IllegalArgumentException("User is disabled") with NoStackTrace
object UserDisabledException {
  implicit val format: Format[UserDisabledException] = Format(
    Reads(_ => JsSuccess(UserDisabledException())),
    Writes(_ => Json.obj("type" -> "UserDisabledException"))
  )
}

case class UserOtpInvalid() extends IllegalArgumentException("Time based OTP is incorrect or expired.") with NoStackTrace
object UserOtpInvalid {
  implicit val format: Format[UserOtpInvalid] = Format(
    Reads(_ => JsSuccess(UserOtpInvalid())),
    Writes(_ => Json.obj("type" -> "UserOtpInvalid"))
  )
}

case class PropertyOnboardedForThisAgency() extends Exception("Property already onboarded") with NoStackTrace
object PropertyOnboardedForThisAgency {
  implicit val format: Format[PropertyOnboardedForThisAgency] = Format(
    Reads(_ => JsSuccess(PropertyOnboardedForThisAgency())),
    Writes(_ => Json.obj("type" -> "PropertyOnboardedForThisAgency"))
  )
}

case class AlreadyJoinedAgency() extends Exception("User has already joined an agency") with NoStackTrace
object AlreadyJoinedAgency {
  implicit val format: Format[AlreadyJoinedAgency] = Format(
    Reads(_ => JsSuccess(AlreadyJoinedAgency())),
    Writes(_ => Json.obj("type" -> "AlreadyJoinedAgency"))
  )
}

case class AlreadyLeftAgency() extends Exception("User has already left the agency") with NoStackTrace
object AlreadyLeftAgency {
  implicit val format: Format[AlreadyLeftAgency] = Format(
    Reads(_ => JsSuccess(AlreadyLeftAgency())),
    Writes(_ => Json.obj("type" -> "AlreadyLeftAgency"))
  )
}
