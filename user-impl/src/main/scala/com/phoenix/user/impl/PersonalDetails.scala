package com.phoenix.user.impl

import play.api.libs.json.{Format, Json}

case class PersonalDetails(details: UserPersonalDetails,
                           emailAddress: Option[String],
                           cellNumber: Option[String],
                           cellVerified: <PERSON>olean,
                           emailVerified: <PERSON>olean)

object PersonalDetails {
  implicit val format: Format[PersonalDetails] = Json.format
}
