package com.phoenix.user.impl

import com.lightbend.lagom.scaladsl.playjson.{JsonSerializer, JsonSerializerRegistry}
import com.phoenix.authentication.jwt.UserSession
import com.phoenix.util.JsonSerializerImpl.generateJsonSerializersFor

import scala.collection.immutable.Seq

object UserSerializerRegistry extends JsonSerializerRegistry {
  private def apis = {
    import com.phoenix.user.api
    generateJsonSerializersFor[api.UserEvent] ++ Seq(
      JsonSerializer[api.User],
      JsonSerializer[api.UserId],
      JsonSerializer[api.UserEmail],
      JsonSerializer[api.UserEvent]
    )
  }

  private def state = {
    import com.phoenix.persistence._
    import com.phoenix.authentication.jwt.AgencyMembership
    Seq(
      JsonSerializer[StateRecord],
      JsonSerializer[CreatedState],
      JsonSerializer[DeletedState],
      JsonSerializer[EventRecord],
      JsonSerializer[UserState],
      JsonSerializer[UserLoginDetails],
      JsonSerializer[UserCreateDetails],
      JsonSerializer[UserPersonalDetails],
      JsonSerializer[AgencyMembership],
      JsonSerializer[ReservedEmail],
      JsonSerializer[UserCellphoneNumber]
    )
  }

  private def requests = {
    import com.phoenix.user.api.request._
    Seq(
      JsonSerializer[ForgotPassword],
      JsonSerializer[InvitationReview],
      JsonSerializer[OtpRequest],
      JsonSerializer[ResetPassword],
      JsonSerializer[SwitchAgencyFields],
      JsonSerializer[UserCreation],
      JsonSerializer[UserDetailFields],
      JsonSerializer[Miscellaneous],
      JsonSerializer[UserEmail],
      JsonSerializer[UserInvitation],
      JsonSerializer[UserInvitationCreation],
      JsonSerializer[UserLogin],
      JsonSerializer[RefreshTokenFields],
      JsonSerializer[AddAgencyMember],
    )
  }

  private def responses = {
    import com.phoenix.user.api.response._
    Seq(
      JsonSerializer[AgencyUserResponse],
      JsonSerializer[ResetPasswordDone],
      JsonSerializer[TokenRefreshDone],
      JsonSerializer[UserAuthTokens],
      JsonSerializer[UserDetails],
      JsonSerializer[UserInvite],
      JsonSerializer[UserInviteId],
      JsonSerializer[UserInvitationResponse],
      JsonSerializer[UserInviteDone],
      JsonSerializer[UserPermissions],
    )
  }

  private def events = generateJsonSerializersFor[UserEvent]

  private def commands = generateJsonSerializersFor[UserCommand] ++ Seq(
    JsonSerializer[PersonalDetails],
    JsonSerializer[UserLoggedIn],
    JsonSerializer[UserSession]
  )

  private def exceptions = Seq(
    JsonSerializer[UserAlreadyEnabled],
    JsonSerializer[UserAlreadyDisabled],
    JsonSerializer[UserDisabledException],
    JsonSerializer[UserOtpInvalid],
    JsonSerializer[PropertyOnboardedForThisAgency],
    JsonSerializer[AlreadyJoinedAgency],
    JsonSerializer[AlreadyLeftAgency]
  )

  override def serializers: Seq[JsonSerializer[_]] = apis ++ state ++ requests ++ responses ++ events ++ commands ++ exceptions
}
