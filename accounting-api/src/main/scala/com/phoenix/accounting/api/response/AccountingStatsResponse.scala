package com.phoenix.accounting.api.response

import play.api.libs.json.{Format, Json}

case class AccountingStatsResponse(
                                    fundsCollectedInLast30Days: FundsCollectedInLast30Days,
                                    commissionLast30Days: CommissionLast30Days,
                                    depositsUnderManagement: DepositsUnderManagement
                                  )
object AccountingStatsResponse {
  implicit val format: Format[AccountingStatsResponse] = Json.format
}

case class FundsCollectedInLast30Days(total: BigDecimal, percentageChange30Days: BigDecimal, creditNotes: BigDecimal)
object FundsCollectedInLast30Days {
  implicit val format: Format[FundsCollectedInLast30Days] = Json.format
}

case class CommissionLast30Days(total: BigDecimal, percentageChange30Days: BigDecimal)
object CommissionLast30Days {
  implicit val format: Format[CommissionLast30Days] = Json.format
}

case class DepositsUnderManagement(total: BigDecimal)
object DepositsUnderManagement {
  implicit val format: Format[DepositsUnderManagement] = Json.format
}
