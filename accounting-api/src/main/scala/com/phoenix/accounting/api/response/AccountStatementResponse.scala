package com.phoenix.accounting.api.response

import com.phoenix.date.DateTimeJsonFormatter
import org.joda.time.DateTime
import play.api.libs.json.{Format, Json}

import java.util.UUID

case class AccountStatementResponse(accountType: String,
                                    agencyId: UUID,
                                    accountId: UUID,
                                    createdAt: DateTime,
                                    sourceId: String,
                                    sourceType: String,
                                    partyId: UUID,
                                    remittanceAt: Option[DateTime],
                                    asset: Option[String],
                                    precision: Option[Int],
                                    reference: String,
                                    debitAmount: Option[BigDecimal],
                                    creditAmount: Option[BigDecimal])

object AccountStatementResponse {
  implicit val formatDateTime: Format[DateTime] = DateTimeJsonFormatter.format
  implicit val format: Format[AccountStatementResponse] = Json.format
}
