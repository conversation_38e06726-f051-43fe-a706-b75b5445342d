#play.crypto.secret is deprecated, use play.http.secret.key instead
play.http.secret.key = ${?APPLICATION_SECRET}
play.application.loader = com.phoenix.party.impl.PartyLoader

party.cassandra.keyspace = party
jwt.issuer = party

sentry-dsn = ${?SENTRY_DSN}

akka.actor.enable-additional-serialization-bindings = on

cassandra-journal.keyspace = ${party.cassandra.keyspace}
cassandra-snapshot-store.keyspace = ${party.cassandra.keyspace}
lagom.persistence.read-side.cassandra.keyspace = ${party.cassandra.keyspace}

lagom.persistence.read-side.global-prepare-timeout = 120s
lagom.broker.kafka.client.consumer.batching-size = 1

# Enable new sharding state store mode by overriding Lagom's default
akka.cluster.sharding.state-store-mode = ddata

cassandra-journal.write-static-column-compat = off
cassandra-query-journal {
  write-static-column-compat = off
  first-time-bucket = "20190101T00:00"
}

# Enable serializers provided in Akka 2.5.8+ to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done"                 = akka-misc
  "akka.actor.Address"        = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

akka {
  actor {
    enable-additional-serialization-bindings = on

    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      jackson-cbor = "akka.serialization.jackson.JacksonCborSerializer"
    }

    serialization-bindings {
      "com.phoenix.party.impl.AgencyPartiesCommand" = jackson-json
      "com.phoenix.party.impl.TaggablePartyCommand" = jackson-json
      "com.phoenix.party.impl.AgencyPartiesEvent" = jackson-json
      "com.phoenix.party.impl.TaggablePartyEvent" = jackson-json
      "com.phoenix.party.impl.AgencyPartiesState" = jackson-json
      "com.phoenix.party.impl.TaggablePartyState" = jackson-json
    }
  }

  serialization.jackson {
    jackson-modules += "com.fasterxml.jackson.datatype.joda.JodaModule"
  }
}

google {
  places {
    server-key = "AIzaSyD0UBLjCW3A3gm273Jz7gvaulcdKbYj96M"
  }
}

lagom.circuit-breaker {
  default {
    exception-whitelist = [
      "com.lightbend.lagom.scaladsl.api.transport.NotFound"
    ]
  }
}

readSidePostgres.username=${?PG_PHOENIX_USERNAME}
readSidePostgres.password=${?PG_PHOENIX_PASSWORD}
readSidePostgres.database=${?PG_PHOENIX_DATABASE}
readSidePostgres.port=${?PG_PHOENIX_PORT}
readSidePostgres.host=${?PG_PHOENIX_HOST}
readSidePostgres.maximumMessageSize=${?PG_PHOENIX_MAXMSG}
