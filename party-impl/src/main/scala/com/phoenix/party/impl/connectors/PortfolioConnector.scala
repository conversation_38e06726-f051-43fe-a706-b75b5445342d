package com.phoenix.party.impl.connectors

import java.util.UUID
import akka.Done
import akka.actor.ActorSystem
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import io.getquill.{CassandraLagomAsyncContext, SnakeCase}

import scala.concurrent.{ExecutionContext, Future}

class PortfolioConnector(session: CassandraSession, system: ActorSystem) {
  private implicit val executionContext: ExecutionContext = system.dispatchers.lookup("lagom.persistence.dispatcher")
  val ctx = new CassandraLagomAsyncContext(SnakeCase, session)
  import ctx._

  private val portfolioAgents = quote { querySchema[PortfolioAgentRow]("portfolio_agents") }
  private val portfolioAddresses = quote { querySchema[PortfolioAddressRow]("portfolio_addresses") }
  private val portfolioTaggedParties = quote { querySchema[PortfolioTaggedPartyRow]("portfolio_tagged_parties") }

  def insert(property: PortfolioAddressRow): Future[Done] = {
    val q = quote { portfolioAddresses.insert(lift(property))}
    ctx.run(q)
  }

  def findPortfolio(portfolioId: UUID): Future[Option[PortfolioAddressRow]] = for {
    property <- ctx.run( quote { portfolioAddresses.filter(_.portfolioId == lift(portfolioId)) } )
  } yield property.headOption

  def insertPortfolioTaggedParty(portfolioId: UUID, tagged: String, partyId: UUID): Future[Done] = {
    val q = quote { portfolioTaggedParties.insert(lift(PortfolioTaggedPartyRow(portfolioId, tagged, partyId))) }
    ctx.run(q)
  }

  def findPortfolioParties(portfolioId: UUID): Future[Seq[PortfolioTaggedPartyRow]] = {
    val q = quote { portfolioTaggedParties.filter(_.portfolioId == lift(portfolioId)) }
    ctx.run(q)
  }

  def insertPortfolioAgents(agencyId: UUID, portfolioId: UUID, agents: String): Future[Done] = {
    val portfolioAgentRow = PortfolioAgentRow(agencyId, portfolioId, agents)
    val q = quote { portfolioAgents.insert(lift(portfolioAgentRow)) }
    ctx.run(q)
  }

  def findPortfolioAgents(agencyId: UUID): Future[Seq[PortfolioAgentRow]] = {
    val q = quote { portfolioAgents.filter(_.agencyId == lift(agencyId)) }
    ctx.run(q)
  }
}

case class PortfolioAgentRow(agencyId: UUID, portfolioId: UUID, agentPartyIds: String) {
  val agentParties: Seq[UUID] = agentPartyIds.split(",").map(UUID.fromString)
}
case class PortfolioAddressRow(portfolioId: UUID, propertyId: UUID, agencyId: UUID, address: String)
case class PortfolioTaggedPartyRow(portfolioId: UUID, tag: String, partyId: UUID)
