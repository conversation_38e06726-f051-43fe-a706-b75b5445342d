package com.phoenix.party.impl

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import com.phoenix.party.api.{CompanySummary_v1, Company_v1, PersonSummary_v1, Person_v1}
import com.phoenix.util.UUID

class AgencyPartiesEntity extends PersistentEntity {
  override type Command = AgencyPartiesCommand
  override type Event = AgencyPartiesEvent
  override type State = AgencyPartiesState
  private val defaultLimit = 500

  override def initialState = AgencyPartiesState()

  //scalastyle:off
  override def behavior: Behavior = {
    Actions()
      .onReadOnlyCommand[SearchParties, PaginatedParties] {
        case (SearchParties(tags, query, paginationOffset, paginationLimit), ctx, AgencyPartiesState(people, companies)) =>
          val lowerQuery = query.toLowerCase()
          val lowerTags = tags
          val offset = paginationOffset.getOrElse(0)
          val limit = paginationLimit.getOrElse(defaultLimit)

          if (offset < 0 || limit < 1) {
            ctx.invalidCommand("Invalid offset")
            ctx.reply(PaginatedParties(List(), List(), offset, limit))
          } else {
            val personSummaries = people
              .filter(p => p.mainText.toLowerCase().contains(lowerQuery))
              .filter(p => lowerTags.isEmpty || p.tags.count(tag => lowerTags.contains(tag)) > 0)
              .sortWith((lt, rt) => lt.updatedAt > rt.updatedAt)
              .slice(offset, limit + offset)

            val companySummaries = companies
              .filter(p => p.mainText.toLowerCase().contains(lowerQuery))
              //.filter(p => lowerTags.nonEmpty && p.tags.count(tag => lowerTags.contains(tag)) > 0)
              .filter(p => lowerTags.isEmpty || p.tags.count(tag => lowerTags.contains(tag)) > 0)
              .sortWith((lt, rt) => lt.updatedAt > rt.updatedAt)
              .slice(offset, limit + offset)

            ctx.reply(PaginatedParties(personSummaries, companySummaries, offset, limit))
          }
      }
      .onEvent {
        case (AgencyPersonAssigned_v1(person), state) =>
          val people = state.people.filterNot(_.id == person.id.get)
          val firstName = person.firstName.getOrElse("")
          val lastName = person.lastName.getOrElse("")
          val accounts = person.accounts.toList
          val description = s"$firstName $lastName"
          state.copy(people = people :+ PersonSummary_v1(person.id.get, person.tags, accounts, description, person.emailAddress, person.updatedAt.get))
        case (AgencyCompanyAssigned_v1(company), state) =>
          val companies = state.companies.filterNot(_.id == company.id.get)
          val description = company.tradingAs.getOrElse("") match {
            case name if name.isEmpty => company.companyName.getOrElse(company.emailAddress)
            case name => name
          }
          state.copy(companies = companies :+ CompanySummary_v1(company.id.get, company.tags, company.accounts.toList, description, company.emailAddress, company.updatedAt.getOrElse(company.createdAt.get)))
      }
  }
}
