package com.phoenix.party.impl

import com.lightbend.lagom.scaladsl.playjson.{JsonSerializer, JsonSerializerRegistry}
import com.phoenix.util.JsonSerializerImpl.generateJsonSerializersFor

import scala.collection.immutable.Seq

object PartySerializerRegistry extends JsonSerializerRegistry {
  private def api = {
    import com.phoenix.party.api._
    Seq(
      JsonSerializer[Tenancy],
      JsonSerializer[TaggableParty],
      JsonSerializer[Person_v1],
      JsonSerializer[Company_v1],
      JsonSerializer[PersonSummary_v1],
      JsonSerializer[CompanySummary_v1],
      JsonSerializer[User_v1],
      JsonSerializer[PartyTag],
      JsonSerializer[AccountType],
      JsonSerializer[Account]
    ) ++ generateJsonSerializersFor[PartyEvent]
  }

  private def state = {
    import com.phoenix.persistence._
    Seq(
      JsonSerializer[StateRecord],
      JsonSerializer[CreatedState],
      JsonSerializer[DeletedState],
      <PERSON>sonSerializer[EventRecord],
      JsonSerializer[AgencyPartiesState],
      JsonSerializer[TaggablePartyState],
    )
  }

  private def responses = {
    import com.phoenix.party.api._
    Seq(
      JsonSerializer[PartyTagSummary],
    ) ++ generateJsonSerializersFor[Response]
  }

  private def events =
    generateJsonSerializersFor[TaggablePartyEvent] ++ generateJsonSerializersFor[AgencyPartiesEvent]

  private def commands = Seq(
    JsonSerializer[PaginatedParties],
  ) ++ generateJsonSerializersFor[AgencyPartiesCommand] ++ generateJsonSerializersFor[TaggablePartyCommand]

  private def exceptions = Seq(JsonSerializer[PartyTagExists], JsonSerializer[PartyLeaseNotFound])

  override def serializers: Seq[JsonSerializer[_]] = api ++ state ++ responses ++ events ++ commands ++ exceptions
}
