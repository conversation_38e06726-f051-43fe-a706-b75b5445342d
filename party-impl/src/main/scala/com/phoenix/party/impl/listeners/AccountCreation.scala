package com.phoenix.party.impl.listeners

import java.util.UUID

import com.phoenix.party.impl.connectors.{PaymentReferenceConnector, PortfolioAddressRow, PortfolioConnector}
import com.phoenix.util.BlueLuhn

import scala.concurrent.{ExecutionContext, Future}

trait AccountCreation {
  val paymentReferenceConnector: PaymentReferenceConnector
  val portfolioConnector: PortfolioConnector
  implicit val ec: ExecutionContext

  protected def findPortfolioAddressRow(portfolioId: UUID): Future[PortfolioAddressRow] = {
    portfolioConnector.findPortfolio(portfolioId).map {
      case Some(account) => account
      case None => throw new Exception(s"Property not found for portfolio $portfolioId")
    }
  }

  protected def findPropertyAddress(portfolioId: UUID): Future[String] = findPortfolioAddressRow(portfolioId).map(_.address)

  protected def createUniqueReference: Future[String] = {
    // recursively check if pmt ref exists
    val numberSize = 10
    val pmtReference = BlueLuhn.generate(numberSize)
    for {
      // might be a fancier cassandra query to perform here
      reference <- paymentReferenceConnector.getPaymentReference(pmtReference)
      pmtRef <- reference match {
        case None => Future(pmtReference)
        case Some(_) => createUniqueReference
      }
    } yield pmtRef
  }
}
