package com.phoenix.party.impl.connectors

import akka.Done
import com.typesafe.config.ConfigFactory
import org.flywaydb.core.Flyway

object PostgresReadsideDatabase {

  def migrate(): Done = {

    val config = ConfigFactory.load("application.conf")
    val username = config.getString("readSidePostgres.username")
    val password = config.getString("readSidePostgres.password")
    val database = config.getString("readSidePostgres.database")
    val port = config.getString("readSidePostgres.port")
    val host = config.getString("readSidePostgres.host")
    val url = s"***************************************"

    Flyway.configure()
      .dataSource(url, username, password)
      .defaultSchema("party") // all db objects will reside in the schema for this service
      .locations("classpath:migrations") // set the location of migration files
      .baselineOnMigrate(true) // baseline the schema if it doesn't exist
      .load() // build the configuration object
      .migrate()

    Done
  }
}
