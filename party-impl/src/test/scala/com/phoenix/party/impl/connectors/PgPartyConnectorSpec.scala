package com.phoenix.party.impl.connectors

import akka.Done
import com.lightbend.lagom.scaladsl.testkit.ServiceTest
import com.phoenix.party.api._
import com.phoenix.party.impl.BaseSpec
import com.phoenix.util
import org.joda.time.DateTime
import org.scalatest.OptionValues

import java.util.UUID

class PgPartyConnectorSpec extends BaseSpec with OptionValues {
  override val serviceSetup: ServiceTest.Setup = ServiceTest.defaultSetup.withCluster(true)
  protected val partyId = UUID.randomUUID()
  protected val companyId = UUID.randomUUID()
  protected val address = "15 Dolphin Drive, Table View, Cape Town"
  protected val address2 = "58 Milnerton Drive, Milnerton Ridge, Cape Town"


  protected val person: Person_v1 = Person_v1(
    Some(partyId.toString),
    Some(util.UUID.toString(agencyId)),
    List(),
    Some("Anne"),
    Some("Chovey"),
    Some("*************"),
    Some("Passport"),
    None,
    "asd",
    None,
    None,
    bank = Some("ABSA"),
    branchCode = Some("7722"),
    accountName = Some("TEST HOLDER"),
    accountNumber = Some("********"),
    accountType = Some("Savings"),
    None,
    None,
    Seq.empty,
    None,
    Some("1994-01-01")
  )

  protected val company: Company_v1 = Company_v1(
    Some(companyId.toString),
    Some(util.UUID.toString(agencyId)),
    List(),
    Some("GoReal"),
    Some("GoReal Trading AS"),
    Some(true),
    Some("*************"),
    None,
    "email@",
    None,
    None,
    bank = Some("ABSA"),
    branchCode = Some("7722"),
    accountName = Some("TEST HOLDER"),
    accountNumber = Some("********"),
    accountType = Some("Savings"),
    contactPersonLastName = None,
    propertyId = None,
    createdAt = None,
    updatedAt = None,
    address = None,
    accounts = Seq.empty,
    agentLeaseCount = None
  )

  override def beforeAll(): Unit = {
      PostgresReadsideDatabase.migrate()
  }

  "Insert Person" should {
    "correctly insert into table" in {
      pgPartyConnector.insert(person).flatMap { x =>
          assert(x === Done)
      }
    }

    "test with correct person details" in {
      pgPartyConnector.find(person.id.get).flatMap { x =>
        assert(x.get.id === person.id)
        assert(x.get.agency === person.agency)
      }
    }

    "test update person details" in {
      val updatedPerson = person.copy(firstName = Some("UpdatedName"))
      val beforeUpdate = DateTime.now().getMillis
      pgPartyConnector.update(updatedPerson).flatMap { _ =>
        pgPartyConnector.find(updatedPerson.id.get).flatMap { x =>
          assert(x.get.id === updatedPerson.id)
          assert(x.get.firstName === updatedPerson.firstName)
          assert(x.get.updatedAt.get >= beforeUpdate)
        }
      }
    }
  }

  "Insert Company" should {
    "correctly insert into table" in {
      pgPartyConnector.insert(company).flatMap { x =>
        assert(x === Done)
      }
    }
    "test with correct company details" in {
      pgPartyConnector.find(company.id.get).flatMap { x =>
        assert(x.get.id === company.id)
        assert(x.get.agency.get === util.UUID.toString(agencyId))
      }
    }
  }

  "Update Company" should {
    "test update company details" in {
      val updatedCompany = company.copy(companyName = Some("UpdatedName"))
      val beforeUpdate = DateTime.now().getMillis
      pgPartyConnector.update(updatedCompany).flatMap { _ =>
        pgPartyConnector.find(updatedCompany.id.get).flatMap { x =>
          assert(x.get.id === updatedCompany.id)
          assert(x.get.companyName === updatedCompany.companyName)
          assert(x.get.updatedAt.get >= beforeUpdate)
        }
      }
    }
  }

  "Update Agent Lease Count" should {
    "update the agent lease count correctly" in {
      val agentLeaseCount = 5

      pgPartyConnector.updateAgentLeaseCount(util.UUID.toString(agencyId), person.id.get, agentLeaseCount).flatMap { _ =>
        pgPartyConnector.find(person.id.get).flatMap { x =>
          assert(x.get.id.get === person.id.get)
          assert(x.get.agentLeaseCount.get === agentLeaseCount)
        }
      }
    }
  }

  "Get all parties" should {
    "return all parties" in {
      pgPartyConnector.findAll().flatMap { x =>
        assert(x.nonEmpty)
      }
    }
  }

  "Get all parties by agency id" should {
    "return all parties by agency id" in {
      pgPartyConnector.findByAgencyId(agencyId).flatMap { x =>
        assert(x.nonEmpty)
      }
    }
  }
}
