package com.phoenix.party.impl

import java.util.UUID
import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.InvalidCommandException
import com.lightbend.lagom.scaladsl.testkit.{PersistentEntityTestDriver, ServiceTest}
import com.phoenix.party.api._
import com.phoenix.persistence.EventRecord
import org.scalatest.OptionValues
import play.api.libs.json.Json

class TaggablePartyEntitySpec extends BaseSpec with OptionValues {
  override val serviceSetup: ServiceTest.Setup = ServiceTest.defaultSetup.withCluster(true)
  protected val partyId = UUID.randomUUID()
  protected val leaseId = UUID.randomUUID()
  protected val propertyId = UUID.randomUUID()
  protected val accountId = UUID.randomUUID()
  protected val reference = "**********"
  protected val address = "15 Dolphin Drive, Table View, Cape Town"
  protected val address2 = "58 Milnerton Drive, Milnerton Ridge, Cape Town"

  protected val driver = new PersistentEntityTestDriver(server.application.actorSystem, new TaggablePartyEntity(), partyId.toString)
  val person2Id = UUID.randomUUID().toString
  protected val driver2 = new PersistentEntityTestDriver(server.application.actorSystem, new TaggablePartyEntity(), person2Id)

  protected val companyPartyId = UUID.randomUUID()
  protected val companyDriver = new PersistentEntityTestDriver(server.application.actorSystem, new TaggablePartyEntity(), companyPartyId.toString)


  protected val person: Person_v1 = Person_v1(
    id = None,
    agency = None,
    firstName = Some("Anne"),
    lastName = Some("Chovey"),
    idNumber = None,
    passport = None,
    taxNumber = None,
    emailAddress = "asd",
    cellNumber = None,
    telNumber = None,
    bank = Some("ABSA"),
    branchCode = Some("7722"),
    accountName = Some("TEST HOLDER"),
    accountNumber = Some("********"),
    accountType = Some("Savings"),
    createdAt = None,
    updatedAt = None,
    agentLeaseCount = None,
    dateOfBirth = Some("1994-01-01"),
  )
  
  protected val person2: Person_v1 = Person_v1(
    id = None,
    agency = None,
    firstName = Some("Anne"),
    lastName = Some("Chovey"),
    idNumber = Some("*************"),
    passport = None,
    taxNumber = None,
    emailAddress = "asd",
    cellNumber = None,
    telNumber = None,
    bank = Some("ABSA"),
    branchCode = Some("7722"),
    accountName = Some("TEST HOLDER"),
    accountNumber = Some("********"),
    accountType = Some("Savings"),
    createdAt = None,
    updatedAt = None,
    agentLeaseCount = None,
    dateOfBirth = None,
  )

  "TaggablePartyEntity entity" should {
    "fail with TaggableParty not found to GetTaggableParty" in {
      val outcome = driver.run(GetTaggableParty)
      outcome.events should have length 0
      outcome.state.taggableParty should ===(Option.empty[TaggableParty])
      outcome.replies should ===(List(InvalidCommandException(s"TaggableParty not found ${partyId.toString}")))
      outcome.issues should be(Nil)
    }
    "reply with Done to CreateTaggableParty" in {
      val outcome = driver.run(CreateTaggableParty(userId.toString, agencyId.toString, person))
      outcome.events should have length 1
      outcome.events(0) shouldBe a[TaggablePartyCreated_v1]
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty shouldBe a[Person_v1]
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Person_v1].id should ===(Some(partyId.toString))
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Person_v1].agency should ===(Some(agencyId.toString))
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Person_v1].firstName should ===(Some("Anne"))
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Person_v1].lastName should ===(Some("Chovey"))
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Person_v1].bank should ===(Some("ABSA"))
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Person_v1].branchCode should ===(Some("7722"))
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Person_v1].accountName should ===(Some("TEST HOLDER"))
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Person_v1].accountNumber should ===(Some("********"))
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Person_v1].accountType should ===(Some("Savings"))
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Person_v1].dateOfBirth should ===(Some("1994-01-01"))

      assert(outcome.state.taggableParty.isDefined)
      outcome.state.taggableParty.get shouldBe a[Person_v1]
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].id should ===(Some(partyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].agency should ===(Some(agencyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].firstName should ===(Some("Anne"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].lastName should ===(Some("Chovey"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].bank should ===(Some("ABSA"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].branchCode should ===(Some("7722"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].accountName should ===(Some("TEST HOLDER"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].accountNumber should ===(Some("********"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].accountType should ===(Some("Savings"))
      outcome.issues should be(Nil)
    }

    "confirm creation of TaggableParty 2 with DOB set from ID number" in {
      val outcome = driver2.run(CreateTaggableParty(person2Id, agencyId.toString, person2))

      assert(outcome.state.taggableParty.isDefined)
      outcome.state.taggableParty.get shouldBe a[Person_v1]
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].id should ===(Some(person2Id))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].agency should ===(Some(agencyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].dateOfBirth should ===(Some("1982-07-20"))
      outcome.issues should be(Nil)
    }

    "confirm date of birth is set" in {
      val outcome = driver2.run(GetTaggableParty)
      outcome.state.taggableParty.get shouldBe a[Person_v1]
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].dateOfBirth should ===(Some("1982-07-20"))//*************
    }

    "confirm TaggableParty updated DOB" in {
      val outcome = driver2.run(UpdateTaggableParty(person2Id, agencyId.toString, person2.copy(dateOfBirth = None)))

      assert(outcome.state.taggableParty.isDefined)
      outcome.state.taggableParty.get shouldBe a[Person_v1]
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].id should ===(Some(person2Id))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].agency should ===(Some(agencyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].dateOfBirth should ===(Some("1982-07-20"))
      outcome.issues should be(Nil)
    }

    "reply with Done to AddAccount" in {
      val outcome = driver.run(AddAccount(Account(accountId, partyId, reference, AccountType.Tenant, Some(leaseId), Some(address))))
      outcome.events should have length 1
      outcome.events(0) shouldBe a[TaggablePartyAccountAdded]
      outcome.state.accounts should have length 1
      outcome.state.accounts(0).accountId.uuid should ===(accountId)
      outcome.state.accounts(0).propertyAddress should ===(Some(address))
      outcome.state.accounts(0).portfolioId.map(_.uuid) should ===(Some(leaseId))
      outcome.state.accounts(0).partyId.uuid should ===(partyId)
      outcome.state.accounts(0).paymentReference should ===(reference)
      outcome.state.accounts(0).tag should ===(AccountType.Tenant)
      outcome.replies should ===(List(Account(accountId, partyId, reference, AccountType.Tenant, Some(leaseId), Some(address))))
      outcome.issues should be(Nil)
    }
    "fail with Account already exists to AddAccount" in {
      val outcome = driver.run(AddAccount(Account(accountId, partyId, reference, AccountType.Tenant, Some(leaseId), Some(address))))
      outcome.events should have length 0
      outcome.replies should ===(List(InvalidCommandException("Account already exists")))
      outcome.issues should be(Nil)
    }
    "fail with PartyLeaseNotFound to UpdateLeaseAddress" in {
      val wrongLeaseId = UUID.randomUUID()
      val outcome = driver.run(UpdateLeaseAddress(wrongLeaseId, address2, EventRecord(Some(userId.toString))))
      outcome.events should have length 0
      outcome.replies should ===(List(PartyLeaseNotFound(wrongLeaseId)))
      outcome.issues should be(Nil)
    }
    "reply with Done to UpdateLeaseAddress" in {
      val outcome = driver.run(UpdateLeaseAddress(leaseId, address2, EventRecord(Some(userId.toString))))
      outcome.replies should ===(List(Done))
      outcome.events should have length 1
      outcome.events(0) shouldBe a[AccountAddressUpdated]
      outcome.state.accounts should have length 1
      outcome.state.accounts(0).accountId.uuid should ===(accountId)
      outcome.state.accounts(0).propertyAddress should ===(Some(address2))
      outcome.state.accounts(0).portfolioId.map(_.uuid) should ===(Some(leaseId))
      outcome.state.accounts(0).partyId.uuid should ===(partyId)
      outcome.state.accounts(0).paymentReference should ===(reference)
      outcome.state.accounts(0).tag should ===(AccountType.Tenant)
      outcome.issues should be(Nil)
    }
    "reply with Done to UpdateTaggableParty" in {
      val outcome = driver.run(UpdateTaggableParty(userId.toString, agencyId.toString, person.copy(firstName = Some("Anne2"))))
      outcome.events should have length 1
      outcome.events(0) shouldBe a[TaggablePartyUpdated_v1]
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty shouldBe a[Person_v1]
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].id should ===(Some(partyId.toString))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].agency should ===(Some(agencyId.toString))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].firstName should ===(Some("Anne2"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].lastName should ===(Some("Chovey"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].bank should ===(Some("ABSA"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].branchCode should ===(Some("7722"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].accountName should ===(Some("TEST HOLDER"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].accountNumber should ===(Some("********"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].accountType should ===(Some("Savings"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].previousBankDetails should ===(Option.empty[PreviousBankDetails])

      assert(outcome.state.taggableParty.isDefined)
      outcome.state.taggableParty.get shouldBe a[Person_v1]
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].id should ===(Some(partyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].agency should ===(Some(agencyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].firstName should ===(Some("Anne2"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].lastName should ===(Some("Chovey"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].bank should ===(Some("ABSA"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].branchCode should ===(Some("7722"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].accountName should ===(Some("TEST HOLDER"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].accountNumber should ===(Some("********"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].accountType should ===(Some("Savings"))
      outcome.issues should be(Nil)
    }
    "reply with Done to UpdateTaggableParty (bank details)" in {
      val outcome = driver.run(UpdateTaggableParty(userId.toString, agencyId.toString, person.copy(firstName = Some("Anne2"),
        bank = Some("Capitec"), accountNumber = Some("*********"), accountName = Some("TEST 2 HOLDER"), branchCode = Some("2277"), accountType = Some("Cheque")
      )))
      outcome.events should have length 1
      outcome.events(0) shouldBe a[TaggablePartyUpdated_v1]
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty shouldBe a[Person_v1]
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].id should ===(Some(partyId.toString))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].agency should ===(Some(agencyId.toString))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].firstName should ===(Some("Anne2"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].lastName should ===(Some("Chovey"))
      assert(outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].previousBankDetails.isDefined)
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].previousBankDetails.get.bank should ===(Some("ABSA"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].previousBankDetails.get.branchCode should ===(Some("7722"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].previousBankDetails.get.accountName should ===(Some("TEST HOLDER"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].previousBankDetails.get.accountNumber should ===(Some("********"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].previousBankDetails.get.accountType should ===(Some("Savings"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].bank should ===(Some("Capitec"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].branchCode should ===(Some("2277"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].accountName should ===(Some("TEST 2 HOLDER"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].accountNumber should ===(Some("*********"))
      outcome.events(0).asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Person_v1].accountType should ===(Some("Cheque"))

      assert(outcome.state.taggableParty.isDefined)
      outcome.state.taggableParty.get shouldBe a[Person_v1]
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].id should ===(Some(partyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].agency should ===(Some(agencyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].firstName should ===(Some("Anne2"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].lastName should ===(Some("Chovey"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].bank should ===(Some("Capitec"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].branchCode should ===(Some("2277"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].accountName should ===(Some("TEST 2 HOLDER"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].accountNumber should ===(Some("*********"))
      outcome.state.taggableParty.get.asInstanceOf[Person_v1].accountType should ===(Some("Cheque"))
      outcome.issues should be(Nil)
    }
    "reply with Done to MigrateAccountReference" in {
      val sourceAccount = Account(accountId, partyId, reference, AccountType.Tenant, Some(leaseId), Some(address2))
      val newAccount = Account(accountId, partyId, reference, AccountType.Application, Some(leaseId), Some(address2))
      val outcome = driver.run(MigrateAccountReference(sourceAccount, AccountType.Application))
      outcome.replies should ===(Seq(Done))
      outcome.events should have length 2
      outcome.events(1) shouldBe a[AccountMigrated]
      val createdAccount = outcome.events(1).asInstanceOf[AccountMigrated].toAccount
      outcome.events(1).asInstanceOf[AccountMigrated].fromAccount should ===(sourceAccount)
      outcome.events(0) shouldBe a[TaggablePartyAccountAdded]
      outcome.events(0).asInstanceOf[TaggablePartyAccountAdded].account should ===(newAccount.copy(accountId = createdAccount.accountId))
      createdAccount should ===(newAccount.copy(accountId = createdAccount.accountId))
      outcome.issues should be(Nil)
    }
  }

  protected val companyAddress = PartyAddress(
    Some("15 Dolphin Drive"),
    Some("Table View"),
    Some("Cape Town"),
    Some("7441"),
    Some("South Africa"))

  protected val company: Company_v1 = Company_v1(
    None,
    None,
    List(),
    Some("Test Company"),
    Some("*************"),
    Some(false),
    None,
    None,
    "<EMAIL>",
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    None,
    Some(companyAddress),
  )

  protected val companyJson: String = """{"tradingAs":"*************","tags":[],"emailAddress":"<EMAIL>","vatRegistered":false,"companyName":"Test Company","address":{"addressLine1":"15 Dolphin Drive","addressLine2":"Table View","city":"Cape Town","province":"7441","postalCode":"South Africa","country":"ZA"},"accounts":[]}"""

  "TaggablePartyEntity company" should {
    "deserialize Company_v1" in {
      val json = Json.toJson(company).toString()
      assert(json.nonEmpty)
      assert(json.equals(companyJson))
    }

    "serialize Company_v1" in {
      val json = Json.parse(companyJson)
      assert(Json.parse(companyJson).as[Company_v1].equals(company))
    }

    "reply with Done to CreateTaggableParty with PartyAddress" in {
      val outcome = companyDriver.run(CreateTaggableParty(userId.toString, agencyId.toString, company))
      outcome.events should have length 1
      outcome.events(0) shouldBe a[TaggablePartyCreated_v1]
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty shouldBe a[Company_v1]
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Company_v1].id should ===(Some(companyPartyId.toString))
      outcome.events(0).asInstanceOf[TaggablePartyCreated_v1].taggableParty.asInstanceOf[Company_v1].agency should ===(Some(agencyId.toString))

      assert(outcome.state.taggableParty.isDefined)
      outcome.state.taggableParty.get shouldBe a[Company_v1]
      outcome.state.taggableParty.get.asInstanceOf[Company_v1].id should ===(Some(companyPartyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Company_v1].agency should ===(Some(agencyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Company_v1].address should ===(Some(companyAddress))
      outcome.issues should be(Nil)
    }

    "reply with Done to CreateTaggableParty with PartyAddress with some fields as empty strings" in {
      val addressWithSpaces = PartyAddress(
        addressLine1 = Some(""),
        addressLine2 = Some(""),
        city = Some(""),
        province = Some(""),
        postalCode = Some(""),
        country = Some("South Africa")
      )
      val outcome = companyDriver.run(UpdateTaggableParty(userId.toString, agencyId.toString, company.copy(
        address = Some(addressWithSpaces)
      )))
      outcome.events should have length 1
      outcome.events.head shouldBe a[TaggablePartyUpdated_v1]
      outcome.events.head.asInstanceOf[TaggablePartyUpdated_v1].taggableParty shouldBe a[Company_v1]
      outcome.events.head.asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Company_v1].id should ===(Some(companyPartyId.toString))
      outcome.events.head.asInstanceOf[TaggablePartyUpdated_v1].taggableParty.asInstanceOf[Company_v1].agency should ===(Some(agencyId.toString))

      assert(outcome.state.taggableParty.isDefined)
      outcome.state.taggableParty.get shouldBe a[Company_v1]
      outcome.state.taggableParty.get.asInstanceOf[Company_v1].id should ===(Some(companyPartyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Company_v1].agency should ===(Some(agencyId.toString))
      outcome.state.taggableParty.get.asInstanceOf[Company_v1].address should ===(Some(addressWithSpaces))
      outcome.issues should be(Nil)
    }
  }
}
